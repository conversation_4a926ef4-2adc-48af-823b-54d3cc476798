# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is a Keycloak migration tool that manages Keycloak configuration declaratively. It's a job container (not a long-running service) built with Micronaut and Kotlin that runs migrations to configure Keycloak realms, clients, roles, and authentication flows.

## Key Architecture

- **Main Service**: Located in `service/` directory, contains the Micronaut application
- **Configuration Files**:
  - `keycloak/configuration.yaml` - Main Keycloak configuration (realms, clients, roles)
  - `keycloak/authentication-flows.yaml` - Authentication flow definitions
  - `service/src/main/resources/application.yml` - Application configuration with environment-specific values
- **Service Structure**: Standard Micronaut application with services for different Keycloak entities (RealmService, ClientService, RoleService, etc.)

## Common Commands

### Build and Test
```bash
# Build the project
./gradlew build

# Run tests
./gradlew test

# Run specific test
./gradlew test --tests "TestClassName"
```

### Docker
```bash
# Build Docker image (referenced in Dockerfile)
docker build -t keycloak-migration .
```

## Configuration Management

### Main Configuration Structure
- All Keycloak config is defined in `keycloak/configuration.yaml`
- Environment-specific values use `-required` postfix and are resolved in `application.yml`
- Authentication flows are defined separately in `keycloak/authentication-flows.yaml`

### Environment-Specific Properties
Properties ending with `-required` in configuration.yaml must have corresponding values in application.yml:
- `root-url-required`
- `base-url-required`
- `redirect-uris-required`
- `web-origins-required`
- `admin-url-required`
- `master-saml-processing-url-required`
- `authentication-flow-required`

### Naming Conventions
- Frontend clients: `frontend-*` (e.g., `frontend-pbk-backoffice`)
- Service clients: `service-*` (e.g., `service-pid-core`)
- Role holders: `world-*` (e.g., `world-bank`)
- Auth manager roles: `<PERMISSION-FOR-ACTION>_<AUTH-MANAGER-PREFIX>`

## Key Restrictions
- Description is always required for all entities
- Use client roles only, not general realm roles
- Client roles are never removed (only added)

## CI/CD Integration
- Uses GitLab CI with shared components from banka/shared/cicd
- Helm charts generated and pushed to OCI registry
- Deploys via ArgoCD to keycloak namespace