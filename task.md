

## Detailní popis úkolu: Keycloak-migration špatně vytváří nastavení v realm

### Shrnutí problému
Služba **keycloak-migration** nesprávně nastavuje konfiguraci při vytváření nového realmu v Keycloaku. Problém se objevil **po upgradu na novější verzi <PERSON> (v26)**, kdy se změnily výchozí hodnoty některých nastavení a proběhly databázové migrace.

### Identifikované problémy

#### 1. Problém s Unmanaged Attributes
- **Stav:** Ve výchozím nastavení je "Unmanaged Attributes" nastaveno jako `Disabled` pro nový realm
- **Důsledek:** Zabraňuje načítání user attributes, zejména kritického atributu **PID UID**
- **Lokalizace:** Realm settings → General settings
- **<PERSON><PERSON><PERSON><PERSON><PERSON> chování:** Keycloak Migration vytváří realm s `Disabled` namísto `Enabled`
- **Specifika:** Custom atributy se ukládají, ale nezobrazují/nenačítají se správně

#### 2. Problém s povinným email polem v User Profile
- **Stav:** Pole email je nastaveno jako:
  - **Required field**: On
  - **Required when**: Always
- **Důsledek:** Uživatelé bez emailu (zejména bankovní uživatelé) se nemohou přihlásit
- **Error:** `{"error":"invalid_grant","error_description":"Account is not fully set up"}`
- **HTTP Status:** 400 Bad Request při pokusu o získání tokenu
- **Lokalizace:** Realm settings → User profile → email attribute
- **Požadované řešení:** Změnit z `Always` na `Scopes are requested`

### Ovlivněné služby
1. **keycloak-migration** - vytváří špatnou konfiguraci realmu
2. **keycloak-user-manager** - vytváří uživatele, kteří se pak nemohou přihlásit

### TODO úkoly

#### 1. Upravit keycloak-migration
- Nastavit správně `Unmanaged Attributes: Enabled`
- Konfigurovat email field jako `Required when: Scopes are requested`
- Implementovat per-realm konfiguraci v `configuration.yaml`
- Defaultní hodnoty nemusí být specifikovány

#### 2. Zprovoznit token testy v keycloak-user-manager
- Opravit failující test `TokenApiAdapterTest`
- Test již existuje jako disabled kvůli tomuto problému
- Zajistit, že Token API Adapter funguje správně

#### 3. Dopsat testy na user-attributes
- Přidat testy do `Users API Adapter test`
- Validovat, že atributy se správně ukládají a načítají
- **Konkrétně ověřit:** že `PID_UID` atribut je správně nastavován
- Přidat asserty na ověření existence atributů po vytvoření uživatele
- **Zajistit, aby selhání atributů nefailovalo "silently"**

#### 4. Ověřit migrace a upgrade schopnost
- Otestovat, že keycloak-migration dokáže úspěšně přežehlit KC po předchozí migraci
- **Vytvořit test:** který nejprve nasetuje Keycloak starší verzí migration a pak aplikuje novou verzi
- Zajistit, že migrace funguje jak na prázdném, tak na již nakonfigurovaném Keycloaku
- Otestovat proti existujícím datům, ne pouze proti prázdnému Keycloaku

### Technické detaily

#### Struktura konfigurace
- **Hlavní konfigurace:** `configuration.yaml`
- **Runtime konfigurace:** oddělené konfigurační soubory pro jednotlivá prostředí
- Nastavení by měla být definována per-realm
- Defaultní hodnoty se nemusí explicitně specifikovat

#### Keycloak Migration charakteristika
- **Migration je aditivní** - přepisuje konfiguraci, ale nemění existující uživatelská data
- Problém vznikl změnou defaultních hodnot v Keycloak v26
- V existujících prostředích byla konfigurace ručně opravena

#### Aktuální stav testů
- `TokenApiAdapterTest` je momentálně **disabled** kvůli tomuto problému
- `Users API Adapter test` potřebuje rozšíření o validaci attributes
- Existují container testy pro migration, ale nekryjí tento specifický scénář
- Potřeba vytvořit **full migration test** pro celou migraci

### Kritičnost úkolu
Úkol má **vysokou prioritu**, protože:
- Blokuje správnou funkčnost autentifikace uživatelů bez emailové adresy
- Je kritické v bankovním prostředí, kde mnoho uživatelů nemá email
- Způsobuje selhání custom atributů, které jsou pro systém důležité
- Failující testy brání dalšímu vývoji

### Poznámky z implementace
- V prostředích je konfigurace aktuálně správná (byla ručně opravena)
- Nové migrace však stále vytváří špatnou konfiguraci
- Je potřeba zajistit, aby oprava fungovala pro budoucí deploymenty i upgrady existujících systémů