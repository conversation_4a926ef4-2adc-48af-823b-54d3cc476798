image:
  repository: <<image>>
  tag: <<tag>>
  pullPolicy: Always
  imagePullSecret: harbor

nameOverride: ""
fullnameOverride: ""

app:
  data:
    SERVER_PORT: 8083

    # Zipkin
    ZIPKIN_URL: http://jaeger-dev-oci-collector.surveillance:9411

    # Logging
    CONSOLE_JSON_ENABLED: true

    # Keycloak admin
    KEYCLOAK_ADMIN_ENDPOINT: http://host.docker.internal:8080/auth/realms/master/protocol/openid-connect/token
    KEYCLOAK_ADMIN_CLIENT_ID: admin-cli
    KEYCLOAK_ADMIN_USERNAME: master-keycloak-migration
    # KEYCLOAK_ADMIN_PASSWORD: secret
    KE<PERSON><PERSON>OAK_ADMIN_TOKEN_RENEWAL_INTERVAL: 55s
    K<PERSON><PERSON><PERSON>OAK_CONFIGURATION_FILE: keycloak/configuration.yaml

    # Clients settings
    WEB_ORIGINS_FRONTEND_APS: 'https://aps-frontend.oci.dev.pbk-lab.tech | https://aps-frontend.oci.dev.pbk-lab.tech/*'
    WEB_ORIGINS_FRONTEND_LOAN_APPLICATION_PFS: ''
    WEB_ORIGINS_FRONTEND_LOAN_APPLICATION: ''
    REDIRECT_URIS_FRONTEND_LOAN_APPLICATION: ''
    WEB_ORIGINS_FRONTEND_LOAN_PROCESSES: ''
    REDIRECT_URIS_FRONTEND_LOAN_PROCESSES: ''
    WEB_ORIGINS_FRONTEND_LOAN_TEMPLATING: ''
    REDIRECT_URIS_FRONTEND_LOAN_TEMPLATING: ''
    WEB_ORIGINS_FRONTEND_BANK_ONBOARDING: 'http://localhost:3000 | http://localhost:3000/* | https://onboarding.oci.dev.pbk-lab.tech | https://onboarding.oci.dev.pbk-lab.tech/*'
    WEB_ORIGINS_FRONTEND_BOA: 'http://localhost:8080 | http://localhost:8080/*'
    REDIRECT_URIS_FRONTEND_BOA: ''
    WEB_ORIGINS_FRONTEND_BOA_PFS: 'http://localhost:8080 | http://localhost:8080/*'
    REALM_PID_FRONTEND_URL: ""
    REALM_ONBOARD_FRONTEND_URL: ""
    REALM_PFS_FRONTEND_URL: ""
    REALM_SERVICES_FRONTEND_URL: ""
    REALM_GEPARD_FRONTEND_URL: ""
    REDIRECT_URIS_FRONTEND_KNBOX: ""
    REDIRECT_LOGOUT_URIS_FRONTEND_KNBOX: ""
    ROOT_URL_FRONTEND_KNBOX: ""
    REDIRECT_URIS_FRONTEND_PAF: ""
    ROOT_URL_FRONTEND_PAF: ""
    WEB_ORIGINS_FRONTEND_PAF: ""
    REDIRECT_LOGOUT_URIS_FRONTEND_PAF: ""
    REDIRECT_URIS_FRONTEND_AML_PLUGIN: ""
    WEB_ORIGINS_FRONTEND_AML_PLUGIN: ""
    REDIRECT_URIS_FRONTEND_FORECLOSURES: "http://localhost:9990/login"
    WEB_ORIGINS_FRONTEND_FORECLOSURES: "http://localhost:8082 | http://localhost:9990"
    REDIRECT_URIS_COMPONENT_AML_SERVICES: "http://localhost:9990/*"
    REDIRECT_URIS_COMPONENT_AML_ELS: "http://localhost:9990/*"
    REDIRECT_URIS_COMPONENT_AML_SL_READER: "http://localhost:9990/*"
    REDIRECT_URIS_COMPONENT_AML_CAMUNDA: "http://localhost:9990/*"
    AUTH_FLOW_TPP_FEDAUTH_TEST: "PIDFederatedAuthentication"
    WEB_ORIGINS_FRONTEND_LOAN_TILES: ""
    REDIRECT_URIS_FRONTEND_LOAN_TILES: ""
    WEB_ORIGINS_FRONTEND_LOAN_TILES_GEPARD: ""
    REDIRECT_URIS_FRONTEND_LOAN_TILES_GEPARD: ""

    # Clients
    CLIENTS_ORG_KEYCLOAK_CLIENT_URL: http://host.docker.internal:8080
    CLIENTS_ORG_KEYCLOAK_CLIENT_PATH: /admin/realms
    CLIENTS_PID_FED_AUTHENTICATION: http://pid-fed-authentication.pid.svc:8083

    CONFIG_FED_AUTH_LOGIN_PATH: pid-fed-authentication-publicapi/v1/login/init
    CONFIG_FED_AUTH_VERIFY_PATH: keycloak/v1/verify

    # offline session configuration for Bank iD clients
    REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION: 365d
    REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX: 365d
    REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED: true

    PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION: 180d
    PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX: 180d

extraEnv:
  - name: CLIENT_SECRETS_COMPONENT_AML_CAMUNDA
    valueFrom:
      secretKeyRef:
        name: aml-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_AML_CAMUNDA
  - name: CLIENT_SECRETS_COMPONENT_AML_ELS
    valueFrom:
      secretKeyRef:
        name: aml-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_AML_ELS
  - name: CLIENT_SECRETS_COMPONENT_AML_SERVICES
    valueFrom:
      secretKeyRef:
        name: aml-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_AML_SERVICES
  - name: CLIENT_SECRETS_COMPONENT_AML_SL_READER
    valueFrom:
      secretKeyRef:
        name: aml-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_AML_SL_READER
  - name: CLIENT_SECRETS_SERVICE_AML_PLUGIN_SERVICE
    valueFrom:
      secretKeyRef:
        name: aml-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_AML_PLUGIN_SERVICE
  - name: CLIENT_SECRETS_COMPONENT_DAKTELA
    valueFrom:
      secretKeyRef:
        name: notification-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_DAKTELA
  - name: CLIENT_SECRETS_COMPONENT_DECISION_ENGINE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_DECISION_ENGINE
  - name: CLIENT_SECRETS_COMPONENT_DECISION_ENGINE_AML
    valueFrom:
      secretKeyRef:
        name: tarandm-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_DECISION_ENGINE_AML
  - name: CLIENT_SECRETS_COMPONENT_EXPONEA
    valueFrom:
      secretKeyRef:
        name: notification-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_EXPONEA
  - name: CLIENT_SECRETS_COMPONENT_KNBOX
    valueFrom:
      secretKeyRef:
        name: knbox-keycloak-client-secrets
        key: CLIENT_SECRETS_COMPONENT_KNBOX
  - name: CLIENT_SECRETS_DEPARTMENT_MOJE_PARTNERS_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_DEPARTMENT_MOJE_PARTNERS_CLIENT
  - name: CLIENT_SECRETS_DEPARTMENT_PIS_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_DEPARTMENT_PIS_CLIENT
  - name: CLIENT_SECRETS_FRONTEND_KNBOX
    valueFrom:
      secretKeyRef:
        name: knbox-keycloak-client-secrets
        key: CLIENT_SECRETS_FRONTEND_KNBOX
  - name: CLIENT_SECRETS_SERVICE_PAF
    valueFrom:
      secretKeyRef:
        name: paf-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PAF
  - name: CLIENT_SECRETS_SERVICE_PID_PAF
    valueFrom:
      secretKeyRef:
        name: paf-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_PAF
  - name: CLIENT_SECRETS_FRONTEND_LOAN_APPLICATION
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_FRONTEND_LOAN_APPLICATION
  - name: CLIENT_SECRETS_FRONTEND_LOAN_PROCESSES
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_FRONTEND_LOAN_PROCESSES
  - name: CLIENT_SECRETS_FRONTEND_TREASURY
    valueFrom:
      secretKeyRef:
        name: treasury-keycloak-client-secrets
        key: CLIENT_SECRETS_FRONTEND_TREASURY
  - name: CLIENT_SECRETS_MASTER_IDP_APIM_ORCHESTRATOR
    valueFrom:
      secretKeyRef:
        name: shared-services-keycloak-client-secrets
        key: CLIENT_SECRETS_MASTER_IDP_APIM_ORCHESTRATOR
  - name: CLIENT_SECRETS_MASTER_KEYCLOAK_USER_MANAGER
    valueFrom:
      secretKeyRef:
        name: keycloak-keycloak-client-secrets
        key: CLIENT_SECRETS_MASTER_KEYCLOAK_USER_MANAGER
  - name: CLIENT_SECRETS_PSD2_TPP_DUMMY
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_DUMMY
  - name: CLIENT_SECRETS_PSD2_TPP_FINBRICKS
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_FINBRICKS
  - name: CLIENT_SECRETS_PSD2_TPP_PARTNERS_BANKA
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_PARTNERS_BANKA
  - name: CLIENT_SECRETS_PSD2_TPP_GOPAY
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_GOPAY
  - name: CLIENT_SECRETS_PSD2_TPP_PAYU
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_PAYU
  - name: CLIENT_SECRETS_PSD2_TPP_TRINITY_BANK
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_TRINITY_BANK
  - name: CLIENT_SECRETS_PSD2_TPP_INBANK
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_INBANK
  - name: CLIENT_SECRETS_PSD2_TPP_BANK_TRANSFER_WORLDLINE
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_BANK_TRANSFER_WORLDLINE
  - name: CLIENT_SECRETS_PSD2_TPP_CONNECT_PAY_UAB
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_CONNECT_PAY_UAB
  - name: CLIENT_SECRETS_PSD2_TPP_EVERIFIN
    valueFrom:
      secretKeyRef:
        name: psd2-tpp-secrets
        key: CLIENT_SECRETS_PSD2_TPP_EVERIFIN
  - name: CLIENT_SECRETS_SERVICE_APIGW_OPERATOR_SYNCHRONOUS_API
    valueFrom:
      secretKeyRef:
        name: api-gw-operator-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APIGW_OPERATOR_SYNCHRONOUS_API
  - name: CLIENT_SECRETS_SERVICE_WSO2
    valueFrom:
      secretKeyRef:
        name: api-gw-operator-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_WSO2
  - name: CLIENT_SECRETS_SERVICE_APS_APPLICATION
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_APPLICATION
  - name: CLIENT_SECRETS_SERVICE_APS_BFF
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_BFF
  - name: CLIENT_SECRETS_SERVICE_APS_DOCUMENT
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_DOCUMENT
  - name: CLIENT_SECRETS_SERVICE_APS_EXCEPTION
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_EXCEPTION
  - name: CLIENT_SECRETS_SERVICE_APS_GATE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_GATE
  - name: CLIENT_SECRETS_SERVICE_APS_LOAN
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_LOAN
  - name: CLIENT_SECRETS_SERVICE_APS_MODELING
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_MODELING
  - name: CLIENT_SECRETS_SERVICE_APS_ORIGINATION
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_ORIGINATION
  - name: CLIENT_SECRETS_SERVICE_APS_PARAMETERS
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_PARAMETERS
  - name: CLIENT_SECRETS_SERVICE_APS_PEPZ
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_PEPZ
  - name: CLIENT_SECRETS_SERVICE_APS_PROCESSES
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_PROCESSES
  - name: CLIENT_SECRETS_SERVICE_APS_VALUERS
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_APS_VALUERS
  - name: CLIENT_SECRETS_SERVICE_BANK_ACCOUNTS_REGISTRY
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_ACCOUNTS_REGISTRY
  - name: CLIENT_SECRETS_SERVICE_BANK_BACKOFFICE_GATEWAY
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_BACKOFFICE_GATEWAY
  - name: CLIENT_SECRETS_SERVICE_BANK_CAMPAIGN
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CAMPAIGN
  - name: CLIENT_SECRETS_SERVICE_BANK_CARD_MANAGEMENT
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CARD_MANAGEMENT
  - name: CLIENT_SECRETS_SERVICE_BANK_CARD_SAVINGS
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CARD_SAVINGS
  - name: CLIENT_SECRETS_SERVICE_BANK_CLICK_TO_PAY
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CLICK_TO_PAY

  - name: CLIENT_SECRETS_SERVICE_BANK_CLIENT
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CLIENT

  - name: CLIENT_SECRETS_SERVICE_BANK_CLIENT_DOCUMENTS
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CLIENT_DOCUMENTS

  - name: CLIENT_SECRETS_SERVICE_BANK_CODELISTS
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CODELISTS

  - name: CLIENT_SECRETS_SERVICE_BANK_COOPERATION_SERVICE
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_COOPERATION_SERVICE

  - name: CLIENT_SECRETS_SERVICE_BANK_DAILY_AUTH_MANAGER
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_DAILY_AUTH_MANAGER

  - name: CLIENT_SECRETS_SERVICE_BANK_DASHBOARD
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_DASHBOARD

  - name: CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING

  - name: CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING_GATEWAY
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING_GATEWAY

  - name: CLIENT_SECRETS_SERVICE_BANK_CHALLENGES
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CHALLENGES

  - name: CLIENT_SECRETS_SERVICE_BANK_CHARGEBACK
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_CHARGEBACK

  - name: CLIENT_SECRETS_SERVICE_BANK_JIRA_ADAPTER
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_JIRA_ADAPTER

  - name: CLIENT_SECRETS_SERVICE_BANK_MOBILITY
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_MOBILITY

  - name: CLIENT_SECRETS_SERVICE_BANK_PAYMENT
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_PAYMENT

  - name: CLIENT_SECRETS_SERVICE_BANK_PFS_INTEGRATION
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_PFS_INTEGRATION

  - name: CLIENT_SECRETS_SERVICE_BANK_SERVICE_GATEWAY
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_SERVICE_GATEWAY

  - name: CLIENT_SECRETS_SERVICE_BANK_STANDING_ORDER
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_STANDING_ORDER

  - name: CLIENT_SECRETS_SERVICE_BANK_TRANSACTION_NOTIFICATION
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_TRANSACTION_NOTIFICATION

  - name: CLIENT_SECRETS_SERVICE_BANK_TREASURY_SERVICE
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_TREASURY_SERVICE

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_SK_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_SK_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT
    valueFrom:
      secretKeyRef:
        name: pfs-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_PAYMENT_CLIENT
    valueFrom:
      secretKeyRef:
        name: pfs-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_PAYMENT_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_SK_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_SK_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_OCP_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_OCP_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_SK_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_SK_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_TEST_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_TEST_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_RENTEA_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_RENTEA_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_SIMPLEA_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_SIMPLEA_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_GEPARD_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_GEPARD_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_VUB_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_VUB_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_WPB_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_WPB_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_ODOO_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_ODOO_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_BANK_EMPLOYEE_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_BANK_EMPLOYEE_CLIENT

  - name: CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CONSULTANT_CLIENT
    valueFrom:
      secretKeyRef:
        name: external-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CONSULTANT_CLIENT

  - name: CLIENT_SECRETS_SERVICE_IDP_APIM_ORCHESTRATOR
    valueFrom:
      secretKeyRef:
        name: shared-services-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_IDP_APIM_ORCHESTRATOR

  - name: CLIENT_SECRETS_SERVICE_KEYCLOAK_USER_MANAGER
    valueFrom:
      secretKeyRef:
        name: keycloak-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_KEYCLOAK_USER_MANAGER

  - name: CLIENT_SECRETS_SERVICE_LOAN_ACCOUNT_REGISTRY
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_ACCOUNT_REGISTRY

  - name: CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_SECURED
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_SECURED

  - name: CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_UNSECURED
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_UNSECURED

  - name: CLIENT_SECRETS_SERVICE_LOAN_BUSINESS_CODEGEN
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_BUSINESS_CODEGEN

  - name: CLIENT_SECRETS_SERVICE_LOAN_CODELISTS_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_CODELISTS_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_CONTRACTING
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_CONTRACTING

  - name: CLIENT_SECRETS_SERVICE_LOAN_CORE_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_CORE_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_CUSTOMER_GATEWAY
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_CUSTOMER_GATEWAY

  - name: CLIENT_SECRETS_SERVICE_LOAN_DE_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_DE_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_DOCUMENTS_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_DOCUMENTS_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_KNBOX_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_KNBOX_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_MODELING_SECURED
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_MODELING_SECURED

  - name: CLIENT_SECRETS_SERVICE_LOAN_NOTIFICATION_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_NOTIFICATION_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_PFS_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_PFS_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_PID_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_PID_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_PRODUCT_CATALOG_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_PRODUCT_CATALOG_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_SIGN_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_SIGN_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_LOAN_TASK_QUEUE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_TASK_QUEUE

  - name: CLIENT_SECRETS_SERVICE_LOAN_WHISPER_BRIDGE
    valueFrom:
      secretKeyRef:
        name: loan-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_LOAN_WHISPER_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_NEBANKA_BACKOFFICE_BE
    valueFrom:
      secretKeyRef:
        name: nebanka-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_NEBANKA_BACKOFFICE_BE

  - name: CLIENT_SECRETS_SERVICE_NOTIFICATION_DAKTELA_BRIDGE
    valueFrom:
      secretKeyRef:
        name: notification-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_NOTIFICATION_DAKTELA_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_NOTIFICATION_EXPONEA_BRIDGE
    valueFrom:
      secretKeyRef:
        name: notification-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_NOTIFICATION_EXPONEA_BRIDGE

  - name: CLIENT_SECRETS_SERVICE_NOTIFICATION_GATEWAY
    valueFrom:
      secretKeyRef:
        name: notification-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_NOTIFICATION_GATEWAY

  - name: CLIENT_SECRETS_SERVICE_NOTIFICATION_PUSH_SERVICE
    valueFrom:
      secretKeyRef:
        name: notification-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_NOTIFICATION_PUSH_SERVICE

  - name: CLIENT_SECRETS_SERVICE_NOTIFICATION_SMSGATE_TMOBILE
    valueFrom:
      secretKeyRef:
        name: notification-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_NOTIFICATION_SMSGATE_TMOBILE

  - name: CLIENT_SECRETS_SERVICE_PFS_ACCEPTANCE
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PFS_ACCEPTANCE

  - name: CLIENT_SECRETS_SERVICE_PFS_ADVISOR_DEFINITION
    valueFrom:
      secretKeyRef:
        name: pfs-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PFS_ADVISOR_DEFINITION

  - name: CLIENT_SECRETS_SERVICE_PFS_AUTH_MANAGER
    valueFrom:
      secretKeyRef:
        name: pfs-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PFS_AUTH_MANAGER

  - name: CLIENT_SECRETS_SERVICE_PFS_CONTRACT_SHARING
    valueFrom:
      secretKeyRef:
        name: pfs-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PFS_CONTRACT_SHARING

  - name: CLIENT_SECRETS_SERVICE_PFS_INSURANCE_COMPARATOR
    valueFrom:
      secretKeyRef:
        name: pfs-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PFS_INSURANCE_COMPARATOR

  - name: CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO

  - name: CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO_AUTH_MANAGER
    valueFrom:
      secretKeyRef:
        name: pfs-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO_AUTH_MANAGER

  - name: CLIENT_SECRETS_SERVICE_NEBANKA_OCP
    valueFrom:
      secretKeyRef:
        name: nebanka-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_NEBANKA_OCP

  - name: CLIENT_SECRETS_SERVICE_PID_ACTIVATION_BACKOFFICE_GATEWAY
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_ACTIVATION_BACKOFFICE_GATEWAY

  - name: CLIENT_SECRETS_SERVICE_PID_ACTIVATION_SERVICE
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_ACTIVATION_SERVICE

  - name: CLIENT_SECRETS_SERVICE_PID_AML
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_AML

  - name: CLIENT_SECRETS_SERVICE_PID_AUTH_MANAGER
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_AUTH_MANAGER

  - name: CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_EVENTS
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_EVENTS

  - name: CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_GATEWAY
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_GATEWAY

  - name: CLIENT_SECRETS_SERVICE_PID_BANKID_DATA
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_BANKID_DATA

  - name: CLIENT_SECRETS_SERVICE_PID_BANKID_OIDC_CONNECTOR
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_BANKID_OIDC_CONNECTOR

  - name: CLIENT_SECRETS_SERVICE_PID_BPM
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_BPM

  - name: CLIENT_SECRETS_SERVICE_PID_CORE
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_CORE

  - name: CLIENT_SECRETS_SERVICE_PID_EMAIL
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_EMAIL

  - name: CLIENT_SECRETS_SERVICE_PID_FED_AUTHENTICATION
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_FED_AUTHENTICATION

  - name: CLIENT_SECRETS_SERVICE_PID_GATEWAY
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_GATEWAY

  - name: CLIENT_SECRETS_SERVICE_PID_MOBILE_GATEWAY
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_MOBILE_GATEWAY

  - name: CLIENT_SECRETS_SERVICE_PID_NOTIFICATION_SERVICE
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_NOTIFICATION_SERVICE

  - name: CLIENT_SECRETS_SERVICE_PID_ONBOARD_AUTH_MANAGER
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_ONBOARD_AUTH_MANAGER

  - name: CLIENT_SECRETS_SERVICE_PID_ONBOARDING
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_ONBOARDING

  - name: CLIENT_SECRETS_SERVICE_PID_IDENTITY_BLOCKING
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_IDENTITY_BLOCKING

  - name: CLIENT_SECRETS_SERVICE_PID_DOCUMENT_SIGNER
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_DOCUMENT_SIGNER

  - name: CLIENT_SECRETS_SERVICE_PID_WORKFLOW
    valueFrom:
      secretKeyRef:
        name: pid-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_PID_WORKFLOW

  - name: CLIENT_SECRETS_WSO2_APIM_ONB
    valueFrom:
      secretKeyRef:
        name: wso2am-onb-keycloak-client-secrets
        key: CLIENT_SECRETS_WSO2_APIM_ONB
  - name: CLIENT_SECRETS_WSO2_APIM_PFS
    valueFrom:
      secretKeyRef:
        name: wso2am-pfs-keycloak-client-secrets
        key: CLIENT_SECRETS_WSO2_APIM_PFS

  - name: CLIENT_SECRETS_WSO2_APIM_PID
    valueFrom:
      secretKeyRef:
        name: wso2am-pid-keycloak-client-secrets
        key: CLIENT_SECRETS_WSO2_APIM_PID

  - name: CLIENT_SECRETS_WSO2_APIM_SVC
    valueFrom:
      secretKeyRef:
        name: wso2am-svc-keycloak-client-secrets
        key: CLIENT_SECRETS_WSO2_APIM_SVC

  - name: CLIENT_SECRETS_WSO2_APIM_GEPARD
    valueFrom:
      secretKeyRef:
        name: wso2am-gep-keycloak-client-secrets
        key: CLIENT_SECRETS_WSO2_APIM_GEPARD

  - name: KEYCLOAK_ADMIN_PASSWORD
    valueFrom:
      secretKeyRef:
        name: keycloak-admin-secret
        key: KEYCLOAK_ADMIN_PASSWORD

  - name: LOKI_PASSWORD
    valueFrom:
      secretKeyRef:
        name: loki-user-secret
        key: password

  - name: CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_BLOCKING
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_BLOCKING

  - name: CLIENT_SECRETS_SERVICE_BANK_TRANSACTION
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_TRANSACTION

  - name: CLIENT_SECRETS_IDM_MIDPOINT_CLIENT
    valueFrom:
      secretKeyRef:
        name: idm-midpoint-keycloak-client-secrets-external
        key: CLIENT_SECRETS_IDM_MIDPOINT_CLIENT

  - name: CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_TERMINATION
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_TERMINATION

  - name: CLIENT_SECRETS_SERVICE_BANK_TEMPLATE_RENDERER
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_TEMPLATE_RENDERER

  - name: CLIENT_SECRETS_SERVICE_BANK_TEMPLATING
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_TEMPLATING

  - name: CLIENT_SECRETS_SERVICE_BANK_FOREIGN_PAYMENT
    valueFrom:
      secretKeyRef:
        name: bank-keycloak-client-secrets
        key: CLIENT_SECRETS_SERVICE_BANK_FOREIGN_PAYMENT

# ============= resource-servers ===============

  - name: CLIENT_SECRETS_RS_LOAN_APPLICATION
    valueFrom:
      secretKeyRef:
        name: rs-pid-secret
        key: CLIENT_SECRETS_RS_LOAN_APPLICATION

  - name: CLIENT_SECRETS_RS_LOAN_APPLICATION_PFS
    valueFrom:
      secretKeyRef:
        name: rs-pfs-secret
        key: CLIENT_SECRETS_RS_LOAN_APPLICATION

  - name: CLIENT_SECRETS_RS_LOAN_APPLICATION_GEPARD
    valueFrom:
      secretKeyRef:
        name: rs-gepard-secret
        key: CLIENT_SECRETS_RS_LOAN_APPLICATION

  - name: CLIENT_SECRETS_RS_APS
    valueFrom:
      secretKeyRef:
        name: rs-pfs-secret
        key: CLIENT_SECRETS_RS_APS

  - name: CLIENT_SECRETS_RS_LOAN_PROCESSES
    valueFrom:
      secretKeyRef:
        name: rs-pid-secret
        key: CLIENT_SECRETS_RS_LOAN_PROCESSES

  - name: CLIENT_SECRETS_RS_BOA
    valueFrom:
      secretKeyRef:
        name: rs-pid-secret
        key: CLIENT_SECRETS_RS_BOA

  - name: CLIENT_SECRETS_RS_BOA_PFS
    valueFrom:
      secretKeyRef:
        name: rs-pfs-secret
        key: CLIENT_SECRETS_RS_BOA

service:
  prometheus_enabled: true
  prometheus_path: "/prometheus"
  annotations: { }

  type: ClusterIP
  portApp: 8083
  portMng: 8091
  ns: <<namespace>>

resources:
  limits:
    cpu: "1500m"
    memory: "1024Mi"
  requests:
    cpu: "50m"
    memory: "128Mi"
