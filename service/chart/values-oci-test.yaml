app:
  data:
    # <PERSON><PERSON><PERSON>
    ZIPKIN_URL: http://jaeger-test-oci-collector.surveillance:9411

    # Keycloak admin
    KEYCLOAK_ADMIN_ENDPOINT: https://login-adm.test.pbk-lab.tech/realms/master/protocol/openid-connect/token

    # Client settings
    WEB_ORIGINS_FRONTEND_APS: 'https://aps-frontend.internal.test.pbk-lab.tech | https://aps-frontend.internal.test.pbk-lab.tech/*'
    WEB_ORIGINS_FRONTEND_LOAN_APPLICATION_PFS: 'https://loan-application-fe-pfs.internal.test.pbk-lab.tech | http://localhost:3002'
    WEB_ORIGINS_FRONTEND_LOAN_APPLICATION: 'https://loan-application-fe.internal.test.pbk-lab.tech | http://localhost:3002'
    REDIRECT_URIS_FRONTEND_LOAN_APPLICATION: 'http://localhost:3002/auth/pid-callback | https://loan-application-fe.internal.test.pbk-lab.tech/auth/pid-callback'
    WEB_ORIGINS_FRONTEND_LOAN_PROCESSES: 'https://loan-processes-fe.internal.test.pbk-lab.tech | http://localhost:3004'
    REDIRECT_URIS_FRONTEND_LOAN_PROCESSES: 'http://localhost:3004/auth/pid-callback | https://loan-processes-fe.internal.test.pbk-lab.tech/auth/pid-callback'
    WEB_ORIGINS_FRONTEND_LOAN_TEMPLATING: 'https://loan-templating-fe.internal.test.pbk-lab.tech | http://localhost:3001'
    REDIRECT_URIS_FRONTEND_LOAN_TEMPLATING: 'http://localhost:3001/auth/pid-callback | https://loan-templating-fe.internal.test.pbk-lab.tech/auth/pid-callback'
    WEB_ORIGINS_FRONTEND_BANK_ONBOARDING: 'https://onboarding.internal.test.pbk-lab.tech | https://onboarding.internal.test.pbk-lab.tech/*'
    WEB_ORIGINS_FRONTEND_BOA: 'https://olymp.internal.test.pbk-lab.tech | https://olymp.internal.test.pbk-lab.tech/*'
    REDIRECT_URIS_FRONTEND_BOA: 'https://olymp.internal.test.pbk-lab.tech/valhalla/auth/pid-callback'
    WEB_ORIGINS_FRONTEND_BOA_PFS: 'https://olymp-pfs.internal.test.pbk-lab.tech | https://olymp-pfs.internal.test.pbk-lab.tech/*'
    REALM_PID_FRONTEND_URL: "https://login-pid.test.pbk-lab.tech"
    REALM_ONBOARD_FRONTEND_URL: "https://login-onb.test.pbk-lab.tech"
    REALM_PFS_FRONTEND_URL: "https://login-pfs.test.pbk-lab.tech"
    REALM_SERVICES_FRONTEND_URL: "https://login-svc.test.pbk-lab.tech"
    REALM_GEPARD_FRONTEND_URL: "https://login-gep.test.pbk-lab.tech"
    REDIRECT_URIS_FRONTEND_KNBOX: "http://knbox.internal.test.pbk-lab.tech/* | https://knbox.internal.test.pbk-lab.tech/* | http://knbox.knbox.svc:8080/*"
    REDIRECT_LOGOUT_URIS_FRONTEND_KNBOX: "http://knbox.internal.test.pbk-lab.tech/knbox/Knbox.html | https://knbox.internal.test.pbk-lab.tech/knbox/Knbox.html"
    ROOT_URL_FRONTEND_KNBOX: "https://knbox.internal.test.pbk-lab.tech"
    REDIRECT_URIS_FRONTEND_PAF: "http://localhost:8080/* | https://paf-case-investigation-web.internal.test.pbk-lab.tech/*"
    ROOT_URL_FRONTEND_PAF: "https://paf-case-investigation-web.internal.test.pbk-lab.tech"
    WEB_ORIGINS_FRONTEND_PAF: "http://localhost:8080 | https://paf-case-investigation-web.internal.test.pbk-lab.tech"
    REDIRECT_LOGOUT_URIS_FRONTEND_PAF: "https://paf-case-investigation-web.internal.test.pbk-lab.tech/*"
    REDIRECT_URIS_FRONTEND_AML_PLUGIN: "https://aml-services.internal.test.pbk-lab.tech/*"
    WEB_ORIGINS_FRONTEND_AML_PLUGIN: "https://aml-services.internal.test.pbk-lab.tech"
    REDIRECT_URIS_FRONTEND_FORECLOSURES: "https://bank-foreclosures.internal.test.pbk-lab.tech/login*"
    WEB_ORIGINS_FRONTEND_FORECLOSURES: "https://bank-foreclosures.internal.test.pbk-lab.tech"
    REDIRECT_URIS_COMPONENT_AML_SERVICES: "https://aml-services.internal.test.pbk-lab.tech/*"
    REDIRECT_URIS_COMPONENT_AML_ELS: "https://aml-els.internal.test.pbk-lab.tech/*"
    REDIRECT_URIS_COMPONENT_AML_SL_READER: "https://aml-sl-reader.internal.test.pbk-lab.tech/*"
    REDIRECT_URIS_COMPONENT_AML_CAMUNDA: "https://aml-camunda.internal.test.pbk-lab.tech/*"
    WEB_ORIGINS_FRONTEND_LOAN_TILES: 'https://loan-tiles-fe.internal.test.pbk-lab.tech | http://localhost:3006'
    REDIRECT_URIS_FRONTEND_LOAN_TILES: 'https://loan-tiles-fe.internal.test.pbk-lab.tech/auth/callback | http://localhost:3006/auth/callback'
    WEB_ORIGINS_FRONTEND_LOAN_TILES_GEPARD: 'https://loan-tiles-fe-gepard.internal.test.pbk-lab.tech | http://localhost:3006'
    REDIRECT_URIS_FRONTEND_LOAN_TILES_GEPARD: 'https://loan-tiles-fe-gepard.internal.test.pbk-lab.tech/auth/callback | http://localhost:3006/auth/callback'

    # Clients
    CLIENTS_ORG_KEYCLOAK_CLIENT_URL: https://login-adm.test.pbk-lab.tech
    CLIENTS_ORG_KEYCLOAK_CLIENT_PATH: /admin/realms
    CLIENTS_APIM: https://api.test.pbk-lab.tech

    # PSD2 tpp clients
    WEB_ORIGINS_PSD2_TPP_DUMMY: "http://localhost:8080 | https://psd2-demo-client.internal.test.pbk-lab.tech"
    REDIRECT_URIS_PSD2_TPP_DUMMY: "http://localhost:8080/* | https://mlich.eu/test.html | https://psd2-demo-client.internal.test.pbk-lab.tech/*"
    WEB_ORIGINS_PSD2_TPP_FINBRICKS: "https://local.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.dev.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.uat.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.perf.finbricks.cleverlance.com/psd2/oauth/partners/auth-redirect | https://fas-partners.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-gopay.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-gopay.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-payu.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-payu.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-trinity.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-trinity.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-inbank.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-inbank.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-worldline.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-worldline.finbricks.com/psd2/oauth/partners/auth-redirect"
    REDIRECT_URIS_PSD2_TPP_FINBRICKS: "https://local.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.dev.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.uat.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.perf.finbricks.cleverlance.com/psd2/oauth/partners/auth-redirect | https://fas-partners.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-gopay.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-gopay.finbricks.com/psd2/oauth/partners/auth-redirect  | https://fas-payu.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-payu.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-trinity.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-trinity.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-inbank.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-inbank.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-worldline.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-worldline.finbricks.com/psd2/oauth/partners/auth-redirect"
    WEB_ORIGINS_PSD2_TPP_CONNECT_PAY_UAB: "https://pay-secure.connectpay.com/connect/oauth_callback | https://www.saltedge.com/connect/oauth_callback"
    REDIRECT_URIS_PSD2_TPP_CONNECT_PAY_UAB: "https://pay-secure.connectpay.com/connect/oauth_callback | https://www.saltedge.com/connect/oauth_callback"
    WEB_ORIGINS_PSD2_TPP_EVERIFIN: "https://app.everifin.com/api/v1/ef/router"
    REDIRECT_URIS_PSD2_TPP_EVERIFIN: "https://app.everifin.com/api/v1/ef/router"