apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "chart.fullname" . }}-{{ .Chart.Version }}
  labels:
    app.kubernetes.io/name: {{ include "chart.name" . }}
    helm.sh/chart: {{ include "chart.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "chart.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      restartPolicy: OnFailure
      imagePullSecrets:
        - name: harbor-oci
        - name: harbor-aws
        - name: {{ .Values.image.imagePullSecret }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          envFrom:
          - configMapRef:
              name: {{ include "chart.fullname" . }}
          {{- if .Values.extraEnv }}
          env:
            {{- toYaml .Values.extraEnv | nindent 10 }}
          {{- end }}

          ports:
            - name: http
              containerPort: {{ .Values.service.portApp }}
              protocol: TCP
          resources: {{ toYaml .Values.resources | nindent 12 }}

