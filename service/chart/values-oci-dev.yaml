app:
  data:
    # <PERSON><PERSON><PERSON>
    ZIPKIN_URL: http://jaeger-dev-oci-collector.surveillance:9411

    # Keycloak admin
    KEYCLOAK_ADMIN_ENDPOINT: https://login-adm.dev.pbk-lab.tech/realms/master/protocol/openid-connect/token

    # Client settings
    WEB_ORIGINS_FRONTEND_APS: 'https://aps-frontend.internal.dev.pbk-lab.tech | https://aps-frontend.internal.dev.pbk-lab.tech/*'
    WEB_ORIGINS_FRONTEND_LOAN_APPLICATION_PFS: 'https://loan-application-fe-pfs.internal.dev.pbk-lab.tech | http://localhost:3001'
    WEB_ORIGINS_FRONTEND_LOAN_APPLICATION: 'https://loan-application-fe.internal.dev.pbk-lab.tech | http://localhost:3001'
    REDIRECT_URIS_FRONTEND_LOAN_APPLICATION: 'http://localhost:3001/auth/pid-callback | https://loan-application-fe.internal.dev.pbk-lab.tech/auth/pid-callback'
    WEB_ORIGINS_FRONTEND_LOAN_PROCESSES: 'https://loan-processes-fe.internal.dev.pbk-lab.tech | http://localhost:3003'
    REDIRECT_URIS_FRONTEND_LOAN_PROCESSES: 'http://localhost:3003/auth/pid-callback | https://loan-processes-fe.internal.dev.pbk-lab.tech/auth/pid-callback'
    WEB_ORIGINS_FRONTEND_LOAN_TEMPLATING: 'https://loan-templating-fe.internal.dev.pbk-lab.tech | http://localhost:3001'
    REDIRECT_URIS_FRONTEND_LOAN_TEMPLATING: 'http://localhost:3001/auth/pid-callback | https://loan-templating-fe.internal.dev.pbk-lab.tech/auth/pid-callback'
    WEB_ORIGINS_FRONTEND_BANK_ONBOARDING: 'https://onboarding.oci.dev.pbk-lab.tech | https://onboarding.oci.dev.pbk-lab.tech/*'
    # Note: `localhost` addresses are here to allow to have olymp app running locally and connected to `DEV`
    # Must not be present on higher environments - TEST, STAGE, PROD
    WEB_ORIGINS_FRONTEND_BOA: 'https://olymp.internal.dev.pbk-lab.tech | https://olymp.internal.dev.pbk-lab.tech/* | http://localhost:8080 | http://localhost:8080/* | http://localhost:3000'
    REDIRECT_URIS_FRONTEND_BOA: 'http://localhost:3000/valhalla/auth/pid-callback | https://olymp.internal.dev.pbk-lab.tech/valhalla/auth/pid-callback'
    WEB_ORIGINS_FRONTEND_BOA_PFS: 'https://olymp-pfs.internal.dev.pbk-lab.tech | https://olymp-pfs.internal.dev.pbk-lab.tech/* | http://localhost:8080 | http://localhost:8080/* | http://localhost:3000'
    REALM_PID_FRONTEND_URL: "https://login-pid.dev.pbk-lab.tech"
    REALM_ONBOARD_FRONTEND_URL: "https://login-onb.dev.pbk-lab.tech"
    REALM_PFS_FRONTEND_URL: "https://login-pfs.dev.pbk-lab.tech"
    REALM_SERVICES_FRONTEND_URL: "https://login-svc.dev.pbk-lab.tech"
    REALM_GEPARD_FRONTEND_URL: "https://login-gep.dev.pbk-lab.tech"
    REDIRECT_URIS_FRONTEND_KNBOX: "http://knbox.internal.dev.pbk-lab.tech/* | https://knbox.internal.dev.pbk-lab.tech/* | http://knbox.knbox.svc:8080/*"
    REDIRECT_LOGOUT_URIS_FRONTEND_KNBOX: "http://knbox.internal.dev.pbk-lab.tech/knbox/Knbox.html | https://knbox.internal.dev.pbk-lab.tech/knbox/Knbox.html"
    ROOT_URL_FRONTEND_KNBOX: "https://knbox.internal.dev.pbk-lab.tech"
    REDIRECT_URIS_FRONTEND_PAF: "http://localhost:8080/* | https://paf-case-investigation-web.internal.dev.pbk-lab.tech/*"
    ROOT_URL_FRONTEND_PAF: "https://paf-case-investigation-web.internal.dev.pbk-lab.tech"
    WEB_ORIGINS_FRONTEND_PAF: "http://localhost:8080 | https://paf-case-investigation-web.internal.dev.pbk-lab.tech"
    REDIRECT_LOGOUT_URIS_FRONTEND_PAF: "https://paf-case-investigation-web.internal.dev.pbk-lab.tech/*"
    REDIRECT_URIS_FRONTEND_AML_PLUGIN: "https://aml-services.internal.dev.pbk-lab.tech/*"
    WEB_ORIGINS_FRONTEND_AML_PLUGIN: "https://aml-services.internal.dev.pbk-lab.tech"
    REDIRECT_URIS_FRONTEND_FORECLOSURES: "https://bank-foreclosures.internal.dev.pbk-lab.tech/login* | http://localhost:9990/*"
    WEB_ORIGINS_FRONTEND_FORECLOSURES: "https://bank-foreclosures.internal.dev.pbk-lab.tech | http://localhost:9990"
    REDIRECT_URIS_COMPONENT_AML_SERVICES: "https://aml-services.internal.dev.pbk-lab.tech/*"
    REDIRECT_URIS_COMPONENT_AML_ELS: "https://aml-els.internal.dev.pbk-lab.tech/*"
    REDIRECT_URIS_COMPONENT_AML_SL_READER: "https://aml-sl-reader.internal.dev.pbk-lab.tech/*"
    REDIRECT_URIS_COMPONENT_AML_CAMUNDA: "https://aml-camunda.internal.dev.pbk-lab.tech/*"
    WEB_ORIGINS_FRONTEND_LOAN_TILES: 'https://loan-tiles-fe.internal.dev.pbk-lab.tech | http://localhost:3006'
    REDIRECT_URIS_FRONTEND_LOAN_TILES: 'https://loan-tiles-fe.internal.dev.pbk-lab.tech/auth/callback | http://localhost:3006/auth/callback'
    WEB_ORIGINS_FRONTEND_LOAN_TILES_GEPARD: 'https://loan-tiles-fe-gepard.internal.dev.pbk-lab.tech | http://localhost:3006'
    REDIRECT_URIS_FRONTEND_LOAN_TILES_GEPARD: 'https://loan-tiles-fe-gepard.internal.dev.pbk-lab.tech/auth/callback | http://localhost:3006/auth/callback'

    # Clients
    CLIENTS_ORG_KEYCLOAK_CLIENT_URL: https://login-adm.dev.pbk-lab.tech
    CLIENTS_ORG_KEYCLOAK_CLIENT_PATH: /admin/realms
    CLIENTS_APIM: https://api.dev.pbk-lab.tech

    # PSD2 tpp clients
    WEB_ORIGINS_PSD2_TPP_DUMMY: "http://localhost:8080 | https://psd2-demo-client.internal.dev.pbk-lab.tech"
    REDIRECT_URIS_PSD2_TPP_DUMMY: "http://localhost:8080/* | https://psd2-demo-client.internal.dev.pbk-lab.tech/*"
    WEB_ORIGINS_PSD2_TPP_FINBRICKS: "https://local.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.dev.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.uat.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.perf.finbricks.cleverlance.com/psd2/oauth/partners/auth-redirect | https://fas-partners.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-gopay.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-gopay.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-payu.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-payu.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-trinity.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-trinity.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-inbank.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-inbank.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-worldline.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-worldline.finbricks.com/psd2/oauth/partners/auth-redirect"
    REDIRECT_URIS_PSD2_TPP_FINBRICKS: "https://local.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.dev.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.uat.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas.perf.finbricks.cleverlance.com/psd2/oauth/partners/auth-redirect | https://fas-partners.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-gopay.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-gopay.finbricks.com/psd2/oauth/partners/auth-redirect  | https://fas-payu.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-payu.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-trinity.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-trinity.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-inbank.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-inbank.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-worldline.sandbox.finbricks.com/psd2/oauth/partners/auth-redirect | https://fas-worldline.finbricks.com/psd2/oauth/partners/auth-redirect"
    WEB_ORIGINS_PSD2_TPP_CONNECT_PAY_UAB: "https://pay-secure.connectpay.com/connect/oauth_callback | https://www.saltedge.com/connect/oauth_callback"
    REDIRECT_URIS_PSD2_TPP_CONNECT_PAY_UAB: "https://pay-secure.connectpay.com/connect/oauth_callback | https://www.saltedge.com/connect/oauth_callback"
    WEB_ORIGINS_PSD2_TPP_EVERIFIN: "https://app.everifin.com/api/v1/ef/router"
    REDIRECT_URIS_PSD2_TPP_EVERIFIN: "https://app.everifin.com/api/v1/ef/router"