version: ${VERSION:0.0}

keycloak:
  admin:
    endpoint: ${<PERSON><PERSON><PERSON><PERSON><PERSON>K_ADMIN_ENDPOINT:`http://host.docker.internal:8888/realms/master/protocol/openid-connect/token`}
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID:`admin-cli`}
    username: ${<PERSON><PERSON><PERSON>CLOAK_ADMIN_USERNAME:`admin`}
    password: ${KE<PERSON>CLOAK_ADMIN_PASSWORD:`admin`}
    token-renewal-interval: ${KEYCLOAK_ADMIN_TOKEN_RENEWAL_INTERVAL:`55s`}
  origin-check:
    attribute-name: "cz.partners.origin"
    origin-name: "static-configuration"

keycloak-configuration-file: ${K<PERSON><PERSON><PERSON>OAK_CONFIGURATION_FILE:`keycloak/configuration.yaml`}
authentication-flows-configuration-file: ${AUTH_FLOWS_CONFIGURATION_FILE:`keycloak/authentication-flows.yaml`}

# default values from STAGE
realm:
  pid:
    frontendUrl: ${R<PERSON><PERSON>_PID_FRONTEND_URL:`http://host.docker.internal:8888`}
    token-lifespan:
      access-token: ${REALM_PID_TOKEN_LIFESPAN_ACCESS_TOKEN:`5m`}
      sso-session: ${REALM_PID_TOKEN_LIFESPAN_SSO_SESSION:`30m`}
      sso-session-max: ${REALM_PID_TOKEN_LIFESPAN_SSO_SESSION_MAX:`10h`}
      client-session: ${REALM_PID_TOKEN_LIFESPAN_CLIENT_SESSION:`0m`}
      client-session-max: ${REALM_PID_TOKEN_LIFESPAN_CLIENT_SESSION_MAX:`0m`}
      offline-session: ${REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
      offline-session-max-enabled: ${REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED:`false`}
      offline-session-max: ${REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  onboard:
    frontendUrl: ${REALM_ONBOARD_FRONTEND_URL:`http://host.docker.internal:8888`}
    token-lifespan:
      access-token: ${REALM_ONBOARD_TOKEN_LIFESPAN_ACCESS_TOKEN:`5m`}
      sso-session: ${REALM_ONBOARD_TOKEN_LIFESPAN_SSO_SESSION:`30m`}
      sso-session-max: ${REALM_ONBOARD_TOKEN_LIFESPAN_SSO_SESSION_MAX:`10h`}
      client-session: ${REALM_ONBOARD_TOKEN_LIFESPAN_CLIENT_SESSION:`0m`}
      client-session-max: ${REALM_ONBOARD_TOKEN_LIFESPAN_CLIENT_SESSION_MAX:`0m`}
      offline-session: ${REALM_ONBOARD_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
      offline-session-max-enabled: ${REALM_ONBOARD_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED:`false`}
      offline-session-max: ${REALM_ONBOARD_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  pfs:
    frontendUrl: ${REALM_PFS_FRONTEND_URL:`http://host.docker.internal:8888`}
    token-lifespan:
      access-token: ${REALM_PFS_TOKEN_LIFESPAN_ACCESS_TOKEN:`5m`}
      sso-session: ${REALM_PFS_TOKEN_LIFESPAN_SSO_SESSION:`30m`}
      sso-session-max: ${REALM_PFS_TOKEN_LIFESPAN_SSO_SESSION_MAX:`10h`}
      client-session: ${REALM_PFS_TOKEN_LIFESPAN_CLIENT_SESSION:`0m`}
      client-session-max: ${REALM_PFS_TOKEN_LIFESPAN_CLIENT_SESSION_MAX:`0m`}
      offline-session: ${REALM_PFS_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
      offline-session-max-enabled: ${REALM_PFS_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED:`false`}
      offline-session-max: ${REALM_PFS_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  gepard:
    frontendUrl: ${REALM_GEPARD_FRONTEND_URL:`http://host.docker.internal:8888`}
    token-lifespan:
      access-token: ${REALM_GEPARD_TOKEN_LIFESPAN_ACCESS_TOKEN:`5m`}
      sso-session: ${REALM_GEPARD_TOKEN_LIFESPAN_SSO_SESSION:`30m`}
      sso-session-max: ${REALM_GEPARD_TOKEN_LIFESPAN_SSO_SESSION_MAX:`10h`}
      client-session: ${REALM_GEPARD_TOKEN_LIFESPAN_CLIENT_SESSION:`0m`}
      client-session-max: ${REALM_GEPARD_TOKEN_LIFESPAN_CLIENT_SESSION_MAX:`0m`}
      offline-session: ${REALM_GEPARD_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
      offline-session-max-enabled: ${REALM_GEPARD_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED:`false`}
      offline-session-max: ${REALM_GEPARD_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  services:
    frontendUrl: ${REALM_SERVICES_FRONTEND_URL:`http://host.docker.internal:8888`}
    token-lifespan:
      access-token: ${REALM_SERVICES_TOKEN_LIFESPAN_ACCESS_TOKEN:`5m`}
      sso-session: ${REALM_SERVICES_TOKEN_LIFESPAN_SSO_SESSION:`30m`}
      sso-session-max: ${REALM_SERVICES_TOKEN_LIFESPAN_SSO_SESSION_MAX:`10h`}
      client-session: ${REALM_SERVICES_TOKEN_LIFESPAN_CLIENT_SESSION:`0m`}
      client-session-max: ${REALM_SERVICES_TOKEN_LIFESPAN_CLIENT_SESSION_MAX:`0m`}
      offline-session: ${REALM_SERVICES_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
      offline-session-max-enabled: ${REALM_SERVICES_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED:`false`}
      offline-session-max: ${REALM_SERVICES_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}

#client-saml:
#  azure:
#    master-saml-processing-url: ${CLIENT_SAML_AZURE_MASTER_SAML_PROCESSING_URL:`https://nexus.microsoftonline-p.com/federationmetadata/saml20/federationmetadata.xml`}

client-openid:
  wso2-apim-client-pid:
    secret: ${CLIENT_SECRETS_WSO2_APIM_PID:`0000`}
  wso2-apim-client-onboard:
    secret: ${CLIENT_SECRETS_WSO2_APIM_ONB:`0000`}
  wso2-apim-client-pfs:
    secret: ${CLIENT_SECRETS_WSO2_APIM_PFS:`0000`}
  wso2-apim-client-services:
    secret: ${CLIENT_SECRETS_WSO2_APIM_SVC:`0000`}
  wso2-apim-client-gepard:
    secret: ${CLIENT_SECRETS_WSO2_APIM_GEPARD:`0000`}

  master-keycloak-user-manager:
    secret: ${CLIENT_SECRETS_MASTER_KEYCLOAK_USER_MANAGER:`0000`}
  service-pfs-auth-manager:
    secret: ${CLIENT_SECRETS_SERVICE_PFS_AUTH_MANAGER:`0000`}
  service-bank-daily-auth-manager:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_DAILY_AUTH_MANAGER:`0000`}
  service-keycloak-user-manager:
    secret: ${CLIENT_SECRETS_SERVICE_KEYCLOAK_USER_MANAGER:`0000`}
  department-bank-employee-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_BANK_EMPLOYEE_CLIENT:`0000`}
  department-pfs-consultant-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CONSULTANT_CLIENT:`0000`}
  department-fip-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT:`0000`}
  department-fip-sk-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_SK_CLIENT:`0000`}
  department-anakin-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_CLIENT:`0000`}
  department-anakin-sk-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_SK_CLIENT:`0000`}
  department-ocp-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_OCP_CLIENT:`0000`}
  department-pfs-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CLIENT:`0000`}
  department-pid-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_CLIENT:`0000`}
  department-pid-test-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_TEST_CLIENT:`0000`}
  department-pis-client:
    secret: ${CLIENT_SECRETS_DEPARTMENT_PIS_CLIENT:`0000`}
  department-rentea-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_RENTEA_CLIENT:`0000`}
  department-orfeus-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_CLIENT:`0000`}
  department-orfeus-sk-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_SK_CLIENT:`0000`}
  department-simplea-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_SIMPLEA_CLIENT:`0000`}
  department-gepard-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_GEPARD_CLIENT:`0000`}
  department-vub-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_VUB_CLIENT:`0000`}
  department-wpb-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_WPB_CLIENT:`0000`}
  department-odoo-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_ODOO_CLIENT:`0000`}
  service-pid-auth-manager:
    secret: ${CLIENT_SECRETS_SERVICE_PID_AUTH_MANAGER:`0000`}
  service-pfs-portfolio-auth-manager:
    secret: ${CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO_AUTH_MANAGER:`0000`}
  service-pfs-portfolio:
    secret: ${CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO:`0000`}
  service-pfs-insurance-comparator:
    secret: ${CLIENT_SECRETS_SERVICE_PFS_INSURANCE_COMPARATOR:`0000`}
  service-pfs-contract-sharing:
    secret: ${CLIENT_SECRETS_SERVICE_PFS_CONTRACT_SHARING:`0000`}
  service-pfs-acceptance:
    secret: ${CLIENT_SECRETS_SERVICE_PFS_ACCEPTANCE:`0000`}
  service-nebanka-ocp:
    secret: ${CLIENT_SECRETS_SERVICE_NEBANKA_OCP:`0000`}
  department-moje-partners-client:
    secret: ${CLIENT_SECRETS_DEPARTMENT_MOJE_PARTNERS_CLIENT:`0000`}
  service-pid-onboard-auth-manager:
    secret: ${CLIENT_SECRETS_SERVICE_PID_ONBOARD_AUTH_MANAGER:`0000`}
  service-pid-bpm:
    secret: ${CLIENT_SECRETS_SERVICE_PID_BPM:`0000`}
  service-pid-mobile-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_PID_MOBILE_GATEWAY:`0000`}
  service-pid-activation-service:
    secret: ${CLIENT_SECRETS_SERVICE_PID_ACTIVATION_SERVICE:`0000`}
  service-pid-core:
    secret: ${CLIENT_SECRETS_SERVICE_PID_CORE:`0000`}
  service-pid-email:
    secret: ${CLIENT_SECRETS_SERVICE_PID_EMAIL:`0000`}
  service-pid-notification-service:
    secret: ${CLIENT_SECRETS_SERVICE_PID_NOTIFICATION_SERVICE:`0000`}
  service-pid-identity-blocking:
    secret: ${CLIENT_SECRETS_SERVICE_PID_IDENTITY_BLOCKING:`0000`}
  service-pid-document-signer:
    secret: ${CLIENT_SECRETS_SERVICE_PID_DOCUMENT_SIGNER:`0000`}
  service-pid-workflow:
    secret: ${CLIENT_SECRETS_SERVICE_PID_WORKFLOW:`0000`}
  service-pid-backoffice-events:
    secret: ${CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_EVENTS:`0000`}
  service-pid-backoffice-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_GATEWAY:`0000`}
  service-pid-activation-backoffice-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_PID_ACTIVATION_BACKOFFICE_GATEWAY:`0000`}
  service-bank-pfs-integration:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_PFS_INTEGRATION:`0000`}
  service-bank-service-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_SERVICE_GATEWAY:`0000`}
  service-aps-modeling:
    secret: ${CLIENT_SECRETS_SERVICE_APS_MODELING:`0000`}
  service-aps-bff:
    secret: ${CLIENT_SECRETS_SERVICE_APS_BFF:`0000`}
  rs-aps:
    secret: ${CLIENT_SECRETS_RS_APS:`0000`}
  service-loan-whisper-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_WHISPER_BRIDGE:`0000`}
  service-loan-de-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_DE_BRIDGE:`0000`}
  service-loan-core-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_CORE_BRIDGE:`0000`}
  service-loan-pid-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_PID_BRIDGE:`0000`}
  component-decision-engine:
    secret: ${CLIENT_SECRETS_COMPONENT_DECISION_ENGINE:`0000`}
  service-loan-product-catalog-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_PRODUCT_CATALOG_BRIDGE:`0000`}
  frontend-aps:
    web-origins: ${WEB_ORIGINS_FRONTEND_APS:`https://aps-frontend.oci.dev.pbk-lab.tech | https://aps-frontend.oci.dev.pbk-lab.tech/*`}
  service-aps-application:
    secret: ${CLIENT_SECRETS_SERVICE_APS_APPLICATION:`0000`}
  service-aps-gate:
    secret: ${CLIENT_SECRETS_SERVICE_APS_GATE:`0000`}
  service-loan-pfs-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_PFS_BRIDGE:`0000`}
  service-pfs-advisor-definition:
    secret: ${CLIENT_SECRETS_SERVICE_PFS_ADVISOR_DEFINITION:`0000`}
  service-aps-loan:
    secret: ${CLIENT_SECRETS_SERVICE_APS_LOAN:`0000`}
  service-aps-parameters:
    secret: ${CLIENT_SECRETS_SERVICE_APS_PARAMETERS:`0000`}
  service-loan-codelists-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_CODELISTS_BRIDGE:`0000`}
  service-bank-codelists:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CODELISTS:`0000`}
  component-knbox:
    secret: ${CLIENT_SECRETS_COMPONENT_KNBOX:`0000`}
  service-aml-plugin-service:
    secret: ${CLIENT_SECRETS_SERVICE_AML_PLUGIN_SERVICE:`0000`}
  service-paf-case-investigation-core:
    secret: ${CLIENT_SECRETS_SERVICE_PAF:`0000`}
  service-paf-case-investigation-pid:
    secret: ${CLIENT_SECRETS_SERVICE_PID_PAF:`0000`}
  frontend-bank-onboarding:
    web-origins: ${WEB_ORIGINS_FRONTEND_BANK_ONBOARDING:`http://localhost:3000 | http://localhost:3000/*`}
  service-aps-origination:
    secret: ${CLIENT_SECRETS_SERVICE_APS_ORIGINATION:`0000`}
  service-aps-processes:
    secret: ${CLIENT_SECRETS_SERVICE_APS_PROCESSES:`0000`}
  service-bank-client-documents:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CLIENT_DOCUMENTS:`0000`}
  service-loan-documents-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_DOCUMENTS_BRIDGE:`0000`}
  service-loan-notification-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_NOTIFICATION_BRIDGE:`0000`}
  service-loan-knbox-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_KNBOX_BRIDGE:`0000`}
  service-loan-customer-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_CUSTOMER_GATEWAY:`0000`}
  service-loan-sign-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_SIGN_BRIDGE:`0000`}
  service-bank-payment:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_PAYMENT:`0000`}
  service-bank-accounts-registry:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_ACCOUNTS_REGISTRY:`0000`}
  service-bank-client:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CLIENT:`0000`}
  service-aps-pepz:
    secret: ${CLIENT_SECRETS_SERVICE_APS_PEPZ:`0000`}
  service-aps-document:
    secret: ${CLIENT_SECRETS_SERVICE_APS_DOCUMENT:`0000`}
  service-aps-valuers:
    secret: ${CLIENT_SECRETS_SERVICE_APS_VALUERS:`0000`}
  service-aps-exception:
    secret: ${CLIENT_SECRETS_SERVICE_APS_EXCEPTION:`0000`}
  service-bank-chargeback:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CHARGEBACK:`0000`}
  service-notification-daktela-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_NOTIFICATION_DAKTELA_BRIDGE:`0000`}
  service-notification-exponea-bridge:
    secret: ${CLIENT_SECRETS_SERVICE_NOTIFICATION_EXPONEA_BRIDGE:`0000`}
  service-notification-push-service:
    secret: ${CLIENT_SECRETS_SERVICE_NOTIFICATION_PUSH_SERVICE:`0000`}
  service-notification-smsgate-tmobile:
    secret: ${CLIENT_SECRETS_SERVICE_NOTIFICATION_SMSGATE_TMOBILE:`0000`}
  service-bank-fip-onboarding-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING_GATEWAY:`0000`}
  service-bank-challenges:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CHALLENGES:`0000`}
  service-bank-cooperation-service:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_COOPERATION_SERVICE:`0000`}
  service-bank-fip-onboarding:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING:`0000`}
  service-bank-campaign:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CAMPAIGN:`0000`}
  service-bank-dashboard:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_DASHBOARD:`0000`}
  service-bank-transaction-notification:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_TRANSACTION_NOTIFICATION:`0000`}
  service-bank-account-blocking:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_BLOCKING:`0000`}
  service-bank-transaction:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_TRANSACTION:`0000`}
  service-bank-account-termination:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_TERMINATION:`0000`}
  service-bank-template-renderer:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_TEMPLATE_RENDERER:`0000`}
  service-bank-templating:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_TEMPLATING:`0000`}
  service-bank-foreign-payment:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_FOREIGN_PAYMENT:`0000`}
  frontend-aml-plugin:
    redirect-uris: ${REDIRECT_URIS_FRONTEND_AML_PLUGIN:`http://localhost:8080/*`}
    web-origins: ${WEB_ORIGINS_FRONTEND_AML_PLUGIN:`http://localhost:8080/`}
  frontend-paf-case-investigation:
   redirect-uris: ${REDIRECT_URIS_FRONTEND_PAF:`http://localhost:8080/*`}
   redirect-logout-uris: ${REDIRECT_LOGOUT_URIS_FRONTEND_PAF:`http://localhost:8080/*`}
   root-url: ${ROOT_URL_FRONTEND_PAF:`http://localhost:8080`}
   web-origins: ${WEB_ORIGINS_FRONTEND_PAF:`http://localhost:8080`}
  frontend-knbox:
    secret: ${CLIENT_SECRETS_FRONTEND_KNBOX:`0000`}
    redirect-uris: ${REDIRECT_URIS_FRONTEND_KNBOX:`http://localhost`}
    redirect-logout-uris: ${REDIRECT_LOGOUT_URIS_FRONTEND_KNBOX:`http://localhost/logout`}
    root-url: ${ROOT_URL_FRONTEND_KNBOX:`http://localhost`}
  service-loan-application-unsecured:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_UNSECURED:`0000`}
  service-loan-account-registry:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_ACCOUNT_REGISTRY:`0000`}
  service-loan-application-secured:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_SECURED:`0000`}
  service-loan-modeling-secured:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_MODELING_SECURED:`0000`}
  service-loan-contracting:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_CONTRACTING:`0000`}
  frontend-boa:
    web-origins: ${WEB_ORIGINS_FRONTEND_BOA:`http://localhost:8080 | http://localhost:8080/*`}
    redirect-uris: ${REDIRECT_URIS_FRONTEND_BOA:`http://localhost:3000/valhalla/auth/pid-callback`}
  frontend-boa-pfs:
    web-origins: ${WEB_ORIGINS_FRONTEND_BOA_PFS:`http://localhost:8080 | http://localhost:8080/*`}
  frontend-treasury:
    secret: ${CLIENT_SECRETS_FRONTEND_TREASURY:`0000`}
    redirect-uris: ${REDIRECT_URIS_FRONTEND_TREASURY:`http://localhost/auth/openid-connect/callback`}
  frontend-bank-foreclosures:
    redirect-uris: ${REDIRECT_URIS_FRONTEND_FORECLOSURES:`http://localhost:9990/login`}
    web-origins: ${WEB_ORIGINS_FRONTEND_FORECLOSURES:`http://localhost:8082 | http://localhost:9990`}
  component-aml-services:
    secret: ${CLIENT_SECRETS_COMPONENT_AML_SERVICES:`0000`}
    redirect-uris: ${REDIRECT_URIS_COMPONENT_AML_SERVICES:`http://localhost/auth/openid-connect/callback`}
  component-aml-els:
    secret: ${CLIENT_SECRETS_COMPONENT_AML_ELS:`0000`}
    redirect-uris: ${REDIRECT_URIS_COMPONENT_AML_ELS:`http://localhost/auth/openid-connect/callback`}
  component-aml-sl-reader:
    secret: ${CLIENT_SECRETS_COMPONENT_AML_SL_READER:`0000`}
    redirect-uris: ${REDIRECT_URIS_COMPONENT_AML_SL_READER:`http://localhost/auth/openid-connect/callback`}
  component-aml-camunda:
    secret: ${CLIENT_SECRETS_COMPONENT_AML_CAMUNDA:`0000`}
    redirect-uris: ${REDIRECT_URIS_COMPONENT_AML_CAMUNDA:`http://localhost/auth/openid-connect/callback`}
  # client services-pid-aml in pid realm is a temporary solution until AML supports authentication using multiple realms
  # this client configuration (including credentials) is shared between `services-pid-aml` from `pid` and `services` reals - this is acceptable for DevOps as long as it actually is temporary
  service-pid-aml:
    secret: ${CLIENT_SECRETS_SERVICE_PID_AML:`0000`}
  service-pid-onboarding:
    secret: ${CLIENT_SECRETS_SERVICE_PID_ONBOARDING:`0000`}
  department-fip-payment-client:
    secret: ${CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_PAYMENT_CLIENT:`0000`}
  service-pid-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_PID_GATEWAY:`0000`}
  service-pid-bankid-oidc-connector:
    secret: ${CLIENT_SECRETS_SERVICE_PID_BANKID_OIDC_CONNECTOR:`0000`}
  service-pid-bankid-data:
    secret: ${CLIENT_SECRETS_SERVICE_PID_BANKID_DATA:`0000`}
  service-pid-fed-authentication:
    secret: ${CLIENT_SECRETS_SERVICE_PID_FED_AUTHENTICATION:`0000`}
  service-notification-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_NOTIFICATION_GATEWAY:`0000`}
  component-daktela:
    secret: ${CLIENT_SECRETS_COMPONENT_DAKTELA:`0000`}
  component-exponea:
    secret: ${CLIENT_SECRETS_COMPONENT_EXPONEA:`0000`}
  frontend-loan-application-pfs:
    web-origins: ${WEB_ORIGINS_FRONTEND_LOAN_APPLICATION_PFS:`http://localhost:3001`}
  rs-loan-application:
    secret: ${CLIENT_SECRETS_RS_LOAN_APPLICATION:`0000`}
  rs-loan-application-pfs:
    secret: ${CLIENT_SECRETS_RS_LOAN_APPLICATION_PFS:`0000`}
  rs-loan-application-gepard:
    secret: ${CLIENT_SECRETS_RS_LOAN_APPLICATION_GEPARD:`0000`}
  frontend-loan-application:
    secret: ${CLIENT_SECRETS_FRONTEND_LOAN_APPLICATION:`0000`}
    web-origins: ${WEB_ORIGINS_FRONTEND_LOAN_APPLICATION:`http://localhost:3001`}
    redirect-uris: ${REDIRECT_URIS_FRONTEND_LOAN_APPLICATION:`http://localhost:3001/auth/pid-callback`}
  rs-loan-processes:
    secret: ${CLIENT_SECRETS_RS_LOAN_PROCESSES:`0000`}
  frontend-loan-processes:
    secret: ${CLIENT_SECRETS_FRONTEND_LOAN_PROCESSES:`0000`}
    web-origins: ${WEB_ORIGINS_FRONTEND_LOAN_PROCESSES:`http://localhost:3003`}
    redirect-uris: ${REDIRECT_URIS_FRONTEND_LOAN_PROCESSES:`http://localhost:3003/auth/pid-callback`}
  frontend-loan-templating:
    web-origins: ${WEB_ORIGINS_FRONTEND_LOAN_TEMPLATING:`http://localhost:3001`}
    redirect-uris: ${REDIRECT_URIS_FRONTEND_LOAN_TEMPLATING:`http://localhost:3001/auth/pid-callback`}
  psd2-tpp-dummy:
    secret: ${CLIENT_SECRETS_PSD2_TPP_DUMMY:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_DUMMY:`http://localhost:8080`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_DUMMY:`http://localhost:8080/*`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_DUMMY:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-finbricks:
    secret: ${CLIENT_SECRETS_PSD2_TPP_FINBRICKS:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_FINBRICKS:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_FINBRICKS:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_FINBRICKS:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-partners-banka:
    secret: ${CLIENT_SECRETS_PSD2_TPP_PARTNERS_BANKA:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_FINBRICKS:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_FINBRICKS:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_FINBRICKS:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-gopay:
    secret: ${CLIENT_SECRETS_PSD2_TPP_GOPAY:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_FINBRICKS:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_FINBRICKS:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_FINBRICKS:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-payu:
    secret: ${CLIENT_SECRETS_PSD2_TPP_PAYU:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_FINBRICKS:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_FINBRICKS:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_FINBRICKS:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-trinity-bank:
    secret: ${CLIENT_SECRETS_PSD2_TPP_TRINITY_BANK:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_FINBRICKS:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_FINBRICKS:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_FINBRICKS:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-inbank:
    secret: ${CLIENT_SECRETS_PSD2_TPP_INBANK:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_FINBRICKS:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_FINBRICKS:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_FINBRICKS:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-bank-transfer-worldline:
    secret: ${CLIENT_SECRETS_PSD2_TPP_BANK_TRANSFER_WORLDLINE:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_FINBRICKS:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_FINBRICKS:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_FINBRICKS:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-connect-pay-uab:
    secret: ${CLIENT_SECRETS_PSD2_TPP_CONNECT_PAY_UAB:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_CONNECT_PAY_UAB:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_CONNECT_PAY_UAB:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_CONNECT_PAY_UAB:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  psd2-tpp-everifin:
    secret: ${CLIENT_SECRETS_PSD2_TPP_EVERIFIN:`0000`}
    web-origins: ${WEB_ORIGINS_PSD2_TPP_EVERIFIN:`https://api.pbapi.cz/`}
    redirect-uris: ${REDIRECT_URIS_PSD2_TPP_EVERIFIN:`http://api.pbapi.cz/tbd`}
    authentication-flow: ${AUTH_FLOW_PSD2_TPP_EVERIFIN:`PIDFederatedAuthentication`}
    offline-session: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION:`30d`}
    offline-session-max: ${PSD2_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX:`60d`}
  service-bank-standing-order:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_STANDING_ORDER:`0000`}
  service-bank-treasury-service:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_TREASURY_SERVICE:`0000`}
  service-bank-mobility:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_MOBILITY:`0000`}
  service-bank-card-management:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CARD_MANAGEMENT:`0000`}
  service-bank-card-savings:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CARD_SAVINGS:`0000`}
  service-bank-click-to-pay:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_CLICK_TO_PAY:`0000`}
  component-decision-engine-aml:
    secret: ${CLIENT_SECRETS_COMPONENT_DECISION_ENGINE_AML:`0000`}
  service-loan-business-codegen:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_BUSINESS_CODEGEN:`0000`}
  service-nebanka-backoffice-be:
    secret: ${CLIENT_SECRETS_SERVICE_NEBANKA_BACKOFFICE_BE:`0000`}
  service-idp-apim-orchestrator:
    secret: ${CLIENT_SECRETS_SERVICE_IDP_APIM_ORCHESTRATOR:`0000`}
  master-idp-apim-orchestrator:
    secret: ${CLIENT_SECRETS_MASTER_IDP_APIM_ORCHESTRATOR:`0000`}
  service-bank-jira-adapter:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_JIRA_ADAPTER:`0000`}
  service-apigw-operator-synchronous-api:
    secret: ${CLIENT_SECRETS_SERVICE_APIGW_OPERATOR_SYNCHRONOUS_API:`0000`}
  service-wso2:
    secret: ${CLIENT_SECRETS_SERVICE_WSO2:`0000`}
  service-loan-task-queue:
    secret: ${CLIENT_SECRETS_SERVICE_LOAN_TASK_QUEUE:`0000`}
  rs-boa:
    secret: ${CLIENT_SECRETS_RS_BOA:`0000`}
  rs-boa-pfs:
    secret: ${CLIENT_SECRETS_RS_BOA_PFS:`0000`}
  service-bank-backoffice-gateway:
    secret: ${CLIENT_SECRETS_SERVICE_BANK_BACKOFFICE_GATEWAY:`0000`}
  service-idm-midpoint-client:
    secret: ${CLIENT_SECRETS_IDM_MIDPOINT_CLIENT:`0000`}
  frontend-loan-tiles:
    web-origins: ${WEB_ORIGINS_FRONTEND_LOAN_TILES:`http://localhost:3006`}
    redirect-uris: ${REDIRECT_URIS_FRONTEND_LOAN_TILES:`http://localhost:3006/auth/callback`}
  frontend-loan-tiles-gepard:
    web-origins: ${WEB_ORIGINS_FRONTEND_LOAN_TILES_GEPARD:`http://localhost:3006`}
    redirect-uris: ${REDIRECT_URIS_FRONTEND_LOAN_TILES_GEPARD:`http://localhost:3006/auth/callback`}

authentication-flows:
  config:
    fed-authenticator-config:
      properties:
        - key: FedAuthBackendPublicUrl
          value: ${CLIENTS_APIM:`http://host.docker.internal:8155`}
        - key: FedAuthBackendApiUrl
          value: ${CLIENTS_PID_FED_AUTHENTICATION:`http://host.docker.internal:8155`}
        - key: FedAuthBackendLoginPath
          value: ${CONFIG_FED_AUTH_LOGIN_PATH:`public/v1/login/init`}
        - key: FedAuthBackendVerifyPath
          value: ${CONFIG_FED_AUTH_VERIFY_PATH:`keycloak/v1/verify`}
        - key: FedAuthNotEligibleErrorCode
          value: ${CONFIG_FED_AUTH_NOT_ELIBIGLE_ERROR_CODE:`user_not_eligible`}
        - key: FedAuthNotEligibleDefaultErrorMessage
          value: ${CONFIG_FED_AUTH_NOT_ELIBIGLE_DEFAULT_ERROR_MESSAGE:`Insufficient user data or age restriction`}
    fed-authenticator-config-bankid:
      properties:
        - key: FedAuthBackendPublicUrl
          value: ${CLIENTS_APIM:`http://host.docker.internal:8155`}
        - key: FedAuthBackendApiUrl
          value: ${CLIENTS_PID_FED_AUTHENTICATION:`http://host.docker.internal:8155`}
        - key: FedAuthBackendLoginPath
          value: ${CONFIG_FED_AUTH_LOGIN_PATH:`public/v1/login/init`}
        - key: FedAuthBackendVerifyPath
          value: ${CONFIG_FED_AUTH_VERIFY_PATH:`keycloak/v1/verify`}
        - key: FedAuthAuthRequestValidationEnabled
          value: ${CONFIG_FED_AUTH_BANKID_REQUEST_VALIDATION_ENABLED:true}
        - key: FedAuthAuthParamValidationParams
          value: ${CONFIG_FED_AUTH_BANKID_PARAM_VALIDATION_PARAMS:`state##nonce##scope##prompt`}
        - key: FedAuthAuthMethodsNoteName
          value: ${CONFIG_FED_AUTH_BANKID_AUTH_METHODS_NOTE_NAME:`authentication_methods`}
        - key: FedAuthNotEligibleErrorCode
          value: ${CONFIG_FED_AUTH_NOT_ELIBIGLE_ERROR_CODE:`user_not_eligible`}
        - key: FedAuthNotEligibleDefaultErrorMessage
          value: ${CONFIG_FED_AUTH_NOT_ELIBIGLE_DEFAULT_ERROR_MESSAGE:`Insufficient user data or age restriction`}
    # NIA plugin uses the same set of properties and values as plugin for federated authentication
    nia-authenticator-config:
      properties:
        - key: FedAuthBackendPublicUrl
          value: ${CLIENTS_APIM:`http://host.docker.internal:8155`}
        - key: FedAuthBackendApiUrl
          value: ${CLIENTS_PID_FED_AUTHENTICATION:`http://host.docker.internal:8155`}
        - key: FedAuthBackendLoginPath
          value: ${CONFIG_FED_AUTH_LOGIN_PATH:`public/v1/login/init`}
        - key: FedAuthBackendVerifyPath
          value: ${CONFIG_FED_AUTH_VERIFY_PATH:`keycloak/v1/verify`}
    condition-loa1:
      properties:
        - key: loa-condition-level
          value: ${CONFIG_CONDITION_LOA_LOA1_LEVEL:`1`}
        - key: loa-max-age
          value: ${CONFIG_CONDITION_LOA_LOA1_MAX_AGE:`0`}
    condition-loa2:
      properties:
        - key: loa-condition-level
          value: ${CONFIG_CONDITION_LOA_LOA2_LEVEL:`2`}
        - key: loa-max-age
          value: ${CONFIG_CONDITION_LOA_LOA2_MAX_AGE:`0`}
    verify-auth-methods-authenticator-loa2:
      properties:
        - key: AuthMethodsListRequired
          value: ${CONFIG_VERIFY_AUTH_METHODS_LOA2_METHODS_REQUIRED:`mfa##swk`}
        - key: AuthMethodsNoteName
          value: ${CONFIG_VERIFY_AUTH_METHODS_LOA2_NOTE_NAME:`authentication_methods`}
    verify-auth-methods-authenticator-loa3:
      properties:
        - key: AuthMethodsListRequired
          value: ${CONFIG_VERIFY_AUTH_METHODS_LOA3_METHODS_REQUIRED:`mfa##swk`}
        - key: AuthMethodsListAlternative
          value: ${CONFIG_VERIFY_AUTH_METHODS_LOA3_METHODS_ALTERNATIVE:`pin##fpt`}
        - key: AuthMethodsNoteName
          value: ${CONFIG_VERIFY_AUTH_METHODS_LOA3_NOTE_NAME:`authentication_methods`}

micronaut:
  application:
    name: keycloak-migration

  server:
    port: ${SERVER_PORT:7080}
  router:
    static-resources:
      swagger:
        paths: classpath:openapi
        mapping: /swagger/**
      swagger-ui:
        paths: classpath:openapi
        mapping: /swagger-ui/**

  metrics:
    enabled: true
    export:
      prometheus:
        descriptions: true
        enabled: true
        step: PT1M

  http:
    services:
      org-keycloak-admin-v19-client:
        url: ${CLIENTS_ORG_KEYCLOAK_CLIENT_URL:`http://host.docker.internal:8888`}
        read-timeout: ${CLIENTS_ORG_KEYCLOAK_READ_TIMEOUT:30s}
      org-keycloak-openidconnect-v19-client:
        url: ${CLIENTS_ORG_KEYCLOAK_CLIENT_URL:`http://host.docker.internal:8888`}
        read-timeout: ${CLIENTS_ORG_KEYCLOAK_READ_TIMEOUT:30s}

  security:
    enabled: true
    endpoints:
      login:
        enabled: false
    intercept-url-map:
      - pattern: /**
        access:
          - isAnonymous()

endpoints:
  prometheus:
    sensitive: false
  liquibase:
    enabled: true
    sensitive: false
  caches:
    enabled: true
    sensitive: false
  health:
    details-visible: anonymous

logger:
  loki-enabled: ${LOKI_ENABLED:false}
  console-json:
    enabled: ${CONSOLE_JSON_ENABLED:false}
