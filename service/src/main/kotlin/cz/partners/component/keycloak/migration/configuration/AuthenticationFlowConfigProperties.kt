package cz.partners.component.keycloak.migration.configuration

import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import io.micronaut.core.annotation.Introspected

@EachProperty("authentication-flows.config")
class AuthenticationFlowConfigProperties(
    @param:Parameter var name: String,
) {
    var properties: List<ConfigProperty> = mutableListOf()
}

@Introspected
data class ConfigProperty(
    val key: String,
    val value: String,
)
