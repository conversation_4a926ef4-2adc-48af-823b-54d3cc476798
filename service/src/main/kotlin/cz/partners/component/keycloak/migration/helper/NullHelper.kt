package cz.partners.component.keycloak.migration.helper

import arrow.core.rightIfNotNull
import cz.partners.component.keycloak.migration.exception.NotFoundAttributeException
import org.keycloak.admin.v19.model.ClientRepresentation
import org.keycloak.admin.v19.model.ClientScopeRepresentation
import org.keycloak.admin.v19.model.RoleRepresentation
import org.keycloak.admin.v19.model.UserRepresentation

fun ClientRepresentation.getId() = id.rightIfNotNull { NotFoundAttributeException("id", "ClientRepresentation", name) }

fun ClientScopeRepresentation.getId() = id.rightIfNotNull { NotFoundAttributeException("id", "ClientScopeRepresentation", name) }

fun UserRepresentation.getId() = id.rightIfNotNull { NotFoundAttributeException("id", "UserRepresentation", username) }

fun RoleRepresentation.getId() = id.rightIfNotNull { NotFoundAttributeException("id", "RoleRepresentation", name) }
