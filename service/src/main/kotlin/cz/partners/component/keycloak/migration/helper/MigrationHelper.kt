package cz.partners.component.keycloak.migration.helper

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import cz.pbktechnology.platform.common.client.ExternalSystemException
import cz.pbktechnology.platform.common.exception.PbkException
import io.micronaut.http.HttpStatus

suspend fun <T> runIdempotent(action: suspend () -> Either<PbkException, T>): Either<PbkException, T?> =
    action.invoke().fold(
        {
            if ((it.cause as? ExternalSystemException)?.cause?.status == HttpStatus.CONFLICT) {
                null.right()
            } else {
                it.left()
            }
        },
        { it.right() },
    )
