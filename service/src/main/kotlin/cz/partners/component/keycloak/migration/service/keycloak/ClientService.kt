package cz.partners.component.keycloak.migration.service.keycloak

import arrow.core.Either
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlow
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlowDeclaration
import cz.partners.component.keycloak.migration.configuration.ClientAssignClientScopesConfiguration
import cz.partners.component.keycloak.migration.configuration.ClientOpenIdConfiguration
import cz.partners.component.keycloak.migration.configuration.ClientOpenIdProperties
import cz.partners.component.keycloak.migration.configuration.ClientSamlConfiguration
import cz.partners.component.keycloak.migration.configuration.ClientSamlProperties
import cz.partners.component.keycloak.migration.configuration.ClientScopeType
import cz.partners.component.keycloak.migration.configuration.SamlSignatureKeyNameType
import cz.partners.component.keycloak.migration.exception.ClientCreatedByOtherServiceException
import cz.partners.component.keycloak.migration.exception.NotFoundClientException
import cz.partners.component.keycloak.migration.helper.getId
import cz.partners.component.keycloak.migration.helper.runIdempotent
import cz.partners.component.keycloak.migration.token.KeycloakClientOriginCheckConfiguration
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import jakarta.inject.Singleton
import mu.KotlinLogging
import org.keycloak.admin.v19.client.ClientsApiClient
import org.keycloak.admin.v19.model.AuthenticationFlowOverride
import org.keycloak.admin.v19.model.ClientRepresentation
import org.keycloak.admin.v19.model.Protocol
import org.keycloak.admin.v19.model.SamlSignatureKeyName

@Singleton
class ClientService(
    private val clientsApiClient: ClientsApiClient,
    private val roleService: RoleService,
    private val clientScopeService: ClientScopeService,
    private val authFlowService: AuthenticationFlowService,
    private val keycloakClientOriginCheckConfiguration: KeycloakClientOriginCheckConfiguration,
    private val authenticationFlowDeclaration: AuthenticationFlowDeclaration,
    clientOpenIdPropertiesList: MutableList<ClientOpenIdProperties>,
    clientSamlPropertiesList: MutableList<ClientSamlProperties>,
) {
    private val logger = KotlinLogging.logger { }

    private val clientOpenIdPropertiesMap = clientOpenIdPropertiesList.associateBy { it.name }
    private val clientSamlPropertiesMap = clientSamlPropertiesList.associateBy { it.name }

    suspend fun createOpenIdClient(
        realm: String,
        clientOpenIdConfiguration: ClientOpenIdConfiguration,
    ): Either<PbkException, Unit> =
        bind {
            logger.info {
                "Creating Client name [${clientOpenIdConfiguration.name}] and client-id " +
                    "[${clientOpenIdConfiguration.clientId}] in realm $realm"
            }
            with(clientOpenIdConfiguration) {
                val postLogoutRedirectUris =
                    redirectLogoutUrisRequired
                        .takeIf { redirectLogoutUrisRequired }
                        .let { clientOpenIdPropertiesMap[clientId]?.redirectLogoutUris?.transformStringToKeycloakFormat() }
                        ?: ""

                !upsertClient(
                    realm = realm,
                    clientRepresentation =
                        ClientRepresentation(
                            clientId = clientId,
                            name = name,
                            description = description,
                            enabled = true,
                            standardFlowEnabled = standardFlowEnabled,
                            directAccessGrantsEnabled = directAccessGrantsEnabled,
                            implicitFlowEnabled = implicitFlowEnabled,
                            authorizationServicesEnabled = authorizationServicesEnabled,
                            serviceAccountsEnabled = serviceAccountsEnabled || authorizationServicesEnabled,
                            publicClient = publicClient,
                            secret = clientOpenIdPropertiesMap[clientId]?.secret,
                            protocol = Protocol.OPENID_MINUS_CONNECT,
                            rootUrl = if (rootUrlRequired) clientOpenIdPropertiesMap[clientId]?.rootUrl else "",
                            baseUrl = if (baseUrlRequired) clientOpenIdPropertiesMap[clientId]?.baseUrl else "",
                            redirectUris =
                                if (redirectUrisRequired) {
                                    clientOpenIdPropertiesMap[clientId]?.redirectUris?.splitToList()
                                } else {
                                    emptyList()
                                },
                            attributes =
                                buildMap {
                                    put(POST_LOGOUT_REDIRECT_URIS_ATTRIBUTE_NAME, postLogoutRedirectUris)
                                    clientOpenIdPropertiesMap[clientId]?.offlineSession?.let { offlineSessionDuration ->
                                        put(
                                            CLIENT_OFFLINE_SESSION_IDLE_NAME,
                                            offlineSessionDuration.seconds.toInt().toString(),
                                        )
                                    }
                                    clientOpenIdPropertiesMap[clientId]?.offlineSessionMax?.let { offlineSessionMaxDuration ->
                                        put(
                                            CLIENT_OFFLINE_SESSION_MAX_LIFESPAN_NAME,
                                            offlineSessionMaxDuration.seconds.toInt().toString(),
                                        )
                                    }
                                },
                            webOrigins =
                                if (webOriginsRequired) {
                                    clientOpenIdPropertiesMap[clientId]?.webOrigins?.splitToList()
                                } else {
                                    emptyList()
                                },
                            adminUrl = if (adminUrlRequired) clientOpenIdPropertiesMap[clientId]?.adminUrl else "",
                            authenticationFlowBindingOverrides =
                                if (authenticationFlowRequired) {
                                    clientOpenIdPropertiesMap[clientId]
                                        ?.authenticationFlow
                                        ?.takeIf { isAuthFlowValid(it, authenticationFlowDeclaration.authenticationFlows) }
                                        ?.let { getAuthenticationFlowOverrides(realm, it) }
                                        ?: AuthenticationFlowOverride()
                                } else {
                                    AuthenticationFlowOverride()
                                },
                        ),
                )

                val clientRepresentation = !getClient(realm, clientId)

                // Create roles
                !roleService.createClientRoles(realm, clientRepresentation, roles)

                // get current scopes
                val currentScopes = getCurrentDefaultScopes(clientRepresentation) + getCurrentOptionalScopes(clientRepresentation)

                // Assign client scopes
                !assignClientScopes(realm, !clientRepresentation.getId(), assignClientScopes, currentScopes)
            }
        }

    private fun getCurrentOptionalScopes(clientRepresentation: ClientRepresentation) =
        clientRepresentation.optionalClientScopes?.associate { it to ClientScopeType.OPTIONAL }.orEmpty()

    private fun getCurrentDefaultScopes(clientRepresentation: ClientRepresentation) =
        clientRepresentation.defaultClientScopes?.associate { it to ClientScopeType.DEFAULT }.orEmpty()

    suspend fun createSamlClient(
        realm: String,
        clientSamlConfiguration: ClientSamlConfiguration,
    ): Either<PbkException, Unit> =
        bind {
            with(clientSamlConfiguration) {
                !upsertClient(
                    realm = realm,
                    clientRepresentation =
                        ClientRepresentation(
                            clientId = name,
                            name = name,
                            description = description,
                            enabled = true,
                            serviceAccountsEnabled = false,
                            implicitFlowEnabled = false,
                            standardFlowEnabled = true,
                            directAccessGrantsEnabled = true,
                            publicClient = true,
                            secret = null,
                            protocol = Protocol.SAML,
                            adminUrl = if (masterSamlProcessingUrlRequired) clientSamlPropertiesMap[name]?.masterSamlProcessingUrl else "",
                            attributes =
                                mapOf(
                                    SAML_SIGN_KEY_TRANSFORMER_DOLLAR_ATTRIBUTE_NAME to
                                        samlSignatureKeyName.toApiSamlSignatureKey().toString(),
                                    SAML_SIGN_KEY_TRANSFORMER_ATTRIBUTE_NAME to samlSignatureKeyName.toApiSamlSignatureKey().toString(),
                                ),
                        ),
                )

                val clientRepresentation = !getClient(realm, name)

                // Create roles
                !roleService.createClientRoles(realm, clientRepresentation, roles)
            }
        }

    private suspend fun getClientOrNull(
        realm: String,
        name: String,
    ): Either<PbkException, ClientRepresentation?> =
        bind {
            (!clientsApiClient.realmClientsGet(realm, name)).firstOrNull()
        }

    suspend fun getClient(
        realm: String,
        name: String,
    ): Either<PbkException, ClientRepresentation> =
        bind {
            !getClientOrNull(realm, name) ?: bindException { NotFoundClientException(name) }
        }

    suspend fun getClients(realm: String): Either<PbkException, List<ClientRepresentation>> = clientsApiClient.realmClientsGet(realm)

    suspend fun getClientServiceUserId(
        realm: String,
        clientId: String,
    ): Either<PbkException, String> =
        bind {
            !(!clientsApiClient.realmClientsClientIdServiceAccountUserGet(realm, clientId)).getId()
        }

    private suspend fun assignClientScopes(
        realm: String,
        id: String,
        assignClientScopes: MutableSet<ClientAssignClientScopesConfiguration>,
        currentScopes: Map<String, ClientScopeType>,
    ): Either<PbkException, Unit> =
        bind {
            assignClientScopes.forEach { assignClientScope ->
                val clientScopeRepresentation = !clientScopeService.getClientScope(realm, assignClientScope.name)
                val clientScopeId = !clientScopeRepresentation.getId()

                if (currentScopes[assignClientScope.name] != null && currentScopes[assignClientScope.name] != assignClientScope.type) {
                    logger.info {
                        "Client scope [${assignClientScope.name}] type is changed from [${currentScopes[assignClientScope.name]}] to " +
                            "[${assignClientScope.type}] for client [$id] in realm [$realm], deleting before applying scope update"
                    }
                    // if client scope is changed, remove it first, since it is not possible to change the type
                    !runIdempotent {
                        clientsApiClient.realmClientsClientIdDefaultClientScopesClientScopeIdDelete(
                            realm = realm,
                            clientId = id,
                            clientScopeId = clientScopeId,
                        )
                    }
                }

                when (assignClientScope.type) {
                    ClientScopeType.DEFAULT -> {
                        !runIdempotent {
                            clientsApiClient.realmClientsClientIdDefaultClientScopesClientScopeIdPut(
                                realm = realm,
                                clientId = id,
                                clientScopeId = clientScopeId,
                            )
                        }
                    }

                    ClientScopeType.OPTIONAL -> {
                        !runIdempotent {
                            clientsApiClient.realmClientsClientIdOptionalClientScopesClientScopeIdPut(
                                realm = realm,
                                clientId = id,
                                clientScopeId = clientScopeId,
                            )
                        }
                    }

                    ClientScopeType.NONE -> Unit // client scope is already removed in previous step
                }
            }
        }

    private suspend fun upsertClient(
        realm: String,
        clientRepresentation: ClientRepresentation,
    ): Either<PbkException, Unit> =
        bind {
            val attributes = clientRepresentation.attributes ?: emptyMap()
            val clientRepresentationWithSource =
                clientRepresentation.copy(
                    attributes =
                        attributes +
                            mapOf(
                                keycloakClientOriginCheckConfiguration.attributeName to
                                    keycloakClientOriginCheckConfiguration.originName,
                            ),
                )

            (!getClientOrNull(realm, clientRepresentation.clientId))
                ?.let {
                    val originAttribute = it.attributes?.get(keycloakClientOriginCheckConfiguration.attributeName)
                    val wasCreatedByUs = originAttribute == keycloakClientOriginCheckConfiguration.originName
                    if (!wasCreatedByUs) {
                        bindException { ClientCreatedByOtherServiceException(it.name ?: "", originAttribute) }
                    }

                    !clientsApiClient.realmClientsClientIdPut(
                        realm = realm,
                        clientId = !it.getId(),
                        clientRepresentation = clientRepresentationWithSource.copy(id = it.id),
                    )
                    logger.info { "Client secret updated [${it.name}] in realm [$realm]" }
                }
                ?: let {
                    !runIdempotent {
                        clientsApiClient.realmClientsPost(
                            realm = realm,
                            clientRepresentation = clientRepresentationWithSource,
                        )
                    }
                    logger.info { "Client created [${clientRepresentationWithSource.name}] in realm [$realm]" }

                    // This code is here because, for some unknown reason,
                    // when creating a client via the POST method, the webOrigins do not get propagated to KC20.
                    // They are saved only when calling the PUT method.

                    (!clientsApiClient.realmClientsGet(realm))
                        .find { it.name == clientRepresentationWithSource.name }
                        ?.let {
                            !clientsApiClient.realmClientsClientIdPut(
                                realm = realm,
                                clientId = !it.getId(),
                                clientRepresentation = clientRepresentationWithSource.copy(id = it.id),
                            )
                            logger.info { "Client secret updated [${it.name}] in realm [$realm]" }
                        }
                }
        }

    private fun String?.transformStringToKeycloakFormat(): String? = this?.splitToList()?.joinToString("##")

    private suspend fun getAuthenticationFlowOverrides(
        realm: String,
        authenticationFlowAlias: String,
    ): AuthenticationFlowOverride =
        authFlowService
            .getAuthenticationFlowId(realm, authenticationFlowAlias)
            .map { AuthenticationFlowOverride(directGrant = it, browser = it) }
            .fold(
                { AuthenticationFlowOverride() },
                { it },
            )

    private fun String?.splitToList(): List<String>? = this?.split("|")?.map { it.trim() }

    private fun SamlSignatureKeyNameType.toApiSamlSignatureKey(): SamlSignatureKeyName = SamlSignatureKeyName.valueOf(this.name)

    private fun isAuthFlowValid(
        flowName: String,
        authFlows: Collection<AuthenticationFlow>,
    ) = authFlows.firstOrNull { it.name == flowName }?.valid ?: false

    companion object {
        const val POST_LOGOUT_REDIRECT_URIS_ATTRIBUTE_NAME = "post.logout.redirect.uris"
        const val SAML_SIGN_KEY_TRANSFORMER_DOLLAR_ATTRIBUTE_NAME = "saml\$server\$signature\$keyinfo\$xmlSigKeyInfoKeyNameTransformer"
        const val SAML_SIGN_KEY_TRANSFORMER_ATTRIBUTE_NAME = "saml.server.signature.keyinfo.xmlSigKeyInfoKeyNameTransformer"
        const val CLIENT_OFFLINE_SESSION_IDLE_NAME = "client.offline.session.idle.timeout"
        const val CLIENT_OFFLINE_SESSION_MAX_LIFESPAN_NAME = "client.offline-session.max.lifespan"
    }
}
