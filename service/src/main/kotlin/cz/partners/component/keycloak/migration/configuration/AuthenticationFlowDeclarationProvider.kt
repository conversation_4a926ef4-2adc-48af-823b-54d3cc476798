package cz.partners.component.keycloak.migration.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import com.fasterxml.jackson.module.kotlin.readValue
import cz.partners.component.keycloak.migration.exception.DuplicateAuthenticationFlowExecutionAliasException
import cz.partners.component.keycloak.migration.exception.DuplicateAuthenticationFlowNameException
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton
import java.io.File

@Factory
class AuthenticationFlowDeclarationProvider {
    /**
     * Bean containing authentication flow declarations. Configuration is read from configuration file specified by [configurationFileName].
     *
     * @param configurationFileName YAML Configuration file containing auth flow definitions
     */
    @Singleton
    fun authenticationFlowsDeclaration(
        @Value("\${authentication-flows-configuration-file}") configurationFileName: String,
    ): AuthenticationFlowDeclaration {
        val mapper = ObjectMapper(YAMLFactory()).findAndRegisterModules()

        val authFlows =
            File(configurationFileName)
                .takeIf { it.exists() }
                ?.inputStream()
                ?.use { mapper.readValue<AuthenticationFlowDeclaration>(it) }
                ?: AuthenticationFlowDeclaration(emptyList())

        // perform config file validations
        // authentication flow names and aliases have to be unique
        val flowNames = mutableSetOf<String>()
        val executionAliases = mutableSetOf<String>()
        authFlows.authenticationFlows.forEach {
            if (!flowNames.add(it.name)) {
                throw DuplicateAuthenticationFlowNameException(it.name)
            }

            collectAliases(it.executions, executionAliases)
        }

        return authFlows
    }

    private fun collectAliases(
        executions: List<AuthenticationFlowExecution>,
        aliases: MutableSet<String>,
    ) {
        executions.forEach { e ->
            // when the element is already present in the set, false is returned
            if (!aliases.add(e.alias)) {
                throw DuplicateAuthenticationFlowExecutionAliasException(e.alias)
            }

            e.executions?.let { collectAliases(it, aliases) }
        }
    }
}
