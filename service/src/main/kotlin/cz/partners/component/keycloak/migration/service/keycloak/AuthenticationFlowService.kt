package cz.partners.component.keycloak.migration.service.keycloak

import arrow.core.Either
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlow
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlowConfigProperties
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlowDeclaration
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlowExecution
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlowExecutionConfiguration
import cz.partners.component.keycloak.migration.configuration.AuthenticatorFlowProviderId
import cz.partners.component.keycloak.migration.exception.IllegalStateException
import cz.partners.component.keycloak.migration.exception.NotFoundAuthenticationFlowException
import cz.partners.component.keycloak.migration.exception.NotFoundAuthenticationFlowExecutionException
import cz.partners.component.keycloak.migration.helper.runIdempotent
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import jakarta.inject.Singleton
import mu.KotlinLogging
import org.keycloak.admin.v19.client.AuthenticationFlowApiClient
import org.keycloak.admin.v19.client.ProvidersApiClient
import org.keycloak.admin.v19.model.AuthenticationExecutionInfoRepresentation
import org.keycloak.admin.v19.model.AuthenticationFlowExecutionRequirement
import org.keycloak.admin.v19.model.AuthenticationFlowRepresentation
import org.keycloak.admin.v19.model.CreateAuthenticationFlowExecution
import org.keycloak.admin.v19.model.CreateAuthenticationFlowSubflow
import org.keycloak.admin.v19.model.UpdateAuthenticationFlowExecutionConfig
import java.util.UUID

@Singleton
class AuthenticationFlowService(
    private val authFlowClient: AuthenticationFlowApiClient,
    private val providersApiClient: ProvidersApiClient,
    private val configPropertiesList: List<AuthenticationFlowConfigProperties>,
    private val authenticationFlowDeclaration: AuthenticationFlowDeclaration,
) {
    private val logger = KotlinLogging.logger { }

    /**
     * A map containing key-value configurations which are mapped to individual authentication flow executions
     * using the config name ([AuthenticationFlowExecutionConfiguration.configurationRef]).
     *
     * Map keys contain config names, values are key-value maps representing the configuration itself
     */
    private val configProperties: Map<String, Map<String, String>> =
        configPropertiesList.associate { it.name to it.properties.associate { p -> p.key to p.value } }

    suspend fun getAuthenticationFlowId(
        realm: String,
        flowAlias: String,
    ): Either<PbkException, UUID?> =
        bind {
            val flows = !authFlowClient.getAuthenticationFlows(realm)

            val flowId =
                flows
                    .filter { it.alias == flowAlias }
                    .map { it.id }
                    .firstOrNull()

            flowId.also { it ?: logger.warn { "Could not find an authentication flow with alias $flowAlias" } }
        }

    /**
     * Creates a new authentication flow in the specified [realm].
     * Flow is created under name specified by [authFlowName]. This parameter is also used to lookup flow declaration in `authentication-flows.yaml`
     *
     * @param realm Realm name to create the flow in
     * @param authFlowName Name of the authentication flow
     */
    suspend fun createAuthenticationFlow(
        realm: String,
        authFlowName: String,
    ): Either<PbkException, Unit> =
        bind {
            val flowConfiguration = authenticationFlowDeclaration.getAuthenticationFlow(authFlowName)
            !runIdempotent {
                authFlowClient.createAuthenticationFlow(
                    realm = realm,
                    authenticationFlowRepresentation =
                        AuthenticationFlowRepresentation(
                            alias = flowConfiguration.name,
                            description = flowConfiguration.description,
                            providerId = flowConfiguration.type.code,
                            topLevel = true,
                            builtIn = false,
                        ),
                )
            }

            val retrievedFlow =
                (!authFlowClient.getAuthenticationFlows(realm))
                    .firstOrNull { it.alias == flowConfiguration.name }
                    ?: bindException { NotFoundAuthenticationFlowException(flowConfiguration.name) }

            !updateFlowExecutions(realm, retrievedFlow, flowConfiguration)
            !updateFlowExecutionsConfig(realm, retrievedFlow.alias, flowConfiguration.executions)
        }

    /**
     * Recursively updates configuration of executions within the flow tree.
     *
     * @param realm Realm name the flow is created in
     * @param parentFlowAlias Flow alias used as a root node in recursion
     * @param executionsConfiguration Configuration of child executions of flow [parentFlowAlias]
     */
    private suspend fun updateFlowExecutionsConfig(
        realm: String,
        parentFlowAlias: String,
        executionsConfiguration: List<AuthenticationFlowExecution>,
    ): Either<PbkException, Unit> =
        bind {
            // get immediate flow's executions
            val directExecutions =
                !authFlowClient.getExecutionsForAuthenticationFlow(realm, parentFlowAlias).map { e ->
                    e.filter { it.level == 0 }
                }

            executionsConfiguration.forEachIndexed { executionConfigurationIndex, executionConfiguration ->
                directExecutions
                    .find { it.priority == executionConfigurationIndex }
                    ?.let { execution ->
                        if (executionConfiguration.type.isFlow) {
                            executionConfiguration.executions?.let { childExecutions ->
                                // recursively update config in a subflow
                                // subflows themselves do not have configuration
                                // TODO: PTS-811 tailrec
                                !updateFlowExecutionsConfig(realm, execution.displayName!!, childExecutions)
                            }
                        } else if (executionConfiguration.config != null) {
                            // update configuration in leaf node
                            !authFlowClient.upsertAuthenticationFlowExecutionConfig(
                                realm,
                                execution.id,
                                UpdateAuthenticationFlowExecutionConfig(
                                    alias = executionConfiguration.alias,
                                    config =
                                        executionConfiguration.config
                                            // The ?. is deliberate to let in only not-null configs
                                            ?.let {
                                                configProperties[it]
                                                    ?: bindException { IllegalStateException("Invalid auth flow config reference") }
                                            } ?: emptyMap(),
                                ),
                            )
                        }
                    }
            }
        }

    /**
     * Creates or updates definition of flow's executions. This function handles the first level of executions where the same recursive logic used in deeper levels cannot be used.
     *
     * If the existing definition is the same as configured (at the whole level), no update/recreate is performed to avoid unnecessary periodic modification of flows.
     *
     * @param realm Realm name the flow is created in
     * @param flow Main flow definition in Keycloak
     * @param configuration Main flow configuration
     */
    private suspend fun updateFlowExecutions(
        realm: String,
        flow: AuthenticationFlowRepresentation,
        configuration: AuthenticationFlow,
    ): Either<PbkException, Unit> =
        bind {
            val providers = !providersApiClient.getAuthenticatorProviders(realm)
            val availableProvidersIds = providers.map { it.id }.toSet()
            val areAllProvidersAvailable =
                getAreAllProvidersAvailable(
                    configuration.executions.flatMapTo(mutableSetOf()) { collectAuthenticationProvidersIds(it) },
                    availableProvidersIds = availableProvidersIds,
                )

            if (!areAllProvidersAvailable) {
                logger.warn { "Executions of ${configuration.name} are not valid. One or more of referenced providers do not exist" }
                logger.warn { "Available providers: $availableProvidersIds" }
                // TODO: PTS-811 throw exception
                configuration.valid = false
            }

            val rootFlowId =
                flow.id
                    ?: bindException {
                        IllegalStateException(
                            "Authentication execution does not contain ID. This is unexpected and should not happen.",
                        )
                    }

            // when the executions at the immediate level do not fully correspond with configuration, just delete all executions and recreate them as new
            // this behaviour is maintained at all other levels as well
            if (!compareChildExecutions(realm, configuration.name, configuration.executions).bind()) {
                logger.info(
                    "Top level authentication flow executions are different from the configuration of flow [${configuration.name}]. Removing and recreating them",
                )
                authFlowClient
                    .getExecutionsForAuthenticationFlow(realm, configuration.name)
                    .map { e -> e.filter { it.level == 0 } }
                    .bind()
                    .map { it.id }
                    .forEach { !authFlowClient.deleteAuthenticationFlowExecution(realm, it) }
            }

            configuration.executions
                .takeIf { areAllProvidersAvailable }
                ?.mapIndexed { i, e ->
                    !createAuthenticationFlowExecution(
                        realm,
                        configuration.name,
                        e,
                        i,
                        rootFlowId,
                        flow.alias,
                    )
                }
        }

    /**
     * Together with [AuthenticationFlowService.createExecutionSubflow] recursively creates the whole flow execution tree.
     * The logic differs between subflows containing other executions (can be considered internal node) and simple executions (leaf).
     *
     * Similar to login at level 1, when the existing definition of execution corresponds to current configuration, the execution is not modified. Otherwise, it is updated/recreated.
     * Subflows have to have all immediate children corresponding to current configuration, otherwise the whole subflow is deleted.
     *
     * @param realm Realm name the flow is created in
     * @param rootFlowAlias Flow alias of the main flow
     * @param execution Currently processed execution (node)
     * @param order Order of the execution in the current level. Used for specifying priority which handles the order of execution in a flow
     * @param parentFlowId ID of the parent of the currently processed execution
     * @param parentFlowAlias Alias of the parent of the currently processed execution
     */
    private suspend fun createAuthenticationFlowExecution(
        realm: String,
        rootFlowAlias: String,
        execution: AuthenticationFlowExecution,
        order: Int,
        parentFlowId: UUID,
        parentFlowAlias: String,
    ): Either<PbkException, Unit> =
        bind {
            // subflow
            if (execution.type.isFlow) {
                // contains check whether the existing definition corresponds to current configuration and therefore a modification can be skipped
                !createExecutionSubflow(
                    realm = realm,
                    rootFlowAlias = rootFlowAlias,
                    execution = execution,
                    order = order,
                    parentFlowAlias = parentFlowAlias,
                )
                // simple execution (leaf)
            } else {
                val existingExecution =
                    !authFlowClient.getExecutionsForAuthenticationFlow(realm, parentFlowAlias).map { e ->
                        e.find {
                            it.priority ==
                                order
                        }
                    }
                val skipCreation = existingExecution?.isEqualExecution(execution) ?: false
                if (skipCreation) return@bind

                existingExecution?.id?.let {
                    !authFlowClient.editAuthenticationFlowExecution(
                        realm,
                        rootFlowAlias,
                        AuthenticationExecutionInfoRepresentation(
                            id = it,
                            providerId = execution.type.providerId,
                            authenticationFlow = execution.type.isFlow,
                            requirement = AuthenticationFlowExecutionRequirement.valueOf(execution.requirement.name),
                            priority = order,
                        ),
                    )
                } ?: run {
                    !authFlowClient.createAuthenticationFlowExecution(
                        realm,
                        CreateAuthenticationFlowExecution(
                            authenticator = execution.type.providerId,
                            authenticatorFlow = execution.type.isFlow,
                            requirement = AuthenticationFlowExecutionRequirement.valueOf(execution.requirement.name),
                            priority = order,
                            parentFlow = parentFlowId,
                        ),
                    )
                }
            }
        }

    /**
     * Compares child executions in the specified subflow with configuration.
     * If the configurations differ, the subflow is deleted and false is returned to indicate that it is necessary to recreate the subflow.
     * true is returned when configuration of all child executions is the same.
     *
     * @param realm Realm name the flow is created in
     * @param parentFlowAlias Alias of the parent of the currently processed execution
     * @param execution Currently processed execution (node)
     *
     * @return true if all immediate executions correspond to current configuration, false otherwise
     */
    private suspend fun compareSubflowExecutions(
        realm: String,
        parentFlowAlias: String,
        execution: AuthenticationFlowExecution,
    ): Either<PbkException, Boolean> =
        bind {
            val executions = !authFlowClient.getExecutionsForAuthenticationFlow(realm, parentFlowAlias)
            // in executions there is no alias but displayName contains the same value
            val existingExecution = executions.find { it.displayName == execution.alias }

            logger.info("Found existing subflow execution with alias [${execution.alias}]: [$existingExecution]")

            if (existingExecution == null) {
                logger.info("Execution with alias [${execution.alias}] does not exist yet. Proceeding with creation")
                return@bind false
            }

            val sameExecutions =
                !compareChildExecutions(
                    realm,
                    execution.alias,
                    execution.executions ?: emptyList(),
                )

            if (!sameExecutions) {
                !authFlowClient.deleteAuthenticationFlowExecution(realm, existingExecution.id)
                return@bind false
            }

            return@bind true
        }

    /**
     * Extracted logic of comparison of immediate child executions of a subflow.
     *
     * @param realm Realm name the flow is created in
     * @param flowAlias Alias of the currently processed subflow
     * @param childExecutionsConfig Configuration of immediate child executions
     *
     * @return true if all child executions correspond to current configuration, false otherwise
     */
    private suspend fun compareChildExecutions(
        realm: String,
        flowAlias: String,
        childExecutionsConfig: List<AuthenticationFlowExecution>,
    ): Either<PbkException, Boolean> =
        bind {
            val childExecutions =
                !authFlowClient.getExecutionsForAuthenticationFlow(realm, flowAlias).map { e ->
                    e.filter { it.level == 0 }
                }

            if (childExecutions.size != childExecutionsConfig.size) {
                logger.info(
                    "The number of child executions of subflow [$flowAlias] (${childExecutions.size}) differ from configuration (${childExecutionsConfig.size}). Deleting the subflow and creating a new one",
                )
                return@bind false
            }

            val anyExecutionDifferent =
                childExecutions.any {
                    val executionsEqual =
                        if (it.authenticationFlow == true) {
                            // subflow
                            childExecutionsConfig
                                .getOrNull(it.priority ?: -1)
                                ?.let { e -> it.isEqualSubflowExecution(e) } ?: false
                        } else {
                            // simple execution
                            childExecutionsConfig.getOrNull(it.priority ?: -1)?.let { e -> it.isEqualExecution(e) }
                                ?: false
                        }
                    !executionsEqual
                }

            if (anyExecutionDifferent) {
                logger.info(
                    "Child executions of subflow [$flowAlias] differ from configuration. Deleting the subflow and creating a new one",
                )
                return@bind false
            }

            logger.info("Child executions of subflow [$flowAlias] correspond to configuration. Skipping update of the subflow")

            return@bind true
        }

    /**
     * Comparison of simple executions - existing Keycloak definition and current configuration.
     *
     * @param execution Current configuration
     * @return true if the definition correspond to current configuration, false otherwise
     */
    private fun AuthenticationExecutionInfoRepresentation.isEqualExecution(execution: AuthenticationFlowExecution): Boolean {
        val result =
            providerId == execution.type.providerId &&
                (authenticationFlow ?: false) == execution.type.isFlow &&
                requirement.name == execution.requirement.name

        logger.info("Execution comparison of [$this] and [$execution]: $result")

        return result
    }

    /**
     * Comparison of subflows - existing Keycloak definition and current configuration.
     *
     * @param execution Current configuration
     * @return true if the definition correspond to current configuration, false otherwise
     */
    private fun AuthenticationExecutionInfoRepresentation.isEqualSubflowExecution(execution: AuthenticationFlowExecution): Boolean {
        val result =
            (authenticationFlow ?: false) == execution.type.isFlow &&
                requirement.name == execution.requirement.name &&
                displayName == execution.alias

        logger.info("Subflow comparison of [$this] and [$execution]: $result")

        return result
    }

    /**
     * Together with [AuthenticationFlowService.createAuthenticationFlowExecution] recursively creates the whole flow execution tree. This function handles creation of subflows.
     * Uses comparison function to determine whether executions need to recreated or kept as-is.
     *
     * @param realm Realm name the flow is created in
     * @param rootFlowAlias Flow alias of the main flow
     * @param execution Currently processed execution (node)
     * @param order Order of the execution in the current level. Used for specifying priority which handles the order of execution in a flow
     * @param parentFlowAlias Alias of the parent of the currently processed execution
     */
    private suspend fun createExecutionSubflow(
        realm: String,
        rootFlowAlias: String,
        execution: AuthenticationFlowExecution,
        order: Int,
        parentFlowAlias: String,
    ): Either<PbkException, Unit> =
        bind {
            val skipCreation = !compareSubflowExecutions(realm, parentFlowAlias, execution)

            if (!skipCreation) {
                val request =
                    CreateAuthenticationFlowSubflow(
                        alias = execution.alias,
                        description = execution.description,
                        type = AuthenticatorFlowProviderId.BASIC_FLOW.code,
                        priority = order,
                    )
                !authFlowClient.createAuthenticationFlowSubflow(realm, parentFlowAlias, request)
            }

            val executions = !authFlowClient.getExecutionsForAuthenticationFlow(realm, rootFlowAlias)
            val newExecution =
                // in executions there is no alias but displayName contains the same value
                executions.find { it.displayName == execution.alias }
                    ?: bindException { NotFoundAuthenticationFlowExecutionException(rootFlowAlias, execution.alias) }
            val newExecutionAlias =
                newExecution.displayName
                    ?: bindException {
                        IllegalStateException(
                            "Authentication execution does not contain alias. This is unexpected and should not happen.",
                        )
                    }
            val newExecutionFlowId =
                newExecution.flowId
                    ?: bindException {
                        IllegalStateException(
                            "Authentication execution does not contain flow ID. This is unexpected and should not happen.",
                        )
                    }

            if (!skipCreation) {
                !authFlowClient.editAuthenticationFlowExecution(
                    realm,
                    rootFlowAlias,
                    AuthenticationExecutionInfoRepresentation(
                        id = newExecution.id,
                        requirement = AuthenticationFlowExecutionRequirement.valueOf(execution.requirement.name),
                        priority = order,
                    ),
                )
            }

            execution.executions
                ?.takeIf { execution.type.isFlow }
                ?.forEachIndexed { i, e ->
                    !createAuthenticationFlowExecution(
                        realm,
                        rootFlowAlias,
                        e,
                        i,
                        newExecutionFlowId,
                        newExecutionAlias,
                    )
                }
        }

    /**
     * Function that recursively collects all different provider IDs from the whole authentication flow tree.
     *
     * @param execution Currently processed execution
     * @return Recursively collected set of different provider IDs
     */
    private fun collectAuthenticationProvidersIds(execution: AuthenticationFlowExecution): Set<String> {
        val providers =
            (execution.executions ?: emptyList()).flatMapTo(mutableSetOf()) { collectAuthenticationProvidersIds(it) }

        execution.type.providerId?.let { providers.add(it) }

        return providers
    }

    /**
     * Convenience function to compare required and available providers/authenticators using provider IDs.
     *
     * @param providerIds Required authenticators
     * @param availableProvidersIds Available authenticators
     */
    private fun getAreAllProvidersAvailable(
        providerIds: Set<String>,
        availableProvidersIds: Set<String>,
    ) = (providerIds - availableProvidersIds).isEmpty()
}
