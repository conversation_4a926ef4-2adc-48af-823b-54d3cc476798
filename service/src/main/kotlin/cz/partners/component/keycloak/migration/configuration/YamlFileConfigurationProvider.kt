package cz.partners.component.keycloak.migration.configuration

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import cz.partners.component.keycloak.migration.exception.UnableParseYamlException
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import io.micronaut.context.annotation.Value
import jakarta.inject.Singleton
import mu.KotlinLogging
import java.io.File

@Singleton
open class YamlFileConfigurationProvider(
    @Value("\${keycloak-configuration-file}") val configurationFileName: String,
    private val clientSamlPropertiesChecker: ClientSamlPropertiesChecker,
    private val clientOpenIdPropertiesChecker: ClientOpenIdPropertiesChecker,
) : ConfigurationProvider {
    private val logger = KotlinLogging.logger { }

    private val mapper = ObjectMapper(YAMLFactory()).findAndRegisterModules()

    override suspend fun getConfiguration(): Either<PbkException, KeycloakConfiguration> =
        bind {
            logger.info("Loading configuration from Yaml file: [$configurationFileName]")
            (!loadFromYaml(File(configurationFileName))).also { keycloakConfiguration ->
                !clientSamlPropertiesChecker.checkConfiguration(keycloakConfiguration)
                !clientOpenIdPropertiesChecker.checkConfiguration(keycloakConfiguration)
            }
        }

    private suspend fun loadFromYaml(file: File): Either<PbkException, KeycloakConfiguration> =
        bind {
            file
                .inputStream()
                .use {
                    mapper.readValue(it, KeycloakConfiguration::class.java)
                }.also { logger.info("Loaded: $it") }
                ?: bindException { UnableParseYamlException(file.absolutePath) }
        }
}
