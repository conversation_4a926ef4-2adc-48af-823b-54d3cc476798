package cz.partners.component.keycloak.migration.token

import io.micronaut.http.HttpResponse
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.annotation.Filter
import io.micronaut.http.filter.ClientFilterChain
import io.micronaut.http.filter.HttpClientFilter
import org.reactivestreams.Publisher

@Filter(
    patterns = [
        "/auth/admin/**",
        "/admin/**",
    ],
)
internal class KeycloakAdminApiFilter(
    private val accessTokenService: AccessTokenService,
) : HttpClientFilter {
    override fun doFilter(
        request: MutableHttpRequest<*>,
        chain: ClientFilterChain,
    ): Publisher<out HttpResponse<*>> =
        chain.proceed(
            request.bearerAuth(accessTokenService.accessToken),
        )
}
