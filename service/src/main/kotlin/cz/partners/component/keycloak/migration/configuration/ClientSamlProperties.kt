package cz.partners.component.keycloak.migration.configuration

import arrow.core.Either
import cz.partners.component.keycloak.migration.exception.MissingSamlClientsMasterSamlProcessingUrlPropertyException
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import jakarta.inject.Singleton

@EachProperty("client-saml")
class ClientSamlProperties(
    @param:Parameter var name: String,
) {
    var masterSamlProcessingUrl: String? = null
}

@Singleton
class ClientSamlPropertiesChecker(
    private val clientSamlPropertiesList: List<ClientSamlProperties>,
) {
    suspend fun checkConfiguration(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            !checkConfiguredMasterSamlProcessingUrl(keycloakConfiguration)
            // Add another checker
        }

    private suspend fun checkConfiguredMasterSamlProcessingUrl(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientSamlPropertiesList
                    .filter { it.masterSamlProcessingUrl != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsSaml }
                    .filter { it.masterSamlProcessingUrlRequired }
                    .map { it.name }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingSamlClientsMasterSamlProcessingUrlPropertyException(this) }
                }
            }
        }
}
