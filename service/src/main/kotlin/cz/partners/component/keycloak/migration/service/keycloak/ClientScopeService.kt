package cz.partners.component.keycloak.migration.service.keycloak

import arrow.core.Either
import cz.partners.component.keycloak.migration.configuration.ClientScopeConfiguration
import cz.partners.component.keycloak.migration.configuration.ClientScopeType
import cz.partners.component.keycloak.migration.exception.NotFoundClientScopeException
import cz.partners.component.keycloak.migration.helper.getId
import cz.partners.component.keycloak.migration.helper.runIdempotent
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import jakarta.inject.Singleton
import mu.KotlinLogging
import org.keycloak.admin.v19.client.ClientScopesApiClient
import org.keycloak.admin.v19.client.RealmsAdminApiClient
import org.keycloak.admin.v19.model.ClientScopeRepresentation
import org.keycloak.admin.v19.model.ClientScopeRepresentationAttributes

@Singleton
class ClientScopeService(
    private val clientScopesApiClient: ClientScopesApiClient,
    private val realmsAdminApiClient: RealmsAdminApiClient,
    private val clientScopeMapperService: ClientScopeMapperService,
) {
    private val logger = KotlinLogging.logger { }

    suspend fun createClientScope(
        realm: String,
        clientScopeConfiguration: ClientScopeConfiguration,
    ): Either<PbkException, Unit> =
        bind {
            with(clientScopeConfiguration) {
                // Creates client scope
                !runIdempotent {
                    clientScopesApiClient.realmClientScopesPost(
                        realm = realm,
                        clientScopeRepresentation =
                            ClientScopeRepresentation(
                                name = name,
                                description = description,
                                protocol = protocol.code,
                                attributes =
                                    ClientScopeRepresentationAttributes(
                                        consentScreenText = null,
                                        displayOnConsentScreen = displayOnConsentScreen,
                                        guiOrder = null,
                                        includeInTokenScope = includeInTokenScope,
                                    ),
                            ),
                    )
                }
                logger.info { "Client scope created [$name] in realm [$realm]" }

                val clientScope = !getClientScope(realm, name)

                !setType(realm, !clientScope.getId(), type)

                // Create mappers
                !clientScopeMapperService.createClientScopeMappers(realm, !clientScope.getId(), mappers)
            }
        }

    suspend fun getClientScope(
        realm: String,
        name: String,
    ): Either<PbkException, ClientScopeRepresentation> =
        bind {
            (!clientScopesApiClient.realmClientScopesGet(realm))
                .find { it.name == name }
                ?: bindException { NotFoundClientScopeException(name) }
        }

    private suspend fun setType(
        realm: String,
        id: String,
        type: ClientScopeType,
    ): Either<PbkException, Unit> =
        bind {
            when (type) {
                ClientScopeType.DEFAULT ->
                    !runIdempotent {
                        realmsAdminApiClient.realmDefaultDefaultClientScopesClientScopeIdPut(
                            realm,
                            id,
                        )
                    }
                ClientScopeType.OPTIONAL ->
                    !runIdempotent {
                        realmsAdminApiClient.realmDefaultOptionalClientScopesClientScopeIdPut(
                            realm,
                            id,
                        )
                    }
                ClientScopeType.NONE -> Unit
            }
        }
}
