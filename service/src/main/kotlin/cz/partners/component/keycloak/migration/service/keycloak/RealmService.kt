package cz.partners.component.keycloak.migration.service.keycloak

import arrow.core.Either
import cz.partners.component.keycloak.migration.configuration.RealmConfiguration
import cz.partners.component.keycloak.migration.configuration.RealmProperties
import cz.partners.component.keycloak.migration.helper.runIdempotent
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bind
import jakarta.inject.Singleton
import mu.KotlinLogging
import org.keycloak.admin.v19.client.RealmsAdminApiClient
import org.keycloak.admin.v19.model.RealmRepresentation
import org.keycloak.admin.v19.model.RealmRepresentationAttributes

@Singleton
class RealmService(
    private val realmsAdminApiClient: RealmsAdminApiClient,
    private val userProfileService: UserProfileService,
    realmPropertiesList: List<RealmProperties>,
) {
    private val logger = KotlinLogging.logger { }

    private val realmPropertiesMap = realmPropertiesList.associateBy { it.name }

    suspend fun createRealm(realmConfiguration: RealmConfiguration): Either<PbkException, Unit> =
        bind {
            with(realmConfiguration) {
                val realmName = name
                !upsertRealm(
                    realm = realmName,
                    realmRepresentation =
                        with(realmPropertiesMap[name]) {
                            this?.let {
                                RealmRepresentation(
                                    realm = realmName,
                                    enabled = true,
                                    accessTokenLifespan = tokenLifespan.accessToken.seconds.toInt(),
                                    ssoSessionIdleTimeout = tokenLifespan.ssoSession.seconds.toInt(),
                                    ssoSessionMaxLifespan = tokenLifespan.ssoSessionMax.seconds.toInt(),
                                    clientSessionIdleTimeout = tokenLifespan.clientSession.seconds.toInt(),
                                    clientSessionMaxLifespan = tokenLifespan.clientSessionMax.seconds.toInt(),
                                    offlineSessionIdleTimeout = tokenLifespan.offlineSession.seconds.toInt(),
                                    offlineSessionMaxLifespanEnabled = tokenLifespan.offlineSessionMaxEnabled,
                                    offlineSessionMaxLifespan = tokenLifespan.offlineSessionMax.seconds.toInt(),
                                    attributes =
                                        RealmRepresentationAttributes(
                                            frontendUrl = frontendUrl,
                                        ),
                                )
                            } ?: RealmRepresentation(
                                realm = realmName,
                                enabled = true,
                            )
                        },
                )

                // Configure user profile with unmanagedAttributePolicy
                !userProfileService.configureUserProfile(realmName, unmanagedAttributePolicy)
            }
        }

    private suspend fun upsertRealm(
        realm: String,
        realmRepresentation: RealmRepresentation,
    ): Either<PbkException, Unit> =
        bind {
            realmsAdminApiClient
                .realmGet(realm = realm)
                .fold(
                    ifLeft = {
                        !runIdempotent {
                            realmsAdminApiClient.rootPost(realmRepresentation)
                        }
                        logger.info { "Realm created [$realm]" }
                    },
                    ifRight = {
                        !realmsAdminApiClient.realmPut(realm, realmRepresentation)
                        logger.info { "Realm updated [$realm]" }
                    },
                )
        }
}
