package cz.partners.component.keycloak.migration.configuration

import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.core.annotation.Introspected

@Introspected
data class KeycloakConfiguration(
    val realms: MutableSet<RealmConfiguration> = mutableSetOf(),
)

@Introspected
data class RealmConfiguration(
    val name: String,
    @JsonProperty("clients-openid")
    val clientsOpenId: Set<ClientOpenIdConfiguration> = emptySet(),
    @JsonProperty("clients-saml")
    val clientsSaml: MutableSet<ClientSamlConfiguration> = mutableSetOf(),
    @JsonProperty("client-scopes")
    val clientScopes: MutableSet<ClientScopeConfiguration> = mutableSetOf(),
    @JsonProperty("authentication-flows")
    val authFlows: MutableSet<String> = mutableSetOf(),
    @JsonProperty("unmanaged-attribute-policy")
    val unmanagedAttributePolicy: UnmanagedAttributePolicy = UnmanagedAttributePolicy.ENABLED,
)

@Introspected
data class ClientScopeConfiguration(
    val name: String,
    val description: String,
    val type: ClientScopeType,
    val protocol: ClientScopeProtocol = ClientScopeProtocol.OPENID_CONNECT,
    @JsonProperty("display-on-consent-screen")
    val displayOnConsentScreen: Boolean = true,
    @JsonProperty("include-in-token-scope")
    val includeInTokenScope: Boolean = true,
    val mappers: MutableSet<ClientScopeMapperConfiguration> = mutableSetOf(),
)

@Introspected
data class ClientScopeMapperConfiguration(
    val name: String,
    val type: ClientScopeMapperType,
    val config: ClientScopeMapperSpecificConfiguration?,
)

@Introspected
data class ClientScopeMapperSpecificConfiguration(
    @JsonProperty("user-attribute-mapper")
    val userAttributeMapper: UserAttributeMapperConfiguration?,
    @JsonProperty("user-session-note-mapper")
    val userSessionNoteMapper: UserSessionNoteMapperConfiguration?,
    @JsonProperty("saml-user-session-note-mapper")
    val samlUserSessionNoteMapper: SamlUserSessionNoteMapperConfiguration?,
)

@Introspected
data class UserAttributeMapperConfiguration(
    @JsonProperty("token-claim-name")
    val tokenClaimName: String,
    @JsonProperty("token-claim-type")
    val tokenClaimType: TokenClaimType,
    @JsonProperty("user-attribute-name")
    val userAttributeName: String?,
)

@Introspected
data class UserSessionNoteMapperConfiguration(
    @JsonProperty("token-claim-name")
    val tokenClaimName: String,
    @JsonProperty("token-claim-type")
    val tokenClaimType: TokenClaimType,
    @JsonProperty("note-name")
    val noteName: String,
)

@Introspected
data class SamlUserSessionNoteMapperConfiguration(
    @JsonProperty("note-name")
    val noteName: String?,
    @JsonProperty("friendly-name")
    val friendlyName: String?,
    @JsonProperty("attribute-name")
    val attributeName: String?,
    @JsonProperty("attribute-name-format")
    val attributeNameFormat: SamlUserSessionNoteMapperNameFormat,
)

@Introspected
enum class SamlUserSessionNoteMapperNameFormat {
    UNSPECIFIED,
    URI_REFERENCE,
    BASIC,
}

@Introspected
data class ClientOpenIdConfiguration(
    val name: String,
    @JsonProperty("client-id")
    val clientId: String = name,
    val description: String,
    @JsonProperty("service-accounts-enabled")
    val serviceAccountsEnabled: Boolean = false,
    @JsonProperty("standard-flow-enabled")
    val standardFlowEnabled: Boolean = false,
    @JsonProperty("implicit-flow-enabled")
    val implicitFlowEnabled: Boolean = false,
    @JsonProperty("direct-access-grants-enabled")
    val directAccessGrantsEnabled: Boolean = false,
    @JsonProperty("public-client")
    val publicClient: Boolean = false,
    @JsonProperty("authorization-services-enabled")
    val authorizationServicesEnabled: Boolean = false,
    @JsonProperty("root-url-required")
    val rootUrlRequired: Boolean = false,
    @JsonProperty("base-url-required")
    val baseUrlRequired: Boolean = false,
    @JsonProperty("redirect-uris-required")
    val redirectUrisRequired: Boolean = false,
    @JsonProperty("redirect-logout-uris-required")
    val redirectLogoutUrisRequired: Boolean = false,
    @JsonProperty("web-origins-required")
    val webOriginsRequired: Boolean = false,
    @JsonProperty("admin-url-required")
    val adminUrlRequired: Boolean = false,
    @JsonProperty("authentication-flow-required")
    val authenticationFlowRequired: Boolean = false,
    val roles: MutableSet<RoleConfiguration> = mutableSetOf(),
    @JsonProperty("assign-client-scopes")
    val assignClientScopes: MutableSet<ClientAssignClientScopesConfiguration> = mutableSetOf(),
    @JsonProperty("assign-roles")
    val assignRoles: MutableSet<ClientAssignClientRoleConfiguration> = mutableSetOf(),
)

@Introspected
data class ClientSamlConfiguration(
    val name: String,
    val description: String,
    @JsonProperty("master-saml-processing-url-required")
    val masterSamlProcessingUrlRequired: Boolean = false,
    @JsonProperty("saml-signature-key-name")
    val samlSignatureKeyName: SamlSignatureKeyNameType = SamlSignatureKeyNameType.NONE,
    val roles: MutableSet<RoleConfiguration> = mutableSetOf(),
    @JsonProperty("assign-roles")
    val assignRoles: MutableSet<ClientAssignClientRoleConfiguration> = mutableSetOf(),
)

@Introspected
data class RoleConfiguration(
    val name: String,
    val description: String,
    @JsonProperty("associated-roles")
    val associatedRoles: MutableSet<AssociatedRoles> = mutableSetOf(),
)

data class AssociatedRoles(
    @JsonProperty("role-name")
    val roleName: String,
)

@Introspected
data class ClientAssignClientRoleConfiguration(
    @JsonProperty("client-name")
    val clientName: String,
    @JsonProperty("role-name")
    val roleName: String,
)

@Introspected
data class ClientAssignClientScopesConfiguration(
    val name: String,
    val type: ClientScopeType,
)

@Introspected
data class AuthenticationFlowExecutionConfiguration(
    val authenticator: String,
    val requirement: AuthenticatorFlowExecutionRequirement = AuthenticatorFlowExecutionRequirement.REQUIRED,
    @JsonProperty("configuration-ref")
    val configurationRef: String?,
)

@Introspected
enum class TokenClaimType {
    JSON,
    STRING,
}

@Introspected
enum class ClientScopeMapperType(
    val protocol: ClientScopeProtocol,
) {
    USER_ATTRIBUTES(ClientScopeProtocol.OPENID_CONNECT),
    USER_SESSION_NOTE(ClientScopeProtocol.OPENID_CONNECT),
    NONCE_ID_TOKEN(ClientScopeProtocol.OPENID_CONNECT),
    NONCE_BACKWARDS_COMPATIBLE(ClientScopeProtocol.OPENID_CONNECT),
    SAML_USER_SESSION_NOTE_MAPPER(ClientScopeProtocol.SAML),
    CUSTOM_SUBJECT_SAML_MAPPER(ClientScopeProtocol.SAML),
}

@Introspected
enum class ClientScopeType {
    DEFAULT,
    OPTIONAL,
    NONE,
}

@Introspected
enum class ClientScopeProtocol(
    val code: String,
) {
    OPENID_CONNECT("openid-connect"),
    SAML("saml"),
}

@Introspected
enum class SamlSignatureKeyNameType {
    NONE,
    KEY_ID,
    CERT_SUBJECT,
}

@Introspected
enum class UnmanagedAttributePolicy {
    ENABLED,
    ADMIN_VIEW,
    ADMIN_EDIT,
}
