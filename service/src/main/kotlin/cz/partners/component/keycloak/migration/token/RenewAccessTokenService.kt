package cz.partners.component.keycloak.migration.token

import arrow.core.Either
import cz.pbktechnology.platform.common.context.ContextConfiguration.initialContext
import cz.pbktechnology.platform.common.exception.PbkException
import io.micronaut.scheduling.annotation.Scheduled
import jakarta.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import mu.KotlinLogging

@Singleton
class RenewAccessTokenService(
    private val oAuth2PasswordGrant: OAuth2PasswordGrant,
    private val oAuth2ClientConfiguration: OAuth2ClientConfiguration,
    private val accessTokenService: AccessTokenService,
) {
    private val logger = KotlinLogging.logger { }

    @Scheduled(fixedDelay = "\${keycloak.admin.token-renewal-interval}")
    fun renewAccessTokenScheduled() {
        CoroutineScope(initialContext()).launch {
            logger.info("Scheduled access token renewal")
            renewAccessToken().fold(
                { logger.error { "Scheduled access token renewal error: " + it.message } },
                { },
            )
        }
    }

    suspend fun renewAccessToken(): Either<PbkException, Unit> =
        oAuth2PasswordGrant.getAuthorization(oAuth2ClientConfiguration).map {
            accessTokenService.accessToken = it.accessToken
            logger.info { "Renewed access token" }
        }
}
