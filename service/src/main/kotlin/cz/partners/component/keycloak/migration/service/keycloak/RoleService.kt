package cz.partners.component.keycloak.migration.service.keycloak

import arrow.core.Either
import cz.partners.component.keycloak.migration.configuration.RoleConfiguration
import cz.partners.component.keycloak.migration.exception.NotFoundClientRoleException
import cz.partners.component.keycloak.migration.helper.getId
import cz.partners.component.keycloak.migration.helper.runIdempotent
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import cz.pbktechnology.platform.common.helper.bindSync
import jakarta.inject.Singleton
import org.keycloak.admin.v19.client.RolesApiClient
import org.keycloak.admin.v19.model.ClientRepresentation
import org.keycloak.admin.v19.model.RoleRepresentation

@Singleton
class RoleService(
    private val rolesApiClient: RolesApiClient,
) {
    suspend fun createClientRoles(
        realm: String,
        clientRepresentation: ClientRepresentation,
        roles: MutableSet<RoleConfiguration>,
    ): Either<PbkException, Unit> =
        bind {
            roles.forEach { !createClientRole(realm, !clientRepresentation.getId(), it) }
            roles.forEach { !associateRoles(realm, clientRepresentation, it) }
        }

    private suspend fun associateRoles(
        realm: String,
        clientRepresentation: ClientRepresentation,
        role: RoleConfiguration,
    ): Either<PbkException, Unit> =
        bind {
            val clientRoles = !getClientRoles(realm, !clientRepresentation.getId())

            role.associatedRoles.forEach { associatedRole ->
                !runIdempotent {
                    rolesApiClient.realmRolesByIdRoleIdCompositesPost(
                        realm = realm,
                        roleId = !getRoleIdByName(clientRepresentation, clientRoles, role.name),
                        roleRepresentation = clientRoles.filter { it.name == associatedRole.roleName },
                    )
                }
            }
        }

    private fun getRoleIdByName(
        clientRepresentation: ClientRepresentation,
        roles: List<RoleRepresentation>,
        name: String,
    ): Either<PbkException, String> =
        bindSync {
            roles
                .find { it.name == name }
                ?.let { !it.getId() }
                ?: bindException { NotFoundClientRoleException(clientRepresentation.name ?: "", name) }
        }

    suspend fun getClientRoles(
        realm: String,
        clientId: String,
    ): Either<PbkException, List<RoleRepresentation>> = rolesApiClient.realmClientsClientIdRolesGet(realm, clientId)

    private suspend fun createClientRole(
        realm: String,
        clientId: String,
        role: RoleConfiguration,
    ): Either<PbkException, Unit> =
        bind {
            with(role) {
                !runIdempotent {
                    rolesApiClient.realmClientsClientIdRolesPost(
                        realm = realm,
                        clientId = clientId,
                        roleRepresentation =
                            RoleRepresentation(
                                name = name,
                                description = description,
                            ),
                    )
                }
            }
        }
}
