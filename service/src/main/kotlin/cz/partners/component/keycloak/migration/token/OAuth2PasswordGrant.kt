package cz.partners.component.keycloak.migration.token

import arrow.core.Either
import cz.pbktechnology.platform.common.exception.PbkException
import jakarta.inject.Singleton
import mu.KotlinLogging
import org.keycloak.openidconnect.v19.client.OpenidConnectApiClient
import org.keycloak.openidconnect.v19.model.GetTokenRequest
import org.keycloak.openidconnect.v19.model.GetTokenResponse

@Singleton
class OAuth2PasswordGrant(
    private val openidConnectApiClient: OpenidConnectApiClient,
) {
    private val logger = KotlinLogging.logger { }

    suspend fun getAuthorization(oAuth2ClientConfiguration: OAuth2ClientConfiguration): Either<PbkException, GetTokenResponse> {
        logger.debug("Requesting token")

        with(oAuth2ClientConfiguration) {
            val passwordGrantRequest =
                GetTokenRequest(
                    clientId = clientId,
                    grantType = GetTokenRequest.GrantType.PASSWORD,
                    username = username,
                    password = password,
                )

            return openidConnectApiClient.getToken("master", passwordGrantRequest)
        }
    }
}
