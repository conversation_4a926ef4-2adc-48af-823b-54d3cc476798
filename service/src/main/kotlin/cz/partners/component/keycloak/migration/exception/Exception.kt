package cz.partners.component.keycloak.migration.exception

import cz.pbktechnology.platform.common.exception.BusinessException
import io.micronaut.http.HttpStatus

const val SOURCE_SERVICE = "keycloak-migration"

class UnableParseYamlException(
    filename: String,
) : BusinessException(
        errorMessage = "Unable to parse yaml from file [$filename]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class NotFoundAttributeException(
    attributeName: String,
    schema: String?,
    name: String?,
) : BusinessException(
        errorMessage = "Not found attribute [$attributeName] on schema [$schema] with name [$name]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class NotFoundClientException(
    clientName: String,
) : BusinessException(
        errorMessage = "Not found client with name [$clientName]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class NotFoundClientRoleException(
    clientName: String,
    roleName: String,
) : BusinessException(
        errorMessage = "Not found role with name [$roleName] of client [$clientName]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class NotFoundClientScopeException(
    clientScopeName: String,
) : BusinessException(
        errorMessage = "Not found client scope with name [$clientScopeName]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class NotFoundAuthenticationFlowException(
    name: String,
) : BusinessException(
        errorMessage = "Not found authentication flow declaration with name [$name]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class NotFoundAuthenticationFlowExecutionException(
    rootFlowAlias: String,
    executionAlias: String,
) : BusinessException(
        errorMessage = "Not found authentication flow execution with name [$executionAlias] in flow [$rootFlowAlias]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsSecretPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing secret application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingSamlClientsMasterSamlProcessingUrlPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing master-saml-processing-url application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsRootUrlPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing root-url application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsBaseUrlPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing base-url application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsRedirectUrisPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing redirect-uris application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsRedirectLogoutUrisPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing redirect-logout-uris application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsWebOriginsPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing web-origins application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsAdminUrlPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing admin-url application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsAuthFlowPropertyException(
    clientNames: Set<String>,
) : BusinessException(
        errorMessage = "Missing authentication-flow application property for clients [$clientNames]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class MissingOpenIdClientsScopeMapperConfigurationException(
    mapperName: String,
) : BusinessException(
        errorMessage = "Missing configuration for client scope mapper [$mapperName]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class IllegalStateException(
    reason: String,
) : BusinessException(
        errorMessage = "Illegal system state exception occurred. Investigate the reason for further details. Reason: [$reason]",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class ClientCreatedByOtherServiceException(
    clientName: String,
    serviceName: String?,
) : BusinessException(
        errorMessage = "Client with name [$clientName] was created by [$serviceName] service. This service is not allowed to modify it.",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class DuplicateAuthenticationFlowNameException(
    name: String,
) : BusinessException(
        errorMessage = "Duplicate declaration of authentication flow with name [$name] found",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )

class DuplicateAuthenticationFlowExecutionAliasException(
    alias: String,
) : BusinessException(
        errorMessage = "Found multiple usages of authentication flow alias [$alias] in authentication flow declaration",
        httpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
        errorSource = SOURCE_SERVICE,
    )
