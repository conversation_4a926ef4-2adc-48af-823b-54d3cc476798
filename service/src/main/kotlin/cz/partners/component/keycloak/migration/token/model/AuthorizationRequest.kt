/**
 * Keycloak Auth REST API
 *
 * This is a REST API reference for the Keycloak Auth
 *
 * The version of the OpenAPI document: 18.0
 *
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport",
)

package cz.partners.component.keycloak.migration.token.model

import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.core.annotation.Introspected

/**
 *
 *
 * @param grantType
 * @param clientId
 * @param clientSecret
 * @param username
 * @param password
 * @param scope
 */

@Introspected
data class AuthorizationRequest(
    @field:JsonProperty("grant_type")
    val grantType: GrantType,
    @field:JsonProperty("client_id")
    val clientId: kotlin.String,
    @field:JsonProperty("client_secret")
    val clientSecret: kotlin.String? = null,
    @field:Json<PERSON>roperty("username")
    val username: kotlin.String? = null,
    @field:Json<PERSON>roperty("password")
    val password: kotlin.String? = null,
    @field:JsonProperty("scope")
    val scope: kotlin.String? = null,
) {
    /**
     *
     *
     * Values: PASSWORD,CLIENT_CREDENTIALS
     */
    enum class GrantType(
        val value: kotlin.String,
    ) {
        @JsonProperty(value = "password")
        PASSWORD("password"),

        @JsonProperty(value = "client_credentials")
        CLIENT_CREDENTIALS("client_credentials"),
    }
}
