package cz.partners.component.keycloak.migration.service.keycloak

import arrow.core.Either
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import cz.partners.component.keycloak.migration.configuration.ClientOpenIdConfiguration
import cz.partners.component.keycloak.migration.exception.NotFoundClientRoleException
import cz.partners.component.keycloak.migration.helper.getId
import cz.partners.component.keycloak.migration.helper.runIdempotent
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import cz.pbktechnology.platform.common.util.EitherUtil.getOrThrow
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.Cacheable
import jakarta.inject.Singleton
import mu.KotlinLogging
import org.keycloak.admin.v19.client.ClientRoleMappingsApiClient
import org.keycloak.admin.v19.client.RoleMapperApiClient
import org.keycloak.admin.v19.client.RolesApiClient
import org.keycloak.admin.v19.model.ClientMappingsRepresentation
import org.keycloak.admin.v19.model.ClientRepresentation

@Singleton
@CacheConfig("assign-role-service")
open class AssignRoleService(
    private val clientService: ClientService,
    private val clientRoleMappingsApiClient: ClientRoleMappingsApiClient,
    private val roleMapperApiClient: RoleMapperApiClient,
    private val objectMapper: ObjectMapper,
    private val roleApiClient: RolesApiClient,
) {
    private val logger = KotlinLogging.logger { }

    suspend fun assignRolesToClients(
        realm: String,
        clientOpenIdConfiguration: Set<ClientOpenIdConfiguration>,
    ): Either<PbkException, Unit> =
        bind {
            clientOpenIdConfiguration
                .filter { it.assignRoles.isNotEmpty() }
                .forEach { clientConfiguration ->
                    val clientIdToBeAssignedRoles = !(!clientService.getClient(realm, clientConfiguration.clientId)).getId()
                    val serviceUserIdToBeAssignedRoles = !clientService.getClientServiceUserId(realm, clientIdToBeAssignedRoles)

                    // Remove all existing roles mappings
                    // TODO: PTS-811 Remove only redundant roles, this can lead to downtime
                    !removeAllAssignedRolesFromClient(realm, serviceUserIdToBeAssignedRoles)

                    // Assign roles
                    clientConfiguration.assignRoles.forEach { roleAssignment ->
                        logger.info {
                            "Assigning role [${roleAssignment.roleName}] from client [${roleAssignment.clientName}] " +
                                "to client [${clientConfiguration.name}] in realm [$realm]"
                        }

                        val clientIdThatOwnsRole = !(getClientByNameCached(name = roleAssignment.clientName, realm = realm)).getId()
                        val allRolesOfOwner = getClientRolesCached(realm = realm, clientIdThatOwnsRole = clientIdThatOwnsRole)
                        val roleToAssign =
                            allRolesOfOwner.find { it.name == roleAssignment.roleName }
                                ?: bindException {
                                    NotFoundClientRoleException(
                                        clientName = roleAssignment.clientName,
                                        roleName = roleAssignment.roleName,
                                    )
                                }

                        !runIdempotent {
                            clientRoleMappingsApiClient.realmUsersUserIdRoleMappingsClientsClientIdPost(
                                realm = realm,
                                userId = serviceUserIdToBeAssignedRoles,
                                clientId = clientIdThatOwnsRole,
                                roleRepresentation = listOf(roleToAssign),
                            )
                        }
                        logger.info {
                            "Assigned role [${roleAssignment.roleName}] from client [${roleAssignment.clientName}] " +
                                "to client [${clientConfiguration.clientId}] in realm [$realm]"
                        }
                    }
                }
        }

    @Cacheable
    internal open suspend fun getClientByNameCached(
        name: String,
        realm: String,
    ): ClientRepresentation = clientService.getClient(realm, name).getOrThrow()

    @Cacheable
    internal open suspend fun getClientRolesCached(
        clientIdThatOwnsRole: String,
        realm: String,
    ) = roleApiClient.realmClientsClientIdRolesGet(realm = realm, clientId = clientIdThatOwnsRole).getOrThrow()

    private suspend fun removeAllAssignedRolesFromClient(
        realm: String,
        serviceUserId: String,
    ): Either<PbkException, Unit> =
        bind {
            val mappingsRepresentation =
                !roleMapperApiClient.realmUsersUserIdRoleMappingsGet(
                    realm = realm,
                    userId = serviceUserId,
                )

            mappingsRepresentation.clientMappings
                ?.map { clientMap ->
                    objectMapper.convertValue<ClientMappingsRepresentation>(clientMap.value).let { it.id to it.mappings }
                }?.toMap()
                ?.let { clientMapping ->
                    clientMapping.map { it ->
                        !clientRoleMappingsApiClient.realmUsersUserIdRoleMappingsClientsClientIdDelete(
                            realm = realm,
                            userId = serviceUserId,
                            clientId = it.key,
                            roleRepresentation = it.value,
                        )
                    }
                }
        }
}
