package cz.partners.component.keycloak.migration.configuration

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import cz.partners.component.keycloak.migration.exception.NotFoundAuthenticationFlowException
import io.micronaut.core.annotation.Introspected

@Introspected
data class AuthenticationFlowDeclaration(
    @JsonProperty("authentication-flows")
    val authenticationFlows: List<AuthenticationFlow>,
) {
    fun getAuthenticationFlow(name: String) =
        authenticationFlows.find { it.name == name } ?: throw NotFoundAuthenticationFlowException(name)
}

@Introspected
data class AuthenticationFlow(
    val name: String,
    val description: String?,
    val type: AuthenticatorFlowProviderId = AuthenticatorFlowProviderId.BASIC_FLOW,
    val executions: List<AuthenticationFlowExecution>,
    /**
     * Technical field to mark invalid authentication flows (e.g. when it contains executions with invalid provider).
     * When false, it prevents the flow from being assigned to clients
     */
    @JsonIgnore
    var valid: Boolean = true,
)

@Introspected
data class AuthenticationFlowExecution(
    val alias: String,
    val description: String?,
    val type: ExecutionType,
    val config: String?,
    val requirement: AuthenticatorFlowExecutionRequirement,
    val executions: List<AuthenticationFlowExecution>?,
)

@Introspected
enum class ExecutionType(
    val providerId: String?,
    val isFlow: Boolean,
) {
    GENERIC_SUBFLOW(null, true),
    FED_AUTHENTICATOR("fed-authenticator", false),
    NIA_AUTHENTICATOR("nia-authenticator", false),
    VERIFY_AUTHENTICATION_METHODS("verify-auth-methods-authenticator", false),
    CONDITION_LEVEL_OF_AUTHENTICATION("conditional-level-of-authentication", false),
}

@Introspected
enum class AuthenticatorFlowProviderId(
    val code: String,
) {
    BASIC_FLOW("basic-flow"),
    CLIENT_FLOW("client-flow"),
}

@Introspected
enum class AuthenticatorFlowExecutionRequirement {
    REQUIRED,
    ALTERNATIVE,
    CONDITIONAL,
    DISABLED,
}
