package cz.partners.component.keycloak.migration.service.keycloak

import arrow.core.Either
import cz.partners.component.keycloak.migration.configuration.ClientScopeMapperConfiguration
import cz.partners.component.keycloak.migration.configuration.ClientScopeMapperType
import cz.partners.component.keycloak.migration.configuration.ClientScopeProtocol
import cz.partners.component.keycloak.migration.configuration.SamlUserSessionNoteMapperNameFormat
import cz.partners.component.keycloak.migration.configuration.TokenClaimType
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsScopeMapperConfigurationException
import cz.partners.component.keycloak.migration.helper.runIdempotent
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import jakarta.inject.Singleton
import mu.KotlinLogging
import org.keycloak.admin.v19.client.ProtocolMappersApiClient
import org.keycloak.admin.v19.model.CustomSubjectSamlMapperConfig
import org.keycloak.admin.v19.model.NonceBackwardsCompatibleMapperConfig
import org.keycloak.admin.v19.model.NonceIdTokenMapperConfig
import org.keycloak.admin.v19.model.Protocol
import org.keycloak.admin.v19.model.ProtocolMapperConfig
import org.keycloak.admin.v19.model.ProtocolMapperRepresentation
import org.keycloak.admin.v19.model.ProtocolMapperRepresentation.ProtocolMapper
import org.keycloak.admin.v19.model.SamlAttributeNameFormat
import org.keycloak.admin.v19.model.SamlUserSessionNoteMapperConfig
import org.keycloak.admin.v19.model.UserAttributeMapperConfig
import org.keycloak.admin.v19.model.UserSessionNoteMapperConfig

@Singleton
class ClientScopeMapperService(
    private val protocolMappersApiClient: ProtocolMappersApiClient,
) {
    private val logger = KotlinLogging.logger { }

    suspend fun createClientScopeMappers(
        realm: String,
        id: String,
        mappers: MutableSet<ClientScopeMapperConfiguration>,
    ): Either<PbkException, Unit> =
        bind {
            mappers.forEach { !createClientScopeMapper(realm, id, it) }
        }

    private suspend fun createClientScopeMapper(
        realm: String,
        clientScopeId: String,
        mapper: ClientScopeMapperConfiguration,
    ): Either<PbkException, Unit> =
        bind {
            with(mapper) {
                !runIdempotent {
                    protocolMappersApiClient.realmClientScopesClientScopeIdProtocolMappersModelsPost(
                        realm = realm,
                        clientScopeId = clientScopeId,
                        protocolMapperRepresentation =
                            ProtocolMapperRepresentation(
                                name = name,
                                protocol =
                                    when (type.protocol) {
                                        ClientScopeProtocol.OPENID_CONNECT -> Protocol.OPENID_MINUS_CONNECT
                                        ClientScopeProtocol.SAML -> Protocol.SAML
                                    },
                                protocolMapper =
                                    when (type) {
                                        ClientScopeMapperType.USER_ATTRIBUTES ->
                                            ProtocolMapper.OIDC_MINUS_USERMODEL_MINUS_ATTRIBUTE_MINUS_MAPPER

                                        ClientScopeMapperType.USER_SESSION_NOTE ->
                                            ProtocolMapper.OIDC_MINUS_USERSESSIONMODEL_MINUS_NOTE_MINUS_MAPPER

                                        ClientScopeMapperType.NONCE_ID_TOKEN ->
                                            ProtocolMapper.NONCE_MINUS_ID_MINUS_TOKEN_MINUS_MAPPER

                                        ClientScopeMapperType.NONCE_BACKWARDS_COMPATIBLE ->
                                            ProtocolMapper.OIDC_MINUS_NONCE_MINUS_BACKWARDS_MINUS_COMPATIBLE_MINUS_MAPPER

                                        ClientScopeMapperType.SAML_USER_SESSION_NOTE_MAPPER ->
                                            ProtocolMapper.SAML_MINUS_USER_MINUS_SESSION_MINUS_NOTE_MINUS_MAPPER

                                        ClientScopeMapperType.CUSTOM_SUBJECT_SAML_MAPPER ->
                                            ProtocolMapper.CUSTOM_MINUS_SUBJECT_MINUS_SAML_MINUS_MAPPER
                                    },
                                config = !getMapperConfig(name, mapper),
                            ),
                    )
                }
                logger.info { "Client scope created [$name] in realm [$realm]" }
            }
        }

    private suspend fun getMapperConfig(
        mapperName: String,
        config: ClientScopeMapperConfiguration,
    ): Either<PbkException, ProtocolMapperConfig> =
        bind {
            when (config.type) {
                ClientScopeMapperType.NONCE_ID_TOKEN -> NonceIdTokenMapperConfig()
                ClientScopeMapperType.NONCE_BACKWARDS_COMPATIBLE -> NonceBackwardsCompatibleMapperConfig()
                ClientScopeMapperType.CUSTOM_SUBJECT_SAML_MAPPER -> CustomSubjectSamlMapperConfig()
                ClientScopeMapperType.USER_ATTRIBUTES ->
                    with(
                        config.config?.userAttributeMapper
                            ?: bindException { MissingOpenIdClientsScopeMapperConfigurationException(mapperName) },
                    ) {
                        UserAttributeMapperConfig(
                            accessTokenClaim = true,
                            aggregateAttrs = false,
                            claimName = tokenClaimName,
                            idTokenClaim = true,
                            jsonTypeLabel =
                                when (tokenClaimType) {
                                    TokenClaimType.STRING -> UserAttributeMapperConfig.JsonTypeLabel.EMPTY
                                    TokenClaimType.JSON -> UserAttributeMapperConfig.JsonTypeLabel.JSON
                                },
                            multivalued = false,
                            userAttribute = userAttributeName ?: mapperName,
                            userinfoTokenClaim = true,
                        )
                    }
                ClientScopeMapperType.USER_SESSION_NOTE ->
                    with(
                        config.config?.userSessionNoteMapper
                            ?: bindException { MissingOpenIdClientsScopeMapperConfigurationException(mapperName) },
                    ) {
                        UserSessionNoteMapperConfig(
                            accessTokenClaim = false,
                            claimName = tokenClaimName,
                            idTokenClaim = true,
                            jsonTypeLabel =
                                when (tokenClaimType) {
                                    TokenClaimType.STRING -> UserSessionNoteMapperConfig.JsonTypeLabel.EMPTY
                                    TokenClaimType.JSON -> UserSessionNoteMapperConfig.JsonTypeLabel.JSON
                                },
                            userSessionNote = noteName,
                            userinfoTokenClaim = false,
                            lightweightClaim = false,
                            accessTokenResponseClaim = false,
                            introspectionTokenClaim = true,
                        )
                    }
                ClientScopeMapperType.SAML_USER_SESSION_NOTE_MAPPER ->
                    with(
                        config.config?.samlUserSessionNoteMapper
                            ?: bindException { MissingOpenIdClientsScopeMapperConfigurationException(mapperName) },
                    ) {
                        SamlUserSessionNoteMapperConfig(
                            attributeNameformat =
                                when (attributeNameFormat) {
                                    SamlUserSessionNoteMapperNameFormat.BASIC -> SamlAttributeNameFormat.BASIC
                                    SamlUserSessionNoteMapperNameFormat.URI_REFERENCE -> SamlAttributeNameFormat.URI_REFERENCE
                                    SamlUserSessionNoteMapperNameFormat.UNSPECIFIED -> SamlAttributeNameFormat.UNSPECIFIED
                                },
                            attributeName = attributeName,
                            note = noteName,
                            friendlyName = friendlyName,
                        )
                    }
            }
        }
}
