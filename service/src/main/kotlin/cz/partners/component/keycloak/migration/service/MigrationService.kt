package cz.partners.component.keycloak.migration.service

import arrow.core.Either
import cz.partners.component.keycloak.migration.configuration.ConfigurationProvider
import cz.partners.component.keycloak.migration.service.keycloak.AssignRoleService
import cz.partners.component.keycloak.migration.service.keycloak.AuthenticationFlowService
import cz.partners.component.keycloak.migration.service.keycloak.ClientScopeService
import cz.partners.component.keycloak.migration.service.keycloak.ClientService
import cz.partners.component.keycloak.migration.service.keycloak.RealmService
import cz.partners.component.keycloak.migration.token.RenewAccessTokenService
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bind
import io.github.resilience4j.kotlin.retry.executeSuspendFunction
import io.github.resilience4j.retry.RetryConfig
import io.github.resilience4j.retry.RetryRegistry
import io.micronaut.context.annotation.Requires
import io.micronaut.context.event.StartupEvent
import io.micronaut.runtime.event.annotation.EventListener
import jakarta.inject.Singleton
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import kotlin.system.exitProcess
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration

@Singleton
class MigrationService(
    private val configurationProvider: ConfigurationProvider,
    private val realmService: RealmService,
    private val clientService: ClientService,
    private val clientScopeService: ClientScopeService,
    private val assignRoleService: AssignRoleService,
    private val renewAccessTokenService: RenewAccessTokenService,
    private val authFlowService: AuthenticationFlowService,
    private val retryRegistry: RetryRegistry,
) {
    private val logger = KotlinLogging.logger { }

    @EventListener
    @Requires(notEnv = ["test"])
    fun onStartup(startupEvent: StartupEvent?): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            migrate().fold(
                {
                    logger.error(it) { it.message }
                    exitProcess(1)
                },
                {
                    logger.info { "Migration successful!" }
                    exitProcess(0)
                },
            )
        }

    suspend fun migrate(): Either<PbkException, Unit> =
        bind {
            val keycloakConfiguration = !configurationProvider.getConfiguration()

            // Get token for first call
            !renewAccessTokenService.renewAccessToken()

            keycloakConfiguration.realms.forEach {
                // Creates realm
                !realmService.createRealm(it)
            }

            // Renew token with new realm roles
            !renewAccessTokenService.renewAccessToken()

            keycloakConfiguration.realms.forEach { realmConfiguration ->
                with(realmConfiguration) {
                    // Create authentication flows and executions
                    realmConfiguration.authFlows.forEach {
                        !retry("authFlows").executeSuspendFunction { authFlowService.createAuthenticationFlow(name, it) }
                    }

                    // Create client scopes
                    realmConfiguration.clientScopes.forEach {
                        !retry("clientScopes").executeSuspendFunction { clientScopeService.createClientScope(name, it) }
                    }

                    // Create openid clients
                    realmConfiguration.clientsOpenId.forEach {
                        !retry("clientsOpenId").executeSuspendFunction { clientService.createOpenIdClient(name, it) }
                    }

                    // Create saml clients
                    realmConfiguration.clientsSaml.forEach {
                        !retry("clientsSaml").executeSuspendFunction { clientService.createSamlClient(name, it) }
                    }

                    // Assign client roles to clients
                    !retry("assignRolesToClients").executeSuspendFunction {
                        assignRoleService.assignRolesToClients(
                            name,
                            realmConfiguration.clientsOpenId,
                        )
                    }
                }
            }
        }

    private fun retry(stageName: String) =
        retryRegistry
            .retry(
                "${this::class.qualifiedName}:$stageName",
                RetryConfig
                    .custom<Either<PbkException, Unit>>()
                    .maxAttempts(MIGRATE_MAX_RETRIES)
                    .retryOnException { true }
                    .retryOnResult { result ->
                        result.fold(
                            {
                                // Event listeners do not work, so we log here
                                logger.warn(it) { "Error occurred, will retry in [${MIGRATE_RETRY_DURATION.inWholeSeconds}]s" }
                                true
                            },
                            { false },
                        )
                    }.waitDuration(MIGRATE_RETRY_DURATION.toJavaDuration())
                    .build(),
            )

    companion object {
        const val MIGRATE_MAX_RETRIES = 3
        val MIGRATE_RETRY_DURATION = 10.seconds
    }
}
