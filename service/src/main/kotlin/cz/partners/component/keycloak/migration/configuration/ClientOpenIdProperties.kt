package cz.partners.component.keycloak.migration.configuration

import arrow.core.Either
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsAdminUrlPropertyException
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsAuthFlowPropertyException
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsBaseUrlPropertyException
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsRedirectLogoutUrisPropertyException
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsRedirectUrisPropertyException
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsRootUrlPropertyException
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsSecretPropertyException
import cz.partners.component.keycloak.migration.exception.MissingOpenIdClientsWebOriginsPropertyException
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.BindHelper.bindException
import cz.pbktechnology.platform.common.helper.bind
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import jakarta.inject.Singleton
import java.time.Duration

@EachProperty("client-openid")
class ClientOpenIdProperties(
    @param:Parameter var name: String,
) {
    var secret: String? = null
    var rootUrl: String? = null
    var baseUrl: String? = null
    var redirectUris: String? = null
    var redirectLogoutUris: String? = null
    var webOrigins: String? = null
    var adminUrl: String? = null
    var authenticationFlow: String? = null
    var offlineSession: Duration? = null
    var offlineSessionMax: Duration? = null
}

@Singleton
class ClientOpenIdPropertiesChecker(
    private val clientOpenIdPropertiesList: List<ClientOpenIdProperties>,
) {
    suspend fun checkConfiguration(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            !checkConfiguredSecret(keycloakConfiguration)
            !checkConfiguredRootUrl(keycloakConfiguration)
            !checkConfiguredBaseUrl(keycloakConfiguration)
            !checkConfiguredRedirectUris(keycloakConfiguration)
            !checkConfiguredRedirectLogoutUris(keycloakConfiguration)
            !checkConfiguredWebOrigins(keycloakConfiguration)
            !checkConfiguredAdminUrl(keycloakConfiguration)
            !checkConfiguredAuthFlow(keycloakConfiguration)
        }

    private suspend fun checkConfiguredRootUrl(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientOpenIdPropertiesList
                    .filter { it.rootUrl != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsOpenId }
                    .filter { it.rootUrlRequired }
                    .map { it.clientId }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingOpenIdClientsRootUrlPropertyException(this) }
                }
            }
        }

    private suspend fun checkConfiguredBaseUrl(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientOpenIdPropertiesList
                    .filter { it.baseUrl != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsOpenId }
                    .filter { it.baseUrlRequired }
                    .map { it.clientId }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingOpenIdClientsBaseUrlPropertyException(this) }
                }
            }
        }

    private suspend fun checkConfiguredRedirectUris(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientOpenIdPropertiesList
                    .filter { it.redirectUris != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsOpenId }
                    .filter { it.redirectUrisRequired }
                    .map { it.clientId }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingOpenIdClientsRedirectUrisPropertyException(this) }
                }
            }
        }

    private suspend fun checkConfiguredRedirectLogoutUris(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientOpenIdPropertiesList
                    .filter { it.redirectLogoutUris != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsOpenId }
                    .filter { it.redirectLogoutUrisRequired }
                    .map { it.clientId }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingOpenIdClientsRedirectLogoutUrisPropertyException(this) }
                }
            }
        }

    private suspend fun checkConfiguredWebOrigins(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientOpenIdPropertiesList
                    .filter { it.webOrigins != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsOpenId }
                    .filter { it.webOriginsRequired }
                    .map { it.clientId }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingOpenIdClientsWebOriginsPropertyException(this) }
                }
            }
        }

    private suspend fun checkConfiguredAdminUrl(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientOpenIdPropertiesList
                    .filter { it.adminUrl != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsOpenId }
                    .filter { it.adminUrlRequired }
                    .map { it.clientId }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingOpenIdClientsAdminUrlPropertyException(this) }
                }
            }
        }

    private suspend fun checkConfiguredSecret(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientOpenIdPropertiesList
                    .filter { it.secret != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsOpenId }
                    .filter { !it.publicClient }
                    .map { it.clientId }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingOpenIdClientsSecretPropertyException(this) }
                }
            }
        }

    private suspend fun checkConfiguredAuthFlow(keycloakConfiguration: KeycloakConfiguration): Either<PbkException, Unit> =
        bind {
            val defined =
                clientOpenIdPropertiesList
                    .filter { it.authenticationFlow != null }
                    .map { it.name }
                    .toSet()
            val required =
                keycloakConfiguration
                    .realms
                    .flatMap { it.clientsOpenId }
                    .filter { it.authenticationFlowRequired }
                    .map { it.clientId }
                    .toSet()

            with(required - defined) {
                if (this.isNotEmpty()) {
                    bindException { MissingOpenIdClientsAuthFlowPropertyException(this) }
                }
            }
        }
}
