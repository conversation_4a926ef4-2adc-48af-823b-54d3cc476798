package cz.partners.component.keycloak.migration.service.keycloak

import arrow.core.Either
import cz.partners.component.keycloak.migration.configuration.UnmanagedAttributePolicy
import cz.pbktechnology.platform.common.exception.PbkException
import cz.pbktechnology.platform.common.helper.bind
import jakarta.inject.Singleton
import mu.KotlinLogging
import org.keycloak.admin.v19.client.UsersApiClient
import org.keycloak.admin.v19.model.UPConfig
import org.keycloak.admin.v19.model.UnmanagedAttributePolicy as ApiUnmanagedAttributePolicy

@Singleton
class UserProfileService(
    private val usersApiClient: UsersApiClient,
) {
    private val logger = KotlinLogging.logger { }

    suspend fun configureUserProfile(
        realm: String,
        unmanagedAttributePolicy: UnmanagedAttributePolicy,
    ): Either<PbkException, Unit> =
        bind {
            logger.info { "Configuring user profile for realm [$realm] with unmanagedAttributePolicy [$unmanagedAttributePolicy]" }
            
            // Get current user profile configuration
            val currentConfig = !usersApiClient.realmUsersProfileGet(realm)

            // Update the configuration with the new unmanagedAttributePolicy
            val updatedConfig = currentConfig.copy(
                unmanagedAttributePolicy = unmanagedAttributePolicy.toApiUnmanagedAttributePolicy()
            )

            // Set the updated configuration
            !usersApiClient.realmUsersProfilePut(realm, updatedConfig)
            
            logger.info { "User profile configured successfully for realm [$realm]" }
        }

    private fun UnmanagedAttributePolicy.toApiUnmanagedAttributePolicy(): ApiUnmanagedAttributePolicy =
        when (this) {
            UnmanagedAttributePolicy.ENABLED -> ApiUnmanagedAttributePolicy.ENABLED
            UnmanagedAttributePolicy.ADMIN_VIEW -> ApiUnmanagedAttributePolicy.ADMIN_VIEW
            UnmanagedAttributePolicy.ADMIN_EDIT -> ApiUnmanagedAttributePolicy.ADMIN_EDIT
        }
}
