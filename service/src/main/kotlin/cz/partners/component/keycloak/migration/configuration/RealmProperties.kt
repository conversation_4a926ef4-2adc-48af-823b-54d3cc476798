package cz.partners.component.keycloak.migration.configuration

import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import java.time.Duration

@EachProperty("realm")
class RealmProperties(
    @param:Parameter var name: String,
) {
    var frontendUrl: String? = null

    lateinit var tokenLifespan: TokenLifespan

    @ConfigurationProperties("token-lifespan")
    class TokenLifespan {
        lateinit var accessToken: Duration

        lateinit var ssoSession: Duration

        lateinit var ssoSessionMax: Duration

        lateinit var clientSession: Duration

        lateinit var clientSessionMax: Duration

        lateinit var offlineSession: Duration

        var offlineSessionMaxEnabled: Boolean = false

        lateinit var offlineSessionMax: Duration
    }
}
