/**
 * Keycloak Auth REST API
 *
 * This is a REST API reference for the Keycloak Auth
 *
 * The version of the OpenAPI document: 18.0
 *
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport",
)

package cz.partners.component.keycloak.migration.token.model

import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.core.annotation.Introspected

/**
 *
 *
 * @param accessToken
 * @param expiresIn
 * @param refreshToken
 * @param refreshExpiresIn
 * @param tokenType
 * @param idToken
 * @param notBeforePolicy
 * @param sessionState
 * @param scope
 */

@Introspected
data class TokenResponse(
    @field:JsonProperty("access_token")
    val accessToken: kotlin.String,
    @field:JsonProperty("expires_in")
    val expiresIn: kotlin.Int,
    @field:JsonProperty("refresh_token")
    val refreshToken: kotlin.String? = null,
    @field:JsonProperty("refresh_expires_in")
    val refreshExpiresIn: kotlin.Int? = null,
    @field:JsonProperty("token_type")
    val tokenType: kotlin.String? = null,
    @field:JsonProperty("id_token")
    val idToken: kotlin.String? = null,
    @field:JsonProperty("not-before-policy")
    val notBeforePolicy: kotlin.Int? = null,
    @field:JsonProperty("session_state")
    val sessionState: kotlin.String? = null,
    @field:JsonProperty("scope")
    val scope: kotlin.String? = null,
)
