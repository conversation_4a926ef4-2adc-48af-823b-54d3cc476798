package cz.partners.component.keycloak.migration.service.keycloak

import cz.partners.component.keycloak.migration.configuration.RealmConfiguration
import cz.partners.component.keycloak.migration.configuration.UnmanagedAttributePolicy
import cz.partners.component.keycloak.migration.helpers.ContainerTest
import cz.partners.component.keycloak.migration.token.RenewAccessTokenService
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.util.EitherUtil.getOrThrow
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import jakarta.inject.Inject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.keycloak.admin.v19.client.RealmsAdminApiClient
import org.keycloak.admin.v19.client.UsersApiClient
import org.keycloak.admin.v19.model.UnmanagedAttributePolicy as ApiUnmanagedAttributePolicy

@MicronautTest()
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RealmServiceTest : ContainerTest() {
    @Inject
    private lateinit var realmService: RealmService

    @Inject
    private lateinit var usersApiClient: UsersApiClient

    @Inject
    private lateinit var renewAccessTokenService: RenewAccessTokenService

    @Inject
    private lateinit var realmsAdminApiClient: RealmsAdminApiClient

    private val testRealmName = "test-realm-service"

    @BeforeEach
    fun setup(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            renewAccessTokenService.renewAccessToken().getOrThrow()
        }

    @AfterEach
    fun cleanup(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            renewAccessTokenService.renewAccessToken().getOrThrow()
            // Try to delete the realm if it exists
            try {
                realmsAdminApiClient.realmDelete(testRealmName).getOrThrow()
            } catch (e: Exception) {
                // Ignore if realm doesn't exist
            }
        }

    @Test
    fun `should create realm with default ENABLED unmanaged attribute policy`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            // Given
            val realmConfiguration = RealmConfiguration(
                name = testRealmName,
            )

            // When
            realmService.createRealm(realmConfiguration).getOrThrow()

            // Then
            val realm = realmsAdminApiClient.realmGet(testRealmName).getOrThrow()
            assertThat(realm.realm).isEqualTo(testRealmName)
            assertThat(realm.enabled).isTrue()

            val userProfile = usersApiClient.realmUsersProfileGet(testRealmName).getOrThrow()
            assertThat(userProfile.unmanagedAttributePolicy).isEqualTo(ApiUnmanagedAttributePolicy.ENABLED)
        }

    @Test
    fun `should create realm with ADMIN_VIEW unmanaged attribute policy`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            // Given
            val realmConfiguration = RealmConfiguration(
                name = testRealmName,
                unmanagedAttributePolicy = UnmanagedAttributePolicy.ADMIN_VIEW
            )

            // When
            realmService.createRealm(realmConfiguration).getOrThrow()

            // Then
            val userProfile = usersApiClient.realmUsersProfileGet(testRealmName).getOrThrow()
            assertThat(userProfile.unmanagedAttributePolicy).isEqualTo(ApiUnmanagedAttributePolicy.ADMIN_VIEW)
        }

    @Test
    fun `should update existing realm with new unmanaged attribute policy`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            // Given - create realm with ADMIN_VIEW policy
            val initialConfiguration = RealmConfiguration(
                name = testRealmName,
                unmanagedAttributePolicy = UnmanagedAttributePolicy.ADMIN_VIEW
            )
            realmService.createRealm(initialConfiguration).getOrThrow()

            val initialProfile = usersApiClient.realmUsersProfileGet(testRealmName).getOrThrow()
            assertThat(initialProfile.unmanagedAttributePolicy).isEqualTo(ApiUnmanagedAttributePolicy.ADMIN_VIEW)

            // When - update realm with ENABLED policy
            val updatedConfiguration = RealmConfiguration(
                name = testRealmName,
                unmanagedAttributePolicy = UnmanagedAttributePolicy.ENABLED
            )
            realmService.createRealm(updatedConfiguration).getOrThrow()

            // Then
            val updatedProfile = usersApiClient.realmUsersProfileGet(testRealmName).getOrThrow()
            assertThat(updatedProfile.unmanagedAttributePolicy).isEqualTo(ApiUnmanagedAttributePolicy.ENABLED)
        }
}
