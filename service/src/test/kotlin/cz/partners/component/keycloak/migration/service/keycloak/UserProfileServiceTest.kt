package cz.partners.component.keycloak.migration.service.keycloak

import cz.partners.component.keycloak.migration.configuration.UnmanagedAttributePolicy
import cz.partners.component.keycloak.migration.helpers.ContainerTest
import cz.partners.component.keycloak.migration.token.RenewAccessTokenService
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.util.EitherUtil.getOrThrow
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import jakarta.inject.Inject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.keycloak.admin.v19.client.RealmsAdminApiClient
import org.keycloak.admin.v19.client.UsersApiClient
import org.keycloak.admin.v19.model.RealmRepresentation
import org.keycloak.admin.v19.model.UnmanagedAttributePolicy as ApiUnmanagedAttributePolicy

@MicronautTest()
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserProfileServiceTest : ContainerTest() {
    @Inject
    private lateinit var userProfileService: UserProfileService

    @Inject
    private lateinit var usersApiClient: UsersApiClient

    @Inject
    private lateinit var renewAccessTokenService: RenewAccessTokenService

    @Inject
    private lateinit var realmsAdminApiClient: RealmsAdminApiClient

    private val testRealmName = "test-user-profile-realm"

    @BeforeEach
    fun createRealm(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            renewAccessTokenService.renewAccessToken().getOrThrow()
            realmsAdminApiClient
                .rootPost(
                    RealmRepresentation(
                        realm = testRealmName,
                        enabled = true,
                    ),
                )
                .getOrThrow()
        }

    @AfterEach
    fun deleteRealm(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            renewAccessTokenService.renewAccessToken().getOrThrow()
            realmsAdminApiClient.realmDelete(testRealmName).getOrThrow()
        }

    @Test
    fun `should configure user profile with ENABLED policy`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            // When
            userProfileService.configureUserProfile(testRealmName, UnmanagedAttributePolicy.ENABLED).getOrThrow()

            // Then
            val userProfile = usersApiClient.realmUsersProfileGet(testRealmName).getOrThrow()
            assertThat(userProfile.unmanagedAttributePolicy).isEqualTo(ApiUnmanagedAttributePolicy.ENABLED)
        }

    @Test
    fun `should configure user profile with ADMIN_VIEW policy`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            // When
            userProfileService.configureUserProfile(testRealmName, UnmanagedAttributePolicy.ADMIN_VIEW).getOrThrow()

            // Then
            val userProfile = usersApiClient.realmUsersProfileGet(testRealmName).getOrThrow()
            assertThat(userProfile.unmanagedAttributePolicy).isEqualTo(ApiUnmanagedAttributePolicy.ADMIN_VIEW)
        }

    @Test
    fun `should update existing user profile configuration`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            // Given - first set to ADMIN_VIEW
            userProfileService.configureUserProfile(testRealmName, UnmanagedAttributePolicy.ADMIN_VIEW).getOrThrow()
            val initialProfile = usersApiClient.realmUsersProfileGet(testRealmName).getOrThrow()
            assertThat(initialProfile.unmanagedAttributePolicy).isEqualTo(ApiUnmanagedAttributePolicy.ADMIN_VIEW)

            // When - update to ENABLED
            userProfileService.configureUserProfile(testRealmName, UnmanagedAttributePolicy.ENABLED).getOrThrow()

            // Then
            val updatedProfile = usersApiClient.realmUsersProfileGet(testRealmName).getOrThrow()
            assertThat(updatedProfile.unmanagedAttributePolicy).isEqualTo(ApiUnmanagedAttributePolicy.ENABLED)
        }
}
