package cz.partners.component.keycloak.migration.helpers

import io.micronaut.test.support.TestPropertyProvider
import mu.KotlinLogging
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.output.Slf4jLogConsumer
import org.testcontainers.junit.jupiter.Container

abstract class ContainerTest : TestPropertyProvider {
    final override fun getProperties(): Map<String, String> =
        mapOf(
            "keycloak.admin.endpoint" to "$keycloakUrl/realms/master/protocol/openid-connect/token",
            "micronaut.http.services.org-keycloak-admin-v19-client.url" to keycloakUrl,
            "micronaut.http.services.org-keycloak-openidconnect-v19-client.url" to keycloakUrl,
        )

    companion object {
        private val logger = KotlinLogging.logger { }

        private const val KEYCLOAK_PORT = 8080

        @Container
        private val keycloakTestContainer =
            GenericContainer("harbor-aws.internal.pbk-lab.tech/infrastructure/keycloak-image-pbk:26.3.0-PTS-RC1")
                .withCommand("start")
                .withExposedPorts(KEYCLOAK_PORT)
                .withEnv("KC_HOSTNAME", "localhost")
                .withEnv("KC_DB", "dev-file")
                .withEnv("KEYCLOAK_ADMIN", "admin")
                .withEnv("KEYCLOAK_ADMIN_PASSWORD", "admin")
                .withLogConsumer(Slf4jLogConsumer(logger).withPrefix("[Keycloak] "))
                .also { it.start() }

        private val keycloakUrl by lazy {
            val port = keycloakTestContainer.getMappedPort(KEYCLOAK_PORT).toString()
            val url = "http://${keycloakTestContainer.host}:$port"
            logger.info { "Keycloak test container is listening on [$url]" }
            url
        }
    }
}
