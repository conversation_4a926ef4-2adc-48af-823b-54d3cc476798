package cz.partners.component.keycloak.migration.service.keycloak

import cz.partners.component.keycloak.migration.helpers.ContainerTest
import cz.partners.component.keycloak.migration.service.MigrationService
import cz.pbktechnology.platform.common.context.ContextConfiguration
import io.micronaut.context.annotation.Property
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import jakarta.inject.Inject
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@MicronautTest(startApplication = false)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Property(name = "keycloak-configuration-file", value = "../keycloak/configuration.yaml")
@Property(name = "authentication-flows-configuration-file", value = "../keycloak/authentication-flows.yaml")
class FullMigrationTest : ContainerTest() {
    @Inject
    lateinit var migrationService: MigrationService

    @Test
    fun `Run full migration script`() {
        val results =
            runBlocking(ContextConfiguration.initialContext()) {
                migrationService.migrate()
            }
        Assertions.assertTrue(results.isRight())
    }
}
