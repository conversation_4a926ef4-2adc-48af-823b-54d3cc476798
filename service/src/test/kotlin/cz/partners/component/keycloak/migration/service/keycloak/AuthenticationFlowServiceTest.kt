package cz.partners.component.keycloak.migration.service.keycloak

import cz.partners.component.keycloak.migration.configuration.AuthenticationFlow
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlowConfigProperties
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlowDeclaration
import cz.partners.component.keycloak.migration.configuration.AuthenticationFlowExecution
import cz.partners.component.keycloak.migration.configuration.AuthenticatorFlowExecutionRequirement
import cz.partners.component.keycloak.migration.configuration.ExecutionType
import cz.partners.component.keycloak.migration.helpers.ContainerTest
import cz.partners.component.keycloak.migration.token.RenewAccessTokenService
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.util.EitherUtil.getOrThrow
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import jakarta.inject.Inject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.keycloak.admin.v19.client.AuthenticationFlowApiClient
import org.keycloak.admin.v19.client.ProvidersApiClient
import org.keycloak.admin.v19.client.RealmsAdminApiClient
import org.keycloak.admin.v19.model.AuthenticationFlowExecutionRequirement
import org.keycloak.admin.v19.model.AuthenticationFlowRepresentation
import org.keycloak.admin.v19.model.RealmRepresentation

@MicronautTest()
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AuthenticationFlowServiceTest : ContainerTest() {
    @Inject
    private lateinit var authFlowClient: AuthenticationFlowApiClient

    @Inject
    private lateinit var providersApiClient: ProvidersApiClient

    @Inject
    private lateinit var renewAccessTokenService: RenewAccessTokenService

    @Inject
    private lateinit var realmsAdminApiClient: RealmsAdminApiClient

    @BeforeEach
    fun createRealm(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            // TODO: PTS-811 - token refreshing should be more automatic
            renewAccessTokenService.renewAccessToken().getOrThrow()
            realmsAdminApiClient
                .rootPost(
                    RealmRepresentation(
                        realm = REALM,
                        enabled = true,
                    ),
                ).getOrThrow()
            renewAccessTokenService.renewAccessToken().getOrThrow()
        }

    @AfterEach
    fun deleteRealm(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            realmsAdminApiClient.realmDelete(REALM).getOrThrow()
        }

    @Test
    fun `creates flow without executions`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val flowName = "without-executions"
            val service =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions = emptyList(),
                        ),
                    ),
                )

            // Check that the flow doesn't exist yet
            assertThat(getAuthFlow(flowName)).isNull()

            // Create the auth flow
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            getAuthFlow(flowName)
                .also {
                    // Check that it was created with no executions
                    checkNotNull(it)

                    // Check that no executions were created
                    assertThat(it.authenticationExecutions).isEmpty()
                }
        }

    @Test
    fun `handles error when creating flow that already exists`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val flowName = "without-executions"
            val service =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions = emptyList(),
                        ),
                    ),
                )

            // Check that the flow doesn't exist yet
            assertThat(getAuthFlow(flowName)).isNull()

            // Create the auth flow
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            getAuthFlow(flowName)
                .also {
                    // Check that it still exists
                    assertThat(it).isNotNull
                }

            // Create again
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            getAuthFlow(flowName)
                .also {
                    // Check that it still exists
                    assertThat(it).isNotNull
                }
        }

    @Test
    fun `creates new flow with top level executions`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val flowName = "with-top-level-executions"
            val service =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions =
                                listOf(
                                    AuthenticationFlowExecution(
                                        alias = "first",
                                        description = "",
                                        type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.REQUIRED,
                                        executions = emptyList(),
                                    ),
                                    AuthenticationFlowExecution(
                                        alias = "second",
                                        description = "",
                                        type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.ALTERNATIVE,
                                        executions = emptyList(),
                                    ),
                                ),
                        ),
                    ),
                )

            // Check that the flow doesn't exist yet
            assertThat(getAuthFlow(flowName)).isNull()

            // Create the auth flow
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            getAuthFlow(flowName)
                .also {
                    // Check that it was created with
                    checkNotNull(it)

                    // Check that two executions exist
                    assertThat(it.authenticationExecutions?.size).isEqualTo(2)

                    // Check that first execution matches the config
                    val firstExecution = it.authenticationExecutions?.get(0)
                    assertThat(firstExecution?.priority).isEqualTo(0)
                    assertThat(firstExecution?.requirement).isEqualTo(AuthenticationFlowExecutionRequirement.REQUIRED)
                    assertThat(firstExecution?.authenticator).isEqualTo("verify-auth-methods-authenticator")

                    // Check that second execution matches the config
                    val secondExecution = it.authenticationExecutions?.get(1)
                    assertThat(secondExecution?.priority).isEqualTo(1)
                    assertThat(secondExecution?.requirement).isEqualTo(AuthenticationFlowExecutionRequirement.ALTERNATIVE)
                    assertThat(secondExecution?.authenticator).isEqualTo("verify-auth-methods-authenticator")
                }
        }

    @Test
    fun `adds top level executions to existing flow`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val flowName = "with-top-level-executions"
            val service =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions = emptyList(),
                        ),
                    ),
                )
            val serviceWithUpdatedExecutions =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions =
                                listOf(
                                    AuthenticationFlowExecution(
                                        alias = "first",
                                        description = "",
                                        type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.REQUIRED,
                                        executions = emptyList(),
                                    ),
                                    AuthenticationFlowExecution(
                                        alias = "second",
                                        description = "",
                                        type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.ALTERNATIVE,
                                        executions = emptyList(),
                                    ),
                                ),
                        ),
                    ),
                )

            // Check that the flow doesn't exist yet
            assertThat(getAuthFlow(flowName)).isNull()

            // Create the auth flow
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            getAuthFlow(flowName)
                .also {
                    // Check that it was created with no executions
                    checkNotNull(it)

                    // Check that no executions were created
                    assertThat(it.authenticationExecutions).isEmpty()
                }

            // Update the auth flow
            serviceWithUpdatedExecutions
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            getAuthFlow(flowName)
                .also {
                    // Check that it wasn't removed
                    checkNotNull(it)

                    // Check that two executions exist
                    assertThat(it.authenticationExecutions?.size).isEqualTo(2)

                    // Check that first execution matches the config
                    val firstExecution = it.authenticationExecutions?.get(0)
                    assertThat(firstExecution?.priority).isEqualTo(0)
                    assertThat(firstExecution?.requirement).isEqualTo(AuthenticationFlowExecutionRequirement.REQUIRED)
                    assertThat(firstExecution?.authenticator).isEqualTo("verify-auth-methods-authenticator")

                    // Check that second execution matches the config
                    val secondExecution = it.authenticationExecutions?.get(1)
                    assertThat(secondExecution?.priority).isEqualTo(1)
                    assertThat(secondExecution?.requirement).isEqualTo(AuthenticationFlowExecutionRequirement.ALTERNATIVE)
                    assertThat(secondExecution?.authenticator).isEqualTo("verify-auth-methods-authenticator")
                }
        }

    @Test
    fun `removes top level executions from existing flow`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val flowName = "with-top-level-executions"
            val service =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions =
                                listOf(
                                    AuthenticationFlowExecution(
                                        alias = "first",
                                        description = "",
                                        type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.REQUIRED,
                                        executions = emptyList(),
                                    ),
                                    AuthenticationFlowExecution(
                                        alias = "second",
                                        description = "",
                                        type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.ALTERNATIVE,
                                        executions = emptyList(),
                                    ),
                                ),
                        ),
                    ),
                )
            val serviceWithUpdatedExecutions =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions = emptyList(),
                        ),
                    ),
                )

            // Check that the flow doesn't exist yet
            assertThat(getAuthFlow(flowName)).isNull()

            // Create the auth flow
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            getAuthFlow(flowName)
                .also {
                    // Check that the flow was created
                    checkNotNull(it)

                    // Check that two executions exist
                    assertThat(it.authenticationExecutions?.size).isEqualTo(2)

                    // Check that first execution matches the config
                    val firstExecution = it.authenticationExecutions?.get(0)
                    assertThat(firstExecution?.priority).isEqualTo(0)
                    assertThat(firstExecution?.requirement).isEqualTo(AuthenticationFlowExecutionRequirement.REQUIRED)
                    assertThat(firstExecution?.authenticator).isEqualTo("verify-auth-methods-authenticator")

                    // Check that second execution matches the config
                    val secondExecution = it.authenticationExecutions?.get(1)
                    assertThat(secondExecution?.priority).isEqualTo(1)
                    assertThat(secondExecution?.requirement).isEqualTo(AuthenticationFlowExecutionRequirement.ALTERNATIVE)
                    assertThat(secondExecution?.authenticator).isEqualTo("verify-auth-methods-authenticator")
                }

            // Update the auth flow
            serviceWithUpdatedExecutions
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            getAuthFlow(flowName)
                .also {
                    // Check that it wasn't removed
                    checkNotNull(it)

                    // Check that executions were removed
                    assertThat(it.authenticationExecutions).isEmpty()
                }
        }

    @Test
    fun `creates flow with nested executions`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val flowName = "with-nested-executions"
            val service =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions =
                                listOf(
                                    AuthenticationFlowExecution(
                                        alias = "top",
                                        description = "",
                                        type = ExecutionType.GENERIC_SUBFLOW,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.CONDITIONAL,
                                        executions =
                                            listOf(
                                                AuthenticationFlowExecution(
                                                    alias = "nested-first",
                                                    description = "",
                                                    type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                                    config = null,
                                                    requirement = AuthenticatorFlowExecutionRequirement.REQUIRED,
                                                    executions = emptyList(),
                                                ),
                                                AuthenticationFlowExecution(
                                                    alias = "nested-second",
                                                    description = "",
                                                    type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                                    config = null,
                                                    requirement = AuthenticatorFlowExecutionRequirement.ALTERNATIVE,
                                                    executions = emptyList(),
                                                ),
                                            ),
                                    ),
                                ),
                        ),
                    ),
                )

            // Check that the flow doesn't exist yet
            assertThat(getAuthFlow(flowName)).isNull()

            // Create the auth flow
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            // Check that the auth flow was created
            assertThat(getAuthFlow(flowName)).isNotNull()

            // Check that the executions were created
            getExecutionsForFlow(flowName).also {
                assertThat(it.size).isEqualTo(3)

                assertThat(it[0].priority).isEqualTo(0)
                assertThat(it[0].level).isEqualTo(0)
                assertThat(it[0].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.CONDITIONAL)

                assertThat(it[1].priority).isEqualTo(0)
                assertThat(it[1].level).isEqualTo(1)
                assertThat(it[1].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.REQUIRED)

                assertThat(it[2].priority).isEqualTo(1)
                assertThat(it[2].level).isEqualTo(1)
                assertThat(it[2].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.ALTERNATIVE)
            }
        }

    @Test
    fun `adds nested executions to existing flow`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val flowName = "with-nested-executions"
            val service =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions =
                                listOf(
                                    AuthenticationFlowExecution(
                                        alias = "top",
                                        description = "",
                                        type = ExecutionType.GENERIC_SUBFLOW,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.CONDITIONAL,
                                        executions = emptyList(),
                                    ),
                                ),
                        ),
                    ),
                )
            val serviceWithUpdatedExecutions =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions =
                                listOf(
                                    AuthenticationFlowExecution(
                                        alias = "top",
                                        description = "",
                                        type = ExecutionType.GENERIC_SUBFLOW,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.CONDITIONAL,
                                        executions =
                                            listOf(
                                                AuthenticationFlowExecution(
                                                    alias = "nested-first",
                                                    description = "",
                                                    type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                                    config = null,
                                                    requirement = AuthenticatorFlowExecutionRequirement.REQUIRED,
                                                    executions = emptyList(),
                                                ),
                                                AuthenticationFlowExecution(
                                                    alias = "nested-second",
                                                    description = "",
                                                    type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                                    config = null,
                                                    requirement = AuthenticatorFlowExecutionRequirement.ALTERNATIVE,
                                                    executions = emptyList(),
                                                ),
                                            ),
                                    ),
                                ),
                        ),
                    ),
                )

            // Check that the flow doesn't exist yet
            assertThat(getAuthFlow(flowName)).isNull()

            // Create the auth flow
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            // Check that the auth flow was created
            assertThat(getAuthFlow(flowName)).isNotNull()

            // Check that the top level execution was created
            getExecutionsForFlow(flowName).also {
                assertThat(it.size).isEqualTo(1)

                assertThat(it[0].priority).isEqualTo(0)
                assertThat(it[0].level).isEqualTo(0)
                assertThat(it[0].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.CONDITIONAL)
            }

            // Update the auth flow
            serviceWithUpdatedExecutions
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            // Check that the auth flow wasn't removed
            assertThat(getAuthFlow(flowName)).isNotNull()

            // Check that the nested executions were created
            getExecutionsForFlow(flowName).also {
                assertThat(it.size).isEqualTo(3)

                assertThat(it[0].priority).isEqualTo(0)
                assertThat(it[0].level).isEqualTo(0)
                assertThat(it[0].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.CONDITIONAL)

                assertThat(it[1].priority).isEqualTo(0)
                assertThat(it[1].level).isEqualTo(1)
                assertThat(it[1].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.REQUIRED)

                assertThat(it[2].priority).isEqualTo(1)
                assertThat(it[2].level).isEqualTo(1)
                assertThat(it[2].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.ALTERNATIVE)
            }
        }

    @Test
    fun `removes nested executions from existing flow`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val flowName = "with-nested-executions"
            val service =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions =
                                listOf(
                                    AuthenticationFlowExecution(
                                        alias = "top",
                                        description = "",
                                        type = ExecutionType.GENERIC_SUBFLOW,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.CONDITIONAL,
                                        executions =
                                            listOf(
                                                AuthenticationFlowExecution(
                                                    alias = "nested-first",
                                                    description = "",
                                                    type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                                    config = null,
                                                    requirement = AuthenticatorFlowExecutionRequirement.REQUIRED,
                                                    executions = emptyList(),
                                                ),
                                                AuthenticationFlowExecution(
                                                    alias = "nested-second",
                                                    description = "",
                                                    type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                                    config = null,
                                                    requirement = AuthenticatorFlowExecutionRequirement.ALTERNATIVE,
                                                    executions = emptyList(),
                                                ),
                                            ),
                                    ),
                                ),
                        ),
                    ),
                )
            val serviceWithUpdatedExecutions =
                getService(
                    listOf(
                        AuthenticationFlow(
                            name = flowName,
                            description = null,
                            executions =
                                listOf(
                                    AuthenticationFlowExecution(
                                        alias = "top",
                                        description = "",
                                        type = ExecutionType.GENERIC_SUBFLOW,
                                        config = null,
                                        requirement = AuthenticatorFlowExecutionRequirement.CONDITIONAL,
                                        executions =
                                            listOf(
                                                AuthenticationFlowExecution(
                                                    alias = "nested-first",
                                                    description = "",
                                                    type = ExecutionType.VERIFY_AUTHENTICATION_METHODS,
                                                    config = null,
                                                    requirement = AuthenticatorFlowExecutionRequirement.REQUIRED,
                                                    executions = emptyList(),
                                                ),
                                            ),
                                    ),
                                ),
                        ),
                    ),
                )

            // Check that the flow doesn't exist yet
            assertThat(getAuthFlow(flowName)).isNull()

            // Create the auth flow
            service
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            // Check that the auth flow was created
            assertThat(getAuthFlow(flowName)).isNotNull()

            // Check that the executions were created
            getExecutionsForFlow(flowName).also {
                assertThat(it.size).isEqualTo(3)

                assertThat(it[0].priority).isEqualTo(0)
                assertThat(it[0].level).isEqualTo(0)
                assertThat(it[0].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.CONDITIONAL)

                assertThat(it[1].priority).isEqualTo(0)
                assertThat(it[1].level).isEqualTo(1)
                assertThat(it[1].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.REQUIRED)

                assertThat(it[2].priority).isEqualTo(1)
                assertThat(it[2].level).isEqualTo(1)
                assertThat(it[2].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.ALTERNATIVE)
            }

            // Update the auth flow
            serviceWithUpdatedExecutions
                .createAuthenticationFlow(realm = REALM, authFlowName = flowName)
                .getOrThrow()

            // Check that the auth flow wasn't removed
            assertThat(getAuthFlow(flowName)).isNotNull()

            // Check that the only two executions remain
            getExecutionsForFlow(flowName).also {
                assertThat(it.size).isEqualTo(2)

                assertThat(it[0].priority).isEqualTo(0)
                assertThat(it[0].level).isEqualTo(0)
                assertThat(it[0].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.CONDITIONAL)

                assertThat(it[1].priority).isEqualTo(0)
                assertThat(it[1].level).isEqualTo(1)
                assertThat(it[1].requirement).isEqualTo(AuthenticationFlowExecutionRequirement.REQUIRED)
            }
        }

    private fun getService(
        authenticationFlows: List<AuthenticationFlow>,
        configPropertiesList: List<AuthenticationFlowConfigProperties> = emptyList(),
    ) = AuthenticationFlowService(
        authFlowClient = authFlowClient,
        providersApiClient = providersApiClient,
        configPropertiesList = configPropertiesList,
        authenticationFlowDeclaration = AuthenticationFlowDeclaration(authenticationFlows),
    )

    private suspend fun getAuthFlow(name: String): AuthenticationFlowRepresentation? {
        val authFlows = authFlowClient.getAuthenticationFlows(realm = REALM).getOrThrow()
        return authFlows.find { it.alias == name }
    }

    private suspend fun getExecutionsForFlow(name: String) =
        authFlowClient.getExecutionsForAuthenticationFlow(realm = REALM, flowAlias = name).getOrThrow()

    companion object {
        const val REALM = "test"
    }
}
