package cz.partners.component.keycloak.migration.service.keycloak

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import cz.partners.component.keycloak.migration.configuration.ClientAssignClientRoleConfiguration
import cz.partners.component.keycloak.migration.configuration.ClientOpenIdConfiguration
import cz.partners.component.keycloak.migration.helpers.ContainerTest
import cz.partners.component.keycloak.migration.token.RenewAccessTokenService
import cz.pbktechnology.platform.common.context.ContextConfiguration
import cz.pbktechnology.platform.common.util.EitherUtil.getOrThrow
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import jakarta.inject.Inject
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.keycloak.admin.v19.client.ClientsApiClient
import org.keycloak.admin.v19.client.RealmsAdminApiClient
import org.keycloak.admin.v19.client.RoleMapperApiClient
import org.keycloak.admin.v19.client.RolesApiClient
import org.keycloak.admin.v19.model.ClientMappingsRepresentation
import org.keycloak.admin.v19.model.ClientRepresentation
import org.keycloak.admin.v19.model.RealmRepresentation
import org.keycloak.admin.v19.model.RoleRepresentation
import java.util.UUID
import kotlin.test.Test

@MicronautTest(startApplication = false)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AssignRoleServiceTest : ContainerTest() {
    @Inject
    private lateinit var assignRoleService: AssignRoleService

    @Inject
    private lateinit var renewAccessTokenService: RenewAccessTokenService

    @Inject
    private lateinit var realmsAdminApiClient: RealmsAdminApiClient

    @Inject
    private lateinit var rolesApiClient: RolesApiClient

    @Inject
    private lateinit var clientsApiClient: ClientsApiClient

    @Inject
    private lateinit var roleMapperApiClient: RoleMapperApiClient

    @Inject
    private lateinit var objectMapper: ObjectMapper

    private lateinit var realmName: String

    @BeforeEach
    fun createRealm(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            realmName = UUID.randomUUID().toString()

            // TODO: PTS-811 - token refreshing should be more automatic
            renewAccessTokenService.renewAccessToken().getOrThrow()
            realmsAdminApiClient
                .rootPost(
                    RealmRepresentation(
                        realm = realmName,
                        enabled = true,
                    ),
                ).getOrThrow()
            renewAccessTokenService.renewAccessToken().getOrThrow()
        }

    @AfterEach
    fun deleteRealm(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            realmsAdminApiClient.realmDelete(realmName).getOrThrow()
        }

    @Test
    fun `assigns roles to a client when the client initially doesn't have any`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val ownerClientName = "owner"
            val toAssignClientName = "to-assign"
            val roleName = "test-role"

            val ownerClientId = createClient("owner")
            val toAssignClientId = createClient(toAssignClientName)

            createRole(name = roleName, clientId = ownerClientId)
            assertThat(
                getAssignedRolesFromClient(
                    clientId = toAssignClientId,
                    fromClientName = ownerClientName,
                ),
            ).isEmpty()

            assignRoleService
                .assignRolesToClients(
                    realm = realmName,
                    clientOpenIdConfiguration =
                        setOf(
                            ClientOpenIdConfiguration(
                                name = toAssignClientName,
                                description = "",
                                assignRoles =
                                    mutableSetOf(
                                        ClientAssignClientRoleConfiguration(
                                            clientName = ownerClientName,
                                            roleName = roleName,
                                        ),
                                    ),
                            ),
                        ),
                ).getOrThrow()

            val assignedRoles =
                getAssignedRolesFromClient(
                    clientId = toAssignClientId,
                    fromClientName = ownerClientName,
                )
            assertThat(assignedRoles).hasSize(1)
            assertThat(assignedRoles[0].name).isEqualTo(roleName)
        }

    @Test
    fun `removes roles from a client that are no longer in configuration`(): Unit =
        runBlocking(ContextConfiguration.initialContext()) {
            val ownerClientName = "owner"
            val toAssignClientName = "to-assign"
            val roleAName = "test-role-a"
            val roleBName = "test-role-b"

            val ownerClientId = createClient("owner")
            val toAssignClientId = createClient(toAssignClientName)

            createRole(name = roleAName, clientId = ownerClientId)
            createRole(name = roleBName, clientId = ownerClientId)
            assertThat(
                getAssignedRolesFromClient(
                    clientId = toAssignClientId,
                    fromClientName = ownerClientName,
                ),
            ).isEmpty()

            // Assign both roles
            run {
                assignRoleService
                    .assignRolesToClients(
                        realm = realmName,
                        clientOpenIdConfiguration =
                            setOf(
                                ClientOpenIdConfiguration(
                                    name = toAssignClientName,
                                    description = "",
                                    assignRoles =
                                        mutableSetOf(
                                            ClientAssignClientRoleConfiguration(
                                                clientName = ownerClientName,
                                                roleName = roleAName,
                                            ),
                                            ClientAssignClientRoleConfiguration(
                                                clientName = ownerClientName,
                                                roleName = roleBName,
                                            ),
                                        ),
                                ),
                            ),
                    ).getOrThrow()

                val assignedRoles =
                    getAssignedRolesFromClient(
                        clientId = toAssignClientId,
                        fromClientName = ownerClientName,
                    )
                assertThat(assignedRoles).hasSize(2)
                assertThat(assignedRoles.map { it.name }).contains(roleAName)
                assertThat(assignedRoles.map { it.name }).contains(roleBName)
            }

            // Assign only A role
            run {
                assignRoleService
                    .assignRolesToClients(
                        realm = realmName,
                        clientOpenIdConfiguration =
                            setOf(
                                ClientOpenIdConfiguration(
                                    name = toAssignClientName,
                                    description = "",
                                    assignRoles =
                                        mutableSetOf(
                                            ClientAssignClientRoleConfiguration(
                                                clientName = ownerClientName,
                                                roleName = roleAName,
                                            ),
                                        ),
                                ),
                            ),
                    ).getOrThrow()

                val assignedRoles =
                    getAssignedRolesFromClient(
                        clientId = toAssignClientId,
                        fromClientName = ownerClientName,
                    )
                assertThat(assignedRoles).hasSize(1)
                assertThat(assignedRoles[0].name).isEqualTo(roleAName)
            }
        }

    private suspend fun createClient(name: String): String {
        clientsApiClient
            .realmClientsPost(
                realm = realmName,
                clientRepresentation =
                    ClientRepresentation(
                        clientId = name,
                        publicClient = true,
                        directAccessGrantsEnabled = true,
                        enabled = true,
                        serviceAccountsEnabled = true,
                        standardFlowEnabled = true,
                        implicitFlowEnabled = true,
                    ),
            ).getOrThrow()

        val client = clientsApiClient.realmClientsGet(realm = realmName, clientId = name).getOrThrow().first()
        return checkNotNull(client.id)
    }

    private suspend fun createRole(
        name: String,
        clientId: String,
    ) {
        rolesApiClient
            .realmClientsClientIdRolesPost(
                realm = realmName,
                clientId = clientId,
                roleRepresentation =
                    RoleRepresentation(
                        name = name,
                    ),
            ).getOrThrow()
    }

    private suspend fun getAssignedRolesFromClient(
        clientId: String,
        fromClientName: String,
    ): List<RoleRepresentation> {
        val serviceUser =
            clientsApiClient
                .realmClientsClientIdServiceAccountUserGet(realm = realmName, clientId = clientId)
                .getOrThrow()

        // TODO: PTS-811 Move to integration layer
        val fromClientRoleMappings =
            roleMapperApiClient
                .realmUsersUserIdRoleMappingsGet(
                    realm = realmName,
                    userId = checkNotNull(serviceUser.id),
                ).getOrThrow()
                .clientMappings
                ?.map { objectMapper.convertValue<ClientMappingsRepresentation>(it.value) }
                ?.find { it.client == fromClientName }
                ?.mappings
                ?: emptyList()

        return fromClientRoleMappings
    }
}
