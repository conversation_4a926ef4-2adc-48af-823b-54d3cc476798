plugins {
    id("cz.pbktechnology.platform.common.gradle-plugin.module")
}

val platformCommonVersion: String by project

platform {
    micronaut {
        enabled = true
        isHttpService = true
        mainClass = "cz.partners.component.keycloak.migration.Application"
        configurationValidation {
            disableSchemas = listOf("infoEndpointEnabled")
        }
    }
}

tasks.test {
    useJUnitPlatform()
}

apply("$rootDir/openapi/openapi.gradle.kts")

sourceSets {
    main {
        java {
            srcDirs("build/openapi-generated/src")
        }
        resources {
            srcDirs("build/openapi-generated/resources")
        }
    }
}

dependencies {
    kapt(platform("cz.pbktechnology.platform.common:bom:$platformCommonVersion"))
    implementation(platform("cz.pbktechnology.platform.common:bom:$platformCommonVersion"))
    implementation("cz.pbktechnology.platform.common:core:$platformCommonVersion")

    implementation("cz.pbktechnology.platform.common:api:$platformCommonVersion")
    implementation("cz.pbktechnology.platform.common:security:$platformCommonVersion")

    kapt("io.micronaut:micronaut-inject-java")
    kapt("io.micronaut:micronaut-validation")
    kapt("io.micronaut.data:micronaut-data-processor")
    kapt("io.micronaut.openapi:micronaut-openapi")

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactive")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-jdk8")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-slf4j")

    implementation(kotlin("stdlib"))
    implementation("javax.annotation:javax.annotation-api")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("io.micronaut.kotlin:micronaut-kotlin-runtime")
    implementation("io.micronaut.kotlin:micronaut-kotlin-extension-functions")

    implementation("io.micronaut:micronaut-validation")
    implementation("io.micronaut:micronaut-runtime")
    implementation("io.micronaut:micronaut-http-client")
    implementation("io.micronaut:micronaut-inject")
    implementation("io.micronaut.reactor:micronaut-reactor")
    implementation("io.micronaut.reactor:micronaut-reactor-http-client")
    implementation("io.micronaut:micronaut-http-server-netty")

    implementation("io.micronaut:micronaut-management")
    implementation("io.micronaut.micrometer:micronaut-micrometer-core")
    implementation("io.micronaut.micrometer:micronaut-micrometer-registry-prometheus")

    implementation("io.micronaut.security:micronaut-security")
    implementation("io.micronaut.security:micronaut-security-jwt")
    implementation("io.micronaut.security:micronaut-security-oauth2")
    kapt("io.micronaut.security:micronaut-security")

    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")

    implementation("io.github.microutils:kotlin-logging-jvm:2.1.23")
    implementation("cz.pbktechnology.platform.common:logging-logback:$platformCommonVersion")

    implementation("io.swagger.core.v3:swagger-annotations")
    implementation("io.swagger.core.v3:swagger-core")

    implementation("io.arrow-kt:arrow-core")

    implementation("io.github.resilience4j:resilience4j-kotlin")

    implementation("io.micronaut.cache:micronaut-cache-core")
    implementation("io.micronaut.cache:micronaut-cache-caffeine")

    testImplementation(kotlin("test-junit5"))
    testImplementation("org.assertj:assertj-core")
    testImplementation("io.micronaut.test:micronaut-test-junit5")
    kaptTest("io.micronaut:micronaut-inject-java")
    testImplementation("org.testcontainers:junit-jupiter")
}
