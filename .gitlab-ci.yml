include:
  - component: $CI_SERVER_FQDN/banka/shared/cicd/pipelines/gitlab-ci-pipeline-general-dind@1
  - component: $CI_SERVER_FQDN/banka/shared/cicd/qodana-pipeline/qodana-trigger@1
    inputs:
      dind: "1"
  - component: $CI_SERVER_FQDN/banka/shared/cicd/pipelines/renovate@1
  #override default deploy
  - component: $CI_SERVER_FQDN/banka/shared/cicd/pipelines/argo-cd-deploy@1
    inputs:
      k8s_namespace_key: keycloak
      dependency:
        - docker_build_tag
        - helm_generate
  - component: $CI_SERVER_FQDN/banka/shared/cicd/pipelines/registry-access@1
    inputs:
      job_name_rw: helm_generate


stages:
  - build
  - scan
  - docker_build
  - charts
  - deploy_lab
  - renovate

helm_generate:
  stage: charts
  image:
    name: ${REGISTRY_HOST}/proxy-docker/alpine/helm:3.11.3
    entrypoint: ["/bin/sh", "-c"]
  before_script:
    - |
      if expr match "${CI_COMMIT_TAG}" '[0-9]\+\.[0-9]\+\.[0-9]\+\-[0-9]\+' ; then
        export APP_VERSION=$(echo ${CI_COMMIT_TAG} | awk -F'-' '{print $1}')
      else
        export APP_VERSION=${CI_COMMIT_TAG}
      fi
  script:
    - cd service/chart
    - sed -i "s|<<image>>|${REGISTRY_HOST}/${HARBOR_PROJECT}/${CI_PROJECT_NAME}|g" values*.yaml
    - sed -i "s|<<tag>>|${APP_VERSION}|g" values.yaml
    - sed -i "s|<<chart-name>>|${CI_PROJECT_NAME}|g" Chart.yaml
    - sed -i "s|<<version>>|${CI_COMMIT_TAG}|g" Chart.yaml
    - cd ..
    - helm package chart/ --version ${CI_COMMIT_TAG}
    - echo ${REGISTRY_PASSWORD}  | helm registry login ${REGISTRY_HOST} -u ${REGISTRY_USER} --password-stdin
    - helm push ${CI_PROJECT_NAME}-${CI_COMMIT_TAG}.tgz oci://${REGISTRY_HOST}/helm-templates
  rules:
    - if: $UHC == "false" && $CI_COMMIT_TAG
    - if: $UHC == "true" && $APP_V2 == "true" && $CI_COMMIT_TAG
