include("service")
rootProject.name = "keycloak-migration"

pluginManagement {
    val platformCommonVersion: String by settings

    val artifactoryUserProperty = settings.extra.takeIf { it.has("artifactory_user") }?.get("artifactory_user") as String?
    val artifactoryPasswordProperty = settings.extra.takeIf { it.has("artifactory_password") }?.get("artifactory_password") as String?
    val artifactoryUser = System.getenv("artifactory_user") ?: artifactoryUserProperty
    val artifactoryPassword = System.getenv("artifactory_password") ?: artifactoryPasswordProperty

    plugins {
        id("cz.pbktechnology.platform.common.gradle-plugin.root") version platformCommonVersion
        id("cz.pbktechnology.platform.common.gradle-plugin.module") version platformCommonVersion
    }

    repositories {
        maven {
            url = uri("https://artifactory.pbk-lab.tech/artifactory/gradle-plugins")
            credentials {
                username = artifactoryUser
                password = artifactoryPassword
            }
        }
        maven {
            url = uri("https://artifactory.pbk-lab.tech/artifactory/pbk")
            credentials {
                username = artifactoryUser
                password = artifactoryPassword
            }
        }
        mavenLocal()
    }
}
