# a separate file for configuration flows
# their definition can be quite verbose, so it was separated into its own file
# flows can be referenced from any realm in configuration.yaml; when a flow is referenced, it is created/updated in the realm during migration

authentication-flows:
  - name: PIDFederatedAuthentication
    description: Authentication flow for PID federated authentication
    executions:
      - alias: PFAFedAuth
        description: Federated authentication
        type: FED_AUTHENTICATOR
        config: fed-authenticator-config
        requirement: REQUIRED

  - name: PIDFederatedAuthenticationBankId
    description: Authentication flow for PID federated authentication - specific configuration for Bank iD
    executions:
      - alias: PFABIFedAuth
        description: Federated authentication
        type: FED_AUTHENTICATOR
        config: fed-authenticator-config-bankid
        requirement: REQUIRED

      - alias: PFABIDetermineLoa
        description: Determine LoA
        type: GENERIC_SUBFLOW
        requirement: REQUIRED
        executions:
          - alias: PFABIAcrLoa2
            description: ACR loa2
            type: GENERIC_SUBFLOW
            requirement: CONDITIONAL
            executions:
              - alias: PFABIConditionLoa2
                description: Condition - loa2
                type: CONDITION_LEVEL_OF_AUTHENTICATION
                # this is correct - LoA 1 is mapped to ACR loa2
                config: condition-loa1
                requirement: REQUIRED

              - alias: PFABIVerifyLoA2
                description: Verify auth methods for loa2
                type: VERIFY_AUTHENTICATION_METHODS
                config: verify-auth-methods-authenticator-loa2
                requirement: REQUIRED

          - alias: PFABIAcrLoa3
            description: ACR loa3
            type: GENERIC_SUBFLOW
            requirement: CONDITIONAL
            executions:
              - alias: PFABIConditionLoa3
                description: Condition - loa3
                type: CONDITION_LEVEL_OF_AUTHENTICATION
                # this is correct - LoA 2 is mapped to ACR loa3
                config: condition-loa2
                requirement: REQUIRED

              - alias: PFABIVerifyLoA3
                description: Verify auth methods for loa3
                type: VERIFY_AUTHENTICATION_METHODS
                config: verify-auth-methods-authenticator-loa3
                requirement: REQUIRED

  - name: PIDNiaAuthentication
    description: Authentication flow for NIA integration
    executions:
      - alias: PNAFedAuth
        description: Federated authentication for NIA
        type: NIA_AUTHENTICATOR
        config: nia-authenticator-config
        requirement: REQUIRED
