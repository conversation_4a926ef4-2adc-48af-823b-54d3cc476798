realms:
  - name: 'master'
    clients-openid:
      - name: 'master-keycloak-user-manager'
        description: |
          Keycloak user manager client. Only this client has permission to manage users in all realms.
        service-accounts-enabled: true
        assign-roles:
          - client-name: pid-realm
            role-name: manage-users
          - client-name: pid-realm
            role-name: view-clients
          - client-name: onboard-realm
            role-name: manage-users
          - client-name: onboard-realm
            role-name: view-clients
          - client-name: pfs-realm
            role-name: manage-users
          - client-name: pfs-realm
            role-name: view-clients
          - client-name: gepard-realm
            role-name: manage-users
          - client-name: gepard-realm
            role-name: view-clients
      - name: master-idp-apim-orchestrator
        description: |
          Client for idp-apim-orchestrator service. This service is used for dynamic client registration to Keycloak and WSO2 and therefore needs elevated permissions and roles.
        service-accounts-enabled: true
        assign-roles:
          - client-name: pid-realm
            role-name: manage-users
          - client-name: pid-realm
            role-name: create-client
          - client-name: pid-realm
            role-name: manage-clients
          - client-name: pid-realm
            role-name: manage-authorization
          - client-name: pid-realm
            role-name: view-realm # necessary to list existing authentication flows; not necessary for other realms, at least for now
          - client-name: onboard-realm
            role-name: manage-users
          - client-name: onboard-realm
            role-name: create-client
          - client-name: onboard-realm
            role-name: manage-clients
          - client-name: onboard-realm
            role-name: manage-authorization
          - client-name: pfs-realm
            role-name: manage-users
          - client-name: pfs-realm
            role-name: create-client
          - client-name: pfs-realm
            role-name: manage-clients
          - client-name: pfs-realm
            role-name: manage-authorization
          - client-name: services-realm
            role-name: manage-users
          - client-name: services-realm
            role-name: create-client
          - client-name: services-realm
            role-name: manage-clients
          - client-name: services-realm
            role-name: manage-authorization
  - name: 'gepard'
    client-scopes:
      - name: 'default'
        description: |
          Default scope needed by WSO2 APIM
        type: DEFAULT
      - name: 'gepard_attributes'
        description: |
          User attributes of type GEPARD advisor
        type: DEFAULT
        mappers:
          - name: 'userId'
            type: USER_ATTRIBUTES
            config:
              user-attribute-mapper:
                token-claim-name: 'user_attributes.userId'
                token-claim-type: STRING
    clients-openid:
      - name: 'wso2-apim-client-gepard'
        description: |
          Binding client for WSO2 APIM
        service-accounts-enabled: true
        direct-access-grants-enabled: false
        public-client: false
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
        assign-client-scopes:
          - name: default
            type: DEFAULT
      - name: 'world-aps'
        description: |
          Holder for APS roles
        public-client: true
        roles:
          - name: APS_ADVISOR
            description: User working in gepard advisor net
      - name: 'frontend-loan-tiles-gepard'
        description: |
          Frontend client for loan tiles
        public-client: true
        direct-access-grants-enabled: true
        standard-flow-enabled: true
        web-origins-required: true
        redirect-uris-required: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'rs-loan-application-gepard'
        client-id: 'rs-loan-application'
        description: |
          Resource server for loan application
        authorization-services-enabled: true
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: manage-authorization
  - name: 'onboard'
    client-scopes:
      - name: 'onboard_attributes'
        description: |
          User attributes of type ONBOARD user
        type: DEFAULT
        mappers:
          - name: 'pidUuid'
            type: USER_ATTRIBUTES
            config:
              user-attribute-mapper:
                token-claim-name: 'user_attributes.pidUuid'
                token-claim-type: STRING
          - name: 'phone'
            type: USER_ATTRIBUTES
            config:
              user-attribute-mapper:
                token-claim-name: 'user_attributes.phone'
                token-claim-type: JSON
          - name: 'activationUserUuid'
            type: USER_ATTRIBUTES
            config:
              user-attribute-mapper:
                token-claim-name: 'user_attributes.activationUserUuid'
                token-claim-type: STRING
      - name: 'default'
        description: |
          Default scope needed by WSO2 APIM
        type: DEFAULT
    clients-openid:
      - name: 'wso2-apim-client-onboard'
        description: |
          Binding client for WSO2 APIM
        service-accounts-enabled: true
        direct-access-grants-enabled: false
        public-client: false
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
        assign-client-scopes:
          - name: default
            type: DEFAULT
      - name: 'world-onboard'
        description: |
          Holder for onboard roles.
        public-client: true
        roles:
          - name: ONBOARD_USER
            description: User during PID boarding process
      - name: 'frontend-mobile-app'
        description: |
          Frontend client for mobile application
        direct-access-grants-enabled: true
        public-client: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
  - name: 'pfs'
    client-scopes:
      - name: 'pfs_attributes'
        description: |
          User attributes of type PFS advisor
        type: DEFAULT
        mappers:
          - name: 'userId'
            type: USER_ATTRIBUTES
            config:
              user-attribute-mapper:
                token-claim-name: 'user_attributes.userId'
                token-claim-type: STRING
          - name: 'advisorId'
            type: USER_ATTRIBUTES
            config:
              user-attribute-mapper:
                token-claim-name: 'user_attributes.advisorId'
                token-claim-type: STRING
      - name: 'default'
        description: |
          Default scope needed by WSO2 APIM
        type: DEFAULT
    clients-openid:
      - name: 'wso2-apim-client-pfs'
        description: |
          Binding client for WSO2 APIM
        service-accounts-enabled: true
        direct-access-grants-enabled: false
        public-client: false
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
        assign-client-scopes:
          - name: default
            type: DEFAULT
      - name: 'frontend-aps'
        description: |
          Frontend client for APS application
        direct-access-grants-enabled: true
        web-origins-required: true
        public-client: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'frontend-bank-onboarding'
        description: |
          Frontend client for bank onboarding
        direct-access-grants-enabled: true
        web-origins-required: true
        public-client: true
      - name: 'rs-aps'
        description: |
          Resource server for APS
        authorization-services-enabled: true
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: manage-authorization
      - name: 'frontend-boa-pfs'
        description: |
          Frontend client for Back Office Application
        public-client: true
        direct-access-grants-enabled: true
        web-origins-required: true
      - name: 'world-aps'
        description: |
          Holder for APS roles
        public-client: true
        roles:
          - name: APS_ADVISOR
            description: User working in external advisor net
      - name: 'world-pfs'
        description: |
          Holder for PFS roles
        public-client: true
        roles:
          # Deprecated - backward compatibility roles, will be removed
          - name: PFS_ADVISOR
            description: Employee of PFS, working in advisor net
            associated-roles:
              - role-name: RQ_SEARCH_PFS_ADVISOR_CLIENTS
          - name: PFS_CALL_CENTRUM
            description: Employee of PFS, working in central of "customer service" department
            associated-roles:
              - role-name: RQ_APPLICATION_VIEW
              - role-name: RQ_CLIENT_VERIFY_IDENTITY
              - role-name: RQ_CLIENT_VIEW
              - role-name: RQ_EVENTS_VIEW
              - role-name: RQ_SEARCH_PFS_CLIENTS
              - role-name: RQ_SIGN_REQUESTS_VIEW
              - role-name: RQ_APPLICATION_BLOCK
              - role-name: RQ_APPLICATION_UNBLOCK
              - role-name: RQ_APPLICATION_REMOVE
          - name: PFS_CALL_CENTRUM_CERTIFIED
            description: Employee of PFS, working in central of "customer service" department & has bank certification
            associated-roles:
              - role-name: DATA_BANK_ACCOUNTS_DETAIL_ADVISOR_GROUP
              - role-name: RQ_APPLICATION_VIEW
              - role-name: RQ_BANK_ACCOUNTS_VIEW
              - role-name: RQ_BANK_BUNDLE_VIEW
              - role-name: RQ_BANK_CARDS_VIEW
              - role-name: RQ_BANK_DOCUMENTS_VIEW
              - role-name: RQ_BANK_NEW_PRODUCT_REQUESTS_VIEW
              - role-name: RQ_BANK_SIGN_REQUESTS_VIEW
              - role-name: RQ_CLIENT_VERIFY_IDENTITY
              - role-name: RQ_CLIENT_VIEW
              - role-name: RQ_EVENTS_VIEW
              - role-name: RQ_SEARCH_PFS_CLIENTS
              - role-name: RQ_SIGN_REQUESTS_VIEW
              - role-name: RQ_APPLICATION_BLOCK
              - role-name: RQ_APPLICATION_UNBLOCK
              - role-name: RQ_APPLICATION_REMOVE
              - role-name: RQ_BANK_MOBILITY_VIEW
          - name: PFS_OPERATIONS
            description: Employee of PFS, working in central of "operations" department
            associated-roles:
              - role-name: RQ_SEARCH_PFS_CLIENTS
              - role-name: RQ_CLIENT_VIEW
          - name: DATA_BANK_ACCOUNTS_DETAIL_ADVISOR_GROUP
            description: Detail účtu - upravený set dat
          - name: DATA_BANK_ACCOUNTS_DETAIL_ADVISOR_GROUP_LIMITED
            description: Detail účtu - omezený set dat
          - name: RQ_APPLICATION_BLOCK
            description: Blokace registrovaných zařízení
          - name: RQ_APPLICATION_UNBLOCK
            description: Odblokování registrovaných zařízení
          - name: RQ_APPLICATION_REMOVE
            description: Odstranění registrovaných zařízení
          - name: RQ_BANK_CARDS_FREEZE
            description: Dočasné zablokování karty
          - name: RQ_BANK_CARDS_UNFREEZE
            description: Dočasné odblokování karty
          - name: RQ_APPLICATION_VIEW
            description: Zobrazení Aplikace a registrovaných zařízení
          - name: RQ_BANK_ACCOUNTS_VIEW
            description: Zobrazení účtu
          - name: RQ_BANK_BUNDLE_VIEW
            description: Zobrazení balíčku
          - name: RQ_BANK_CARDS_VIEW
            description: Zobrazení karet
          - name: RQ_BANK_DOCUMENTS_VIEW
            description: Zobrazení dokumentů
          - name: RQ_BANK_NEW_PRODUCT_REQUESTS_VIEW
            description: Zobrazení žádostí o nové produkty
          - name: RQ_BANK_PSD2_VIEW
            description: Zobrazení připojených bank (PSD2)
          - name: RQ_BANK_SIGN_REQUESTS_VIEW
            description: Zobrazení bankovních požadavků
          - name: RQ_CLIENT_VERIFY_IDENTITY
            description: Ověření totožnosti klienta přes mobilní aplikaci
          - name: RQ_CLIENT_VIEW
            description: Zobrazení detailu klienta
          - name: RQ_EVENTS_VIEW
            description: Zobrazení událostí
          - name: RQ_SEARCH_PFS_ADVISOR_CLIENTS
            description: Vyhledávání PFS klientů pro vybraného poradce
          - name: RQ_SEARCH_PFS_CLIENTS
            description: Vyhledávání PFS klientů
          - name: RQ_SIGN_REQUESTS_VIEW
            description: Zobrazení požadavků
          - name: PFS_ADVISOR
            description: |
              Employee of PFS, working in advisor net
          # PFS_CALL_CENTRE will be replaced by PFS_CALL_CENTRUM later
          - name: PFS_CALL_CENTRE
            description: |
              Employee of PFS, working in central of "customer service" department
          - name: PFS_CALL_CENTRUM
            description: |
              Employee of PFS, working in central of "customer service" department
          - name: PFS_CALL_CENTRUM_CERTIFIED
            description: |
              Employee of PFS, working in central of "customer service" department & has bank certification
          - name: PFS_OPERATIONS
            description: |
              Employee of PFS, working in central of "operations" department
          - name: BANK_CERTIFICATION
            description: |
              Has bank certification
          - name: LOAN_CERTIFICATION
            description: |
              Has loan certification
          - name: RQ_BANK_MOBILITY_CREATE
            description: Role for Mobility creation
          - name: RQ_BANK_MOBILITY_VIEW
            description: Role for display Mobility
          - name: PFS_LOAN_FRIENDS_AND_FAMILY
            description: Role for friends and family
      - name: 'frontend-loan-application-pfs'
        description: |
          Frontend client for loan application
        direct-access-grants-enabled: true
        web-origins-required: true
        public-client: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'frontend-loan-tiles'
        description: |
          Frontend client for loan tiles
        public-client: true
        direct-access-grants-enabled: true
        standard-flow-enabled: true
        web-origins-required: true
        redirect-uris-required: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'rs-loan-application-pfs'
        client-id: 'rs-loan-application'
        description: |
          Resource server for loan application
        authorization-services-enabled: true
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: manage-authorization
      - name: 'rs-boa-pfs'
        client-id: 'rs-boa'
        description: |
          Resource server for backoffice
        authorization-services-enabled: true
        assign-roles:
          - client-name: world-pfs
            role-name: PFS_CALL_CENTRUM
          - client-name: world-pfs
            role-name: PFS_ADVISOR
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: manage-authorization
  - name: 'pid'
    # Unmanaged attribute policy controls how user attributes are handled
    # DISABLED: Unmanaged attributes are not allowed
    # ENABLED: Allows unmanaged attributes to be stored and displayed (default)
    # ADMIN_VIEW: Unmanaged attributes are visible only to admins
    # ADMIN_EDIT: Unmanaged attributes can only be edited by admins
    unmanaged-attribute-policy: ENABLED
    authentication-flows:
      - PIDFederatedAuthentication
      - PIDFederatedAuthenticationBankId
      - PIDNiaAuthentication
    client-scopes:
      - name: 'pid_attributes'
        description: |
          User attributes of type PID
        type: DEFAULT
        mappers:
          - name: 'pidUuid'
            type: USER_ATTRIBUTES
            config:
              user-attribute-mapper:
                token-claim-name: 'user_attributes.pidUuid'
                token-claim-type: STRING
      - name: 'nia_attributes'
        description: |
          Attributes required by NIA
        type: NONE
        protocol: SAML
        display-on-consent-screen: false
        include-in-token-scope: false
        mappers:
          - name: Custom Subject SAML Mapper
            type: CUSTOM_SUBJECT_SAML_MAPPER
          - name: BSI mapper
            type: SAML_USER_SESSION_NOTE_MAPPER
            config:
              saml-user-session-note-mapper:
                note-name: customSubjectValue
                attribute-name: 'http://eidas.europa.eu/attributes/naturalperson/PersonIdentifier'
                attribute-name-format: URI_REFERENCE
          - name: Verification instrument mapper
            type: SAML_USER_SESSION_NOTE_MAPPER
            config:
              saml-user-session-note-mapper:
                note-name: niaVerificationInstrument
                attribute-name: 'http://schemas.microsoft.com/cgg/2010/identity/claims/objectidentifier'
                attribute-name-format: URI_REFERENCE
      - name: 'default'
        description: |
          Default scope needed by WSO2 APIM
        type: DEFAULT
      - name: 'CISP'
        description: PSD2 TPP CISP scope
        type: NONE
      - name: 'AISP'
        description: PSD2 TPP AISP scope
        type: NONE
      - name: 'PISP'
        description: PSD2 TPP PISP scope
        type: NONE
      - name: 'openid'
        description: Bank iD openid scope
        type: NONE
      - name: 'profile.addresses'
        description: Bank iD profile.addresses scope
        type: NONE
      - name: 'profile.birthdate'
        description: Bank iD profile.birthdate scope
        type: NONE
      - name: 'profile.birthnumber'
        description: Bank iD profile.birthnumber scope
        type: NONE
      - name: 'profile.birthplaceNationality'
        description: Bank iD profile.birthplaceNationality scope
        type: NONE
      - name: 'profile.email'
        description: Bank iD profile.email scope
        type: NONE
      - name: 'profile.gender'
        description: Bank iD profile.gender scope
        type: NONE
      - name: 'profile.idcards'
        description: Bank iD profile.idcards scope
        type: NONE
      - name: 'profile.legalstatus'
        description: Bank iD profile.legalstatus scope
        type: NONE
      - name: 'profile.locale'
        description: Bank iD profile.locale scope
        type: NONE
      - name: 'profile.maritalstatus'
        description: Bank iD profile.maritalstatus scope
        type: NONE
      - name: 'profile.name'
        description: Bank iD profile.name scope
        type: NONE
      - name: 'profile.paymentAccounts'
        description: Bank iD profile.paymentAccounts scope
        type: NONE
      - name: 'profile.phonenumber'
        description: Bank iD profile.phonenumber scope
        type: NONE
      - name: 'profile.titles'
        description: Bank iD profile.titles scope
        type: NONE
      - name: 'profile.updatedat'
        description: Bank iD profile.updatedat scope
        type: NONE
      - name: 'profile.zoneinfo'
        description: Bank iD profile.zoneinfo scope
        type: NONE
      - name: 'profile.verification'
        description: Bank iD profile.verification scope
        type: NONE
      - name: 'notification.claims_updated'
        description: Bank iD notification.claims_updated scope
        type: NONE
      - name: 'sign.qualified'
        description: Bank iD sign.qualified scope
        type: NONE
      - name: 'sign.officially_certified'
        description: Bank iD sign.officially_certified scope
        type: NONE
      - name: 'bankid_sub'
        description: Scope for Bank iD clients containing mappers for manipulating sub claim
        type: NONE
        display-on-consent-screen: false
        include-in-token-scope: false
        mappers:
          - name: pidUuid sub mapper
            type: USER_ATTRIBUTES
            config:
              user-attribute-mapper:
                token-claim-name: sub
                token-claim-type: STRING
                user-attribute-name: pidUuid
          - name: nonce workaround mapper
            type: NONCE_ID_TOKEN
          - name: amr mapper
            type: USER_SESSION_NOTE
            config:
              user-session-note-mapper:
                token-claim-name: amr
                token-claim-type: JSON
                note-name: authentication_methods
          - name: structured scope mapper
            type: USER_SESSION_NOTE
            config:
              user-session-note-mapper:
                token-claim-name: structured_scope
                token-claim-type: JSON
                note-name: structured_scope
    clients-openid:
      - name: 'wso2-apim-client-pid'
        description: |
          Binding client for WSO2 APIM
        service-accounts-enabled: true
        direct-access-grants-enabled: false
        public-client: false
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
        assign-client-scopes:
          - name: default
            type: DEFAULT
      - name: 'world-pid'
        description: |
          Holder for PID user roles
        public-client: true
        roles:
          - name: PID_ACTIVATED
            description: User of PID
      - name: 'world-bank'
        description: |
          Holder for BANK user roles
        public-client: true
        roles:
          - name: BANK_USER
            description: Adult user of Bank
          - name: BANK_CHILD_USER
            description: Child user of Bank
      - name: 'world-idm-cadcalc'
        description: |
          Holder for CADCalc user roles
        public-client: true
        roles:
          - name: PID-CADCALC-TREASURY
            description: CADCalc user role for use cases of treasury
          - name: PID-CADCALC-BACKOFFICE
            description: CADCalc user role for use cases of backoffice
          - name: PID-CADCALC-UCTO
            description: CADCalc user role for use cases of ucto
          - name: PID-CADCALC-RISK
            description: CADCalc user role for use cases of risk
          - name: PID-CADCALC-AUDITOR
            description: CADCalc user role for use cases of auditor
          - name: PID-CADCALC-ADMIN
            description: CADCalc user role for use cases of admin
      - name: 'frontend-treasury'
        description: |
          Frontend client for treasury domain (CADCalc)
        direct-access-grants-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        public-client: false
      - name: 'frontend-aps'
        description: |
          Frontend client for APS application
        direct-access-grants-enabled: true
        public-client: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'service-paf-case-investigation-pid'
        description: |
          Servisni klient v PID realm pro PAF
        service-accounts-enabled: true
        assign-roles:
          - client-name: component-aml-els
            role-name: AML_ELS_ROLE_APP
      - name: 'frontend-aml-plugin'
        description: |
          Chrome Plugin pro AML Case Management
        redirect-uris-required: true
        public-client: true
        standard-flow-enabled: true
        web-origins-required: true
        roles:
          - name: AML_SERVICES_ROLE_USER
            description: 'Role for AML services user'
      - name: 'frontend-paf-case-investigation'
        description: |
          Frontend pro partners antifraud case investigation
        redirect-uris-required: true
        redirect-logout-uris-required: true
        standard-flow-enabled: true
        root-url-required: true
        public-client: true
        web-origins-required: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
        roles:
          - name: PAF-CASE-INVESTIGATION-RISK-OFFICER
            description: for risk officers
          - name: PAF-CASE-INVESTIGATION-LOAN-BACKOFFICE
            description: for loan backoffice
      - name: 'frontend-knbox'
        description: |
          Component client for KNBOX
        redirect-uris-required: true
        redirect-logout-uris-required: true
        standard-flow-enabled: true
        root-url-required: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'world-knbox'
        description: |
          Holder fro KNBOX user roles
        public-client: true
        roles:
          - name: PID-KNBOX-BACK-OFFICE
            description: KNBOX - ROLE 1 - BO
          - name: PID-KNBOX-COLLATERAL
            description: KNBOX - ROLE 2 - Collateral
          - name: PID-KNBOX-UNDERWRITER-COLLATERAL
            description: KNBOX - ROLE 3 - UNDW, Coll
          - name: PID-KNBOX-CREDIT-DEPARTMENT
            description: KNBOX - ROLE 4 - Úverové oddelení
          - name: PID-KNBOX-SERVICE-ADVANCED
            description: KNBOX - ServiceAdvanced
          - name: PID-KNBOX-SERVICE-BASIC
            description: KNBOX - ServiceBasic
          - name: PID-KNBOX-SYSTEM-USER
            description: KNBOX - SystemUser
      - name: 'rs-aps'
        description: |
          Resource server for APS
        authorization-services-enabled: true
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: manage-authorization
      - name: 'frontend-mobile-app'
        description: |
          Frontend client for mobile application
        direct-access-grants-enabled: true
        public-client: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'frontend-boa'
        description: |
          Frontend client for Back Office Application
        direct-access-grants-enabled: true
        public-client: true
        web-origins-required: true
        redirect-uris-required: true
        standard-flow-enabled: true
      - name: 'world-bank-aps'
        description: |
          Holder for APS user roles
        public-client: true
      - name: 'world-idm-loan'
        description: |
          Holder for user roles of loan systems
        public-client: true
        roles:
          - name: PID-LOAN-VERIFIER
            description: Role for verifier of the Loan
          - name: PID-LOAN-UNDERWRITER
            description: Role for underwriter of the Loan
          - name: PID-LOAN-BACK-OFFICE
            description: Role for back office of the Loan
          - name: PID-LOAN-SUPPORT
            description: Role for technical support of the Loan
          - name: PID-LOAN-CALL-CENTRE
            description: Role for call centre of the Loan
          - name: PID-LOAN-COLLATERAL
            description: Role for collateral of the Loan
          - name: PID-LOAN-SALES-SUPPORT
            description: Role for sales support of the Loan
          - name: PID-LOAN-AUDIT
            description: Role for audit of the Loan
      - name: 'world-idm-boa'
        description: |
          Holder for Bank Back Office user roles
        public-client: true
        roles:
          # Temporary solution for BOA using RQ roles. Will be replaced by permissions in the future
          - name: PID-BOA-ADMIN
            description: Role for Admin user
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-BANK-BUNDLE-MARKETING-CAMPAIGN-ALL
              - role-name: PID-BOA-DATA-BANK-MARKETING-CAMPAIGN-REDEEMED-CLIENT-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-ACTIVATIONS-MANAGE
              - role-name: PID-BOA-RQ-APPLICATION-ACTIVATIONS-VIEW
              - role-name: PID-BOA-RQ-APPLICATION-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-REMOVE
              - role-name: PID-BOA-RQ-APPLICATION-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-BLOCK
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-CHANGE-LIMIT
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-MARK-ACCOUNT-AS-BASIC
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-TERMINATE
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-MANAGE-MARKETING-CAMPAIGN
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-ACTIVATE
              - role-name: PID-BOA-RQ-BANK-CARDS-CANCEL-HOLD
              - role-name: PID-BOA-RQ-BANK-CARDS-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-CHANGE-LIMIT
              - role-name: PID-BOA-RQ-BANK-CARDS-CHANGE-SECURITY-FEATURES
              - role-name: PID-BOA-RQ-BANK-CARDS-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-ACTIVATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-UNBLOCK-PIN
              - role-name: PID-BOA-RQ-BANK-CARDS-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-GENERATE
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-SET-VISIBILITY
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-UPLOAD
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-GENERATE-SUMMARY-REPORT-FOR-STATE-AUTHORITIES
              - role-name: PID-BOA-RQ-BANK-IDENTITY-BLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-UNBLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-CREATE
              - role-name: PID-BOA-RQ-BANK-MOBILITY-MANAGE
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-RESTART
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-REMOVE
              - role-name: PID-BOA-RQ-BANK-PSD2-RELOAD
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-CHANGE-ADDRESS
              - role-name: PID-BOA-RQ-CLIENT-CHANGE-EMAIL
              - role-name: PID-BOA-RQ-CLIENT-CHANGE-NATIONAL-NUMBER
              - role-name: PID-BOA-RQ-CLIENT-CHANGE-PHONE
              - role-name: PID-BOA-RQ-CLIENT-CREATE-PFS-ANALYSIS
              - role-name: PID-BOA-RQ-CLIENT-IDENTIFY-CLIENT-DATA-IN-AIS
              - role-name: PID-BOA-RQ-CLIENT-SEND-BANK-DOCUMENTATION-TO-PFS
              - role-name: PID-BOA-RQ-CLIENT-VERIFY-IDENTITY
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-PRODUCTS-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-TRIGGER-UPDATE
              - role-name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-MANAGE
              - role-name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-REFRESH-ADVISOR-ROLES
              - role-name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SERVICE-BANK-CARDS-CLICK-TO-PAY-VIEW
              - role-name: PID-BOA-RQ-SERVICE-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-SERVICE-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-SERVICE-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SERVICE-PID-ONBOARDING-VIEW
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-ACCOUNTS
            description: Role for Account management department
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-DATA-BANK-BUNDLE-MARKETING-CAMPAIGN-ALL
              - role-name: PID-BOA-RQ-APPLICATION-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-REMOVE
              - role-name: PID-BOA-RQ-APPLICATION-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-CHANGE-LIMIT
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-MARK-ACCOUNT-AS-BASIC
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-TERMINATE
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-MANAGE-MARKETING-CAMPAIGN
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-CANCEL-HOLD
              - role-name: PID-BOA-RQ-BANK-CARDS-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-GENERATE
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-SET-VISIBILITY
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-UPLOAD
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-GENERATE-SUMMARY-REPORT-FOR-STATE-AUTHORITIES
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-CREATE
              - role-name: PID-BOA-RQ-BANK-MOBILITY-MANAGE
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-RESTART
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SERVICE-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-KLIPR-SUPPORT
            description: Role for KLIPR support group
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-BANK-BUNDLE-MARKETING-CAMPAIGN-ALL
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-MANAGE-MARKETING-CAMPAIGN
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-SET-VISIBILITY
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-UPLOAD
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-RESTART
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-SEND-BANK-DOCUMENTATION-TO-PFS
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-CALL-CENTRUM
            description: Role for Call centrum department
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-DATA-BANK-BUNDLE-MARKETING-CAMPAIGN-ALL
              - role-name: PID-BOA-RQ-APPLICATION-ACTIVATIONS-MANAGE
              - role-name: PID-BOA-RQ-APPLICATION-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-REMOVE
              - role-name: PID-BOA-RQ-APPLICATION-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-BLOCK
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-CHANGE-LIMIT
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-MARK-ACCOUNT-AS-BASIC
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-MANAGE-MARKETING-CAMPAIGN
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-ACTIVATE
              - role-name: PID-BOA-RQ-BANK-CARDS-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-CHANGE-LIMIT
              - role-name: PID-BOA-RQ-BANK-CARDS-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-ACTIVATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-UNBLOCK-PIN
              - role-name: PID-BOA-RQ-BANK-CARDS-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-GENERATE
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-BLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-UNBLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-CREATE
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-CREATE-PFS-ANALYSIS
              - role-name: PID-BOA-RQ-CLIENT-SEND-BANK-DOCUMENTATION-TO-PFS
              - role-name: PID-BOA-RQ-CLIENT-VERIFY-IDENTITY
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SERVICE-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-SERVICE-PID-ONBOARDING-VIEW
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-CALL-CENTRUM-LIMITED
            description: Role for Call centrum department with limited rights
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-DATA-BANK-BUNDLE-MARKETING-CAMPAIGN-ALL
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-MANAGE-MARKETING-CAMPAIGN
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VERIFY-IDENTITY
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SERVICE-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-SERVICE-PID-ONBOARDING-VIEW
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-CALL-CENTRUM-SENIOR
            description: Role for Call centrum department and senior users
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-DATA-BANK-BUNDLE-MARKETING-CAMPAIGN-ALL
              - role-name: PID-BOA-RQ-APPLICATION-ACTIVATIONS-MANAGE
              - role-name: PID-BOA-RQ-APPLICATION-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-REMOVE
              - role-name: PID-BOA-RQ-APPLICATION-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-BLOCK
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-CHANGE-LIMIT
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-MARK-ACCOUNT-AS-BASIC
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-MANAGE-MARKETING-CAMPAIGN
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-ACTIVATE
              - role-name: PID-BOA-RQ-BANK-CARDS-CANCEL-HOLD
              - role-name: PID-BOA-RQ-BANK-CARDS-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-CHANGE-LIMIT
              - role-name: PID-BOA-RQ-BANK-CARDS-CHANGE-SECURITY-FEATURES
              - role-name: PID-BOA-RQ-BANK-CARDS-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-ACTIVATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-UNBLOCK-PIN
              - role-name: PID-BOA-RQ-BANK-CARDS-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-GENERATE
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-BLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-UNBLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-CREATE
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-CREATE-PFS-ANALYSIS
              - role-name: PID-BOA-RQ-CLIENT-IDENTIFY-CLIENT-DATA-IN-AIS
              - role-name: PID-BOA-RQ-CLIENT-SEND-BANK-DOCUMENTATION-TO-PFS
              - role-name: PID-BOA-RQ-CLIENT-VERIFY-IDENTITY
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-PRODUCTS-VIEW
              - role-name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SERVICE-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-SERVICE-PID-ONBOARDING-VIEW
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-CARDS
            description: Role for Card management department
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-ACTIVATE
              - role-name: PID-BOA-RQ-BANK-CARDS-CANCEL-HOLD
              - role-name: PID-BOA-RQ-BANK-CARDS-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-CHANGE-LIMIT
              - role-name: PID-BOA-RQ-BANK-CARDS-CHANGE-SECURITY-FEATURES
              - role-name: PID-BOA-RQ-BANK-CARDS-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-ACTIVATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-TERMINATE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-UNBLOCK-PIN
              - role-name: PID-BOA-RQ-BANK-CARDS-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-GENERATE
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-BANK-CLIENTS
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-CRM-AND-SALES
            description: Role for CRM and Sales department
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SERVICE-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-DIGITAL
            description: Role for Digital channels user
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-PRODUCTS-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-TRIGGER-UPDATE
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-DWH
            description: Role for Bank DWH department
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-SEARCH-BANK-CLIENTS
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-PAYMENTS-SUPPORT
            description: Role for Payments support team
            associated-roles:
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-REMOVE
              - role-name: PID-BOA-RQ-BANK-PSD2-RELOAD
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-SEARCH-BANK-CLIENTS
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-PRODUCT-MANAGEMENT
            description: Role for Product management users
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-BANK-CLIENTS
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-LOANS-LIMITED
            description: Role for Loan backoffice department
            associated-roles:
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-BANK-SUPPORT
            description: Role for Support team users
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-APPLICATION-ACTIVATIONS-MANAGE
              - role-name: PID-BOA-RQ-APPLICATION-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-REMOVE
              - role-name: PID-BOA-RQ-APPLICATION-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-BLOCK
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-BLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-UNBLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-RESTART
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-REMOVE
              - role-name: PID-BOA-RQ-BANK-PSD2-RELOAD
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-CHANGE-ADDRESS
              - role-name: PID-BOA-RQ-CLIENT-CHANGE-EMAIL
              - role-name: PID-BOA-RQ-CLIENT-CHANGE-NATIONAL-NUMBER
              - role-name: PID-BOA-RQ-CLIENT-CHANGE-PHONE
              - role-name: PID-BOA-RQ-CLIENT-CREATE-PFS-ANALYSIS
              - role-name: PID-BOA-RQ-CLIENT-IDENTIFY-CLIENT-DATA-IN-AIS
              - role-name: PID-BOA-RQ-CLIENT-SEND-BANK-DOCUMENTATION-TO-PFS
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-PRODUCTS-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-TRIGGER-UPDATE
              - role-name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-REFRESH-ADVISOR-ROLES
              - role-name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SERVICE-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-SERVICE-PID-ONBOARDING-VIEW
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-COMPLIANCE
            description: Role for Compliance department
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-SEARCH-BANK-CLIENTS
              - role-name: PID-BOA-RQ-SERVICE-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-LEGAL
            description: Role for Legal department
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-BANK-CLIENTS
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-PID-SUPERVISOR
            description: Role for PID Supervisor user
            associated-roles:
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-CLIENT-IDENTIFY-CLIENT-DATA-IN-AIS
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
              - role-name: PID-BOA-RQ-SERVICE-PID-ONBOARDING-VIEW
          - name: PID-BOA-RISK-AML
            description: Role for ALM officer user
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-BLOCK
              - role-name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-UNBLOCK
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-BLOCK
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-TERMINATE
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-FREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-UNFREEZE
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-GENERATE
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-BLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-UNBLOCK
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VERIFY-IDENTITY
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-RISK-COLLECTIONS
            description: Role for Collections department
            associated-roles:
              - role-name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
              - role-name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
              - role-name: PID-BOA-RQ-APPLICATION-VIEW
              - role-name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
              - role-name: PID-BOA-RQ-BANK-BUNDLE-VIEW
              - role-name: PID-BOA-RQ-BANK-CARDS-VIEW
              - role-name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
              - role-name: PID-BOA-RQ-BANK-IDENTITY-VIEW
              - role-name: PID-BOA-RQ-BANK-LOANS-VIEW
              - role-name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
              - role-name: PID-BOA-RQ-BANK-MOBILITY-VIEW
              - role-name: PID-BOA-RQ-BANK-PSD2-VIEW
              - role-name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
              - role-name: PID-BOA-RQ-CLIENT-VERIFY-IDENTITY
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-EVENTS-VIEW
              - role-name: PID-BOA-RQ-SEARCH-BANK-CLIENTS
              - role-name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-SIMPLEA
            description: Role for Simplea company
            associated-roles:
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-SEARCH-SIMPLEA-CLIENTS
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          - name: PID-BOA-NEBANKA-SUPPORT
            description: Role for Nebanka support team
            associated-roles:
              - role-name: PID-BOA-RQ-CLIENT-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-PRODUCTS-VIEW
              - role-name: PID-BOA-RQ-NEBANKA-TRIGGER-UPDATE
              - role-name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
              - role-name: PID-BOA-RQ-USER-DATA-VIEW
          # Permissions
          - name: PID-BOA-RQ-BANK-ACCOUNTS-MARK-ACCOUNT-AS-BASIC
            description: Označení účtu jako “základního”
          - name: PID-BOA-RQ-BANK-BUNDLE-MANAGE-MARKETING-CAMPAIGN
            description: Zadání marketingové akce
          - name: PID-BOA-DATA-BANK-ACCOUNTS-DETAIL
            description: Detail účtu - kompletní set dat
          - name: PID-BOA-DATA-BANK-BUNDLE-MARKETING-CAMPAIGN-ADVISOR
            description: Data - marketingové akce pro poradce
          - name: PID-BOA-DATA-BANK-BUNDLE-MARKETING-CAMPAIGN-ALL
            description: Data - všechny marketignové akce
          - name: PID-BOA-DATA-BANK-MARKETING-CAMPAIGN-REDEEMED-CLIENT-DETAIL
            description: Data - detail klientů u marketingové akce
          - name: PID-BOA-DATA-CLIENT-ALL-DATA-ACCESS
            description: Zobrazit všechny data o klientovi
          - name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-BLOCK
            description: Role for blocking new activations
          - name: PID-BOA-RQ-APPLICATION-NEW-ACTIVATIONS-UNBLOCK
            description: Role for unblocking new activations
          - name: PID-BOA-RQ-APPLICATION-ACTIVATIONS-MANAGE
            description: Dokončení aktivací klienta
          - name: PID-BOA-RQ-APPLICATION-ACTIVATIONS-VIEW
            description: Výpis aktivací klienta
          - name: PID-BOA-RQ-APPLICATION-BLOCK
            description: Blokování registrovaných zařízení
          - name: PID-BOA-RQ-APPLICATION-UNBLOCK
            description: Odblokování registrovaných zařízení
          - name: PID-BOA-RQ-APPLICATION-REMOVE
            description: Odebrání registrovaných zařízení
          - name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-RESTART
            description: Restart zpracování onboardingové žádosti
          - name: PID-BOA-RQ-APPLICATION-VIEW
            description: Zobrazit informace o registrovaných zařízeních
          - name: PID-BOA-RQ-BANK-BUNDLE-VIEW
            description: Zobrazení balíčku
          - name: PID-BOA-RQ-BANK-ACCOUNTS-BLOCK
            description: Blokace účtu
          - name: PID-BOA-RQ-BANK-ACCOUNTS-CHANGE-LIMIT
            description: Změna limitu na účtu
          - name: PID-BOA-RQ-BANK-ACCOUNTS-TERMINATE
            description: Zrušení účtu
          - name: PID-BOA-RQ-BANK-ACCOUNTS-VIEW
            description: Zobrazení účtu
          - name: PID-BOA-RQ-BANK-CARDS-ACTIVATE
            description: Aktivace karty
          - name: PID-BOA-RQ-BANK-CARDS-UNBLOCK-PIN
            description: Odblokování PINu u karty
          - name: PID-BOA-RQ-BANK-CARDS-VIEW
            description: Zobrazení karet
          - name: PID-BOA-RQ-BANK-CARDS-TOKEN-VIEW
            description: Zobrazení karetních tokenů ke kartě
          - name: PID-BOA-RQ-BANK-CARDS-TERMINATE
            description: Zrušení karty
          - name: PID-BOA-RQ-BANK-CARDS-CHANGE-SECURITY-FEATURES
            description: Změna nastavení bezpečnostních prvků na kartě
          - name: PID-BOA-RQ-BANK-CARDS-FREEZE
            description: Dočasné zablokování karty
          - name: PID-BOA-RQ-BANK-CARDS-UNFREEZE
            description: Dočasné odblokování karty
          - name: PID-BOA-RQ-BANK-CARDS-TOKEN-ACTIVATE
            description: Aktivace tokenu ke kartě
          - name: PID-BOA-RQ-BANK-CARDS-TOKEN-FREEZE
            description: Dočasné zablokování tokenu ke kartě
          - name: PID-BOA-RQ-BANK-CARDS-TOKEN-UNFREEZE
            description: Odblokování tokenu ke kartě
          - name: PID-BOA-RQ-BANK-CARDS-TOKEN-TERMINATE
            description: Zrušení tokenu ke kartě
          - name: PID-BOA-RQ-BANK-CARDS-CHANGE-LIMIT
            description: Změna limitu na kartě
          - name: PID-BOA-RQ-BANK-CARDS-CANCEL-HOLD
            description: Zrušení karetního holdu
          - name: PID-BOA-RQ-BANK-TRANSACTIONS-VIEW
            description: Zobrazení transakcí
          - name: PID-BOA-RQ-BANK-MARKETING-CAMPAIGNS-VIEW
            description: Zobrazení marketingových akcí
          - name: PID-BOA-RQ-BANK-NEW-PRODUCT-REQUESTS-VIEW
            description: Zobrazení žádostí klienta o nové produkty
          - name: PID-BOA-RQ-BANK-SIGN-REQUESTS-VIEW
            description: Zobrazení bankovních požadavků
          - name: PID-BOA-RQ-BANK-ONBOARDING-APPLICATIONS-VIEW
            description: Zobrazení onboarding žádostí
          - name: PID-BOA-RQ-BANK-PSD2-REMOVE
            description: Odpojení připojených bank (PSD2)
          - name: PID-BOA-RQ-BANK-PSD2-RELOAD
            description: Reload připojených bank (PSD2)
          - name: PID-BOA-RQ-BANK-PSD2-VIEW
            description: Zobrazení připojených bank (PSD2)
          - name: PID-BOA-RQ-BANK-DOCUMENTS-VIEW
            description: Zobrazení dokumentů
          - name: PID-BOA-RQ-BANK-DOCUMENTS-GENERATE
            description: Generování dokumentu
          - name: PID-BOA-RQ-BANK-DOCUMENTS-UPLOAD
            description: Nahrání dokumentu
          - name: PID-BOA-RQ-BANK-DOCUMENTS-SET-VISIBILITY
            description: Zneplatnění dokumentu
          - name: PID-BOA-RQ-BANK-GENERATE-SUMMARY-REPORT-FOR-STATE-AUTHORITIES
            description: Generování souhrnného hlášení pro státní instituce
          - name: PID-BOA-RQ-BANK-MOBILITY-MANAGE
            description: Správa mobility
          - name: PID-BOA-RQ-BANK-MOBILITY-VIEW
            description: Zobrazení mobility
          - name: PID-BOA-RQ-BANK-LOANS-VIEW
            description: Zobrazení úvěrů
          - name: PID-BOA-RQ-NEBANKA-PRODUCTS-VIEW
            description: Zobrazení nebankovních produktů
          - name: PID-BOA-RQ-NEBANKA-TRIGGER-UPDATE
            description: Spuštění aktualizace dat nebankovních produktů
          - name: PID-BOA-RQ-CLIENT-CHANGE-ADDRESS
            description: Změnit adresu
          - name: PID-BOA-RQ-CLIENT-CHANGE-EMAIL
            description: Změnit email
          - name: PID-BOA-RQ-CLIENT-CHANGE-NATIONAL-NUMBER
            description: Změna národního identifikátoru
          - name: PID-BOA-RQ-CLIENT-CHANGE-PHONE
            description: Změnit telefon
          - name: PID-BOA-RQ-CLIENT-CREATE-PFS-ANALYSIS
            description: Vytvoření analýzy klienta v PFS
          - name: PID-BOA-RQ-CLIENT-IDENTIFY-CLIENT-DATA-IN-AIS
            description: Iniciace nebo aktualizace klientských údajů v základních registrech
          - name: PID-BOA-RQ-CLIENT-SEND-BANK-DOCUMENTATION-TO-PFS
            description: Odeslání smluvní dokumentace do PFS
          - name: PID-BOA-RQ-CLIENT-VIEW
            description: Zobrazit detail o klientovi
          - name: PID-BOA-RQ-CLIENT-VERIFY-IDENTITY
            description: Ověření identity klienta (pro vzdálený servis)
          - name: PID-BOA-RQ-EVENTS-VIEW
            description: Zobrazení událostí
          - name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-MANAGE
            description: Správa nastavení práv pro role poradce
          - name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-VIEW
            description: Zobrazit nastavení práv pro poradce
          - name: PID-BOA-RQ-SEARCH-ALL-CLIENTS
            description: Vyhledat všechny klienty
          - name: PID-BOA-RQ-SEARCH-BANK-CLIENTS
            description: Vyhledat bankovní klienty
          - name: PID-BOA-RQ-SEARCH-SIMPLEA-CLIENTS
            description: Vyhledat simplea klienty
          - name: PID-BOA-RQ-SERVICE-BANK-CARDS-CLICK-TO-PAY-VIEW
            description: Náhled na všechny karty v click-to-pay
          - name: PID-BOA-RQ-SERVICE-BANK-IDENTITY-VIEW
            description: Náhled na všechny bankovní identity
          - name: PID-BOA-RQ-SERVICE-BANK-MOBILITY-VIEW
            description: Náhled na všechny mobility
          - name: PID-BOA-RQ-SERVICE-EVENTS-VIEW
            description: Seznam událostí - admin pohled
          - name: PID-BOA-RQ-SERVICE-PID-ONBOARDING-VIEW
            description: Výpis aktivací klienta
          - name: PID-BOA-RQ-SIGN-REQUESTS-VIEW
            description: Zobrazit požadavky
          - name: PID-BOA-RQ-USER-DATA-VIEW
            description: Načtení informací o uživateli
          - name: PID-BOA-RQ-BANK-IDENTITY-BLOCK
            description: Zablokovat BankID identitu
          - name: PID-BOA-RQ-BANK-IDENTITY-UNBLOCK
            description: Odblokovat BankID identitu
          - name: PID-BOA-RQ-BANK-IDENTITY-VIEW
            description: Zobrazit informace o BankID klienta
          - name: PID-BOA-RQ-BANK-MOBILITY-CREATE
            description: Role for Mobility creation
          - name: PID-BOA-RQ-PFS-ADVISOR-DEFINITION-REFRESH-ADVISOR-ROLES
            description: Role for refresh advisor roles
          # Composite roles definition (old approach)
          - name: PID-BOA-ADMIN
            description: Role for Admin user
          - name: PID-BOA-BANK-ACCOUNTS
            description: Role for Account management department
          - name: PID-BOA-BANK-CALL-CENTRUM
            description: Role for Call centrum department
          - name: PID-BOA-BANK-CALL-CENTRUM-SENIOR
            description: Role for Call centrum department and senior users
          - name: PID-BOA-BANK-CALL-CENTRUM-LIMITED
            description: Role for Call centrum department with limited rights
          - name: PID-BOA-BANK-CARDS
            description: Role for Card management department
          - name: PID-BOA-BANK-CRM-AND-SALES
            description: Role for CRM and Sales department
          - name: PID-BOA-BANK-DIGITAL
            description: Role for Digital channels user
          - name: PID-BOA-BANK-DWH
            description: Role for Bank DWH department
          - name: PID-BOA-BANK-LOANS-LIMITED
            description: Role for Loan backoffice department
          - name: PID-BOA-BANK-PAYMENTS-SUPPORT
            description: Role for Payments support team
          - name: PID-BOA-BANK-PRODUCT-MANAGEMENT
            description: Role for Product management users
          - name: PID-BOA-BANK-SUPPORT
            description: Role for Support team users
          - name: PID-BOA-COMPLIANCE
            description: Role for Compliance department
          - name: PID-BOA-LEGAL
            description: Role for Legal department
          - name: PID-BOA-PID-SUPERVISOR
            description: Role for PID Supervisor user
          - name: PID-BOA-RISK-AML
            description: Role for ALM officer user
          - name: PID-BOA-RISK-COLLECTIONS
            description: Role for Collections department
          - name: PID-BOA-SIMPLEA
            description: Role for Simplea company
          - name: PID-BOA-NEBANKA-SUPPORT
            description: Role for Nebanka support team
      - name: "world-pfs-portfolio"
        description: |
          Holder for PFS Portfolio roles
        public-client: true
        roles:
          - name: PFS_PORTFOLIO_USER
            description: Portfolio owner
      - name: 'world-daily'
        description: |
          Holder for bank daily user roles
        public-client: true
        roles:
          - name: DAILY_ACTIVATED
            description: Adult user of Bank
          - name: DAILY_CHILD_ACTIVATED
            description: Child user of Bank
      - name: 'world-loan'
        description: |
          Holder for bank loan user roles
        public-client: true
        roles:
          - name: LOAN_ACTIVATED
            description: User of Bank that can access Loan
      - name: 'world-idm-foreclosures'
        description: |
          Holder for bank-foreclosures user roles
        public-client: true
        roles:
          - name: FORECLOSURES_BACKOFFICE_USER
            description: Role for bank-foreclosures backoffice user
          - name: FORECLOSURES_BACKOFFICE_ADMIN
            description: Role for bank-foreclosures backoffice admin
          - name: FORECLOSURES_BACKOFFICE_SUPER_ADMIN
            description: Role for bank-foreclosures backoffice super admin
          - name: FORECLOSURES_RISK_USER
            description: Role for bank-foreclosures risk user
          - name: FORECLOSURES_RISK_ADMIN
            description: Role for bank-foreclosures risk admin
          - name: FORECLOSURES_RISK_SUPER_ADMIN
            description: Role for bank-foreclosures risk super admin
          - name: FORECLOSURES_LEGAL_USER
            description: Role for bank-foreclosures legal user
          - name: FORECLOSURES_LEGAL_ADMIN
            description: Role for bank-foreclosures legal admin
          - name: FORECLOSURES_LEGAL_SUPER_ADMIN
            description: Role for bank-foreclosures legal super admin
      - name: 'frontend-bank-foreclosures'
        description: |
          Client for bank-foreclosures zauzoo module
        public-client: true
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
      - name: 'component-aml-services'
        description: |
          Service client for AML Services
        service-accounts-enabled: true
        standard-flow-enabled: true
        redirect-uris-required: true
        roles:
          - name: AML_SERVICES_ROLE_ADMIN
            description: Admin role for the AML Services
          - name: AML_SERVICES_ROLE_APP
            description: Internal app role for the AML Services
          - name: AML_SERVICES_ROLE_APP_PUBLIC
            description: App role for the AML Services
          - name: AML_SERVICES_ROLE_USER
            description: User role for the AML Services
        assign-roles:
          - client-name: component-aml-els
            role-name: AML_ELS_ROLE_APP
          - client-name: component-aml-sl-reader
            role-name: AML_SL_READER_ROLE_APP
          - client-name: component-aml-camunda
            role-name: AML_CAMUNDA_ROLE_APP
      - name: 'component-aml-els'
        description: |
          Service client for AML ELS
        service-accounts-enabled: true
        standard-flow-enabled: true
        redirect-uris-required: true
        roles:
          - name: AML_ELS_ROLE_ADMIN
            description: Admin role for AML ELS
          - name: AML_ELS_ROLE_APP
            description: Internal app role for AML ELS
          - name: AML_ELS_ROLE_APP_PUBLIC
            description: App role for the AML ELS
          - name: AML_ELS_ROLE_USER
            description: User role for AML ELS
          - name: AML_ELS_ROLE_EDITOR
            description: Editor role for AML ELS
          - name: AML_ELS_ROLE_VIEWER
            description: Viewer role for AML ELS
      - name: 'component-aml-sl-reader'
        description: |
          Service client for AML SL Reader
        service-accounts-enabled: true
        standard-flow-enabled: true
        redirect-uris-required: true
        roles:
          - name: AML_SL_READER_ROLE_ADMIN
            description: Admin role for AML SL READER
          - name: AML_SL_READER_ROLE_APP
            description: Internal app role for AML SL READER
          - name: AML_SL_READER_ROLE_USER
            description: User role for AML SL READER
        assign-roles:
          - client-name: component-aml-els
            role-name: AML_ELS_ROLE_APP
          - client-name: component-aml-services
            role-name: AML_SERVICES_ROLE_APP
      - name: 'component-aml-camunda'
        description: |
          Service client for AML Camunda
        service-accounts-enabled: true
        standard-flow-enabled: true
        redirect-uris-required: true
        roles:
          - name: AML_CAMUNDA_ROLE_APP
            description: Internal app role for AML Camunda
          - name: AML_CAM_ROLE_ADMIN
            description: Admin role for AML Camunda
        assign-roles:
          - client-name: component-aml-services
            role-name: AML_SERVICES_ROLE_APP
          - client-name: realm-management
            role-name: query-groups
          - client-name: realm-management
            role-name: query-users
          - client-name: realm-management
            role-name: view-users
      - name: 'service-pid-aml'
        description: |
          Service client for pid-aml service
        service-accounts-enabled: true
        assign-roles:
          - client-name: component-aml-services
            role-name: AML_SERVICES_ROLE_APP_PUBLIC
      - name: 'frontend-loan-application'
        description: |
          Frontend client for loan application
        public-client: true
        standard-flow-enabled: true
        direct-access-grants-enabled: true
        web-origins-required: true
        redirect-uris-required: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'rs-loan-application'
        description: |
          Resource server for loan application
        authorization-services-enabled: true
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: manage-authorization
      - name: 'frontend-loan-processes'
        description: |
          Frontend client for loan processes
        public-client: true
        standard-flow-enabled: true
        direct-access-grants-enabled: true
        web-origins-required: true
        redirect-uris-required: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'rs-loan-processes'
        description: |
          Resource server for loan processes
        authorization-services-enabled: true
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: manage-authorization
      - name: 'frontend-loan-templating'
        description: |
          Frontend client for loan templating
        public-client: true
        standard-flow-enabled: true
        web-origins-required: true
        redirect-uris-required: true
        assign-client-scopes:
          - name: offline_access
            type: NONE
      - name: 'Testovací PSD2 DEMO klient'
        client-id: 'psd2-tpp-dummy'
        description: |
          Dummy PSD2 third party client for smoke tests and testing
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: CISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'Komerční banka, a.s.'
        client-id: 'psd2-tpp-finbricks'
        description: |
          Finbrticks PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: CISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'Partners Banka, a.s.'
        client-id: 'psd2-tpp-partners-banka'
        description: |
          Partners Banka PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: CISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'GoPay'
        client-id: 'psd2-tpp-gopay'
        description: |
          GoPay PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: CISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'PayU'
        client-id: 'psd2-tpp-payu'
        description: |
          Partners Banka PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: CISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'TRINITY BANK a.s.'
        client-id: 'psd2-tpp-trinity-bank'
        description: |
          Partners Banka PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: CISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'Inbank'
        client-id: 'psd2-tpp-inbank'
        description: |
          Partners Banka PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: CISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'BANK TRANSFER by WORLDLINE'
        client-id: 'psd2-tpp-bank-transfer-worldline'
        description: |
          Partners Banka PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'ConnectPay UAB'
        client-id: 'psd2-tpp-connect-pay-uab'
        description: |
          ConnectPay UAB PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT
      - name: 'Everifin'
        client-id: 'psd2-tpp-everifin'
        description: |
          Everifin UAB PSD2 third party client
        service-accounts-enabled: false
        standard-flow-enabled: true
        redirect-uris-required: true
        web-origins-required: true
        authentication-flow-required: true
        assign-client-scopes:
          - name: AISP
            type: OPTIONAL
          - name: CISP
            type: NONE
          - name: PISP
            type: OPTIONAL
          - name: roles
            type: NONE
          - name: offline_access
            type: DEFAULT

      - name: 'component-decision-engine-aml'
        description: |
          Component service client for decision engine, used only to call AML services.
        service-accounts-enabled: true
        assign-roles:
          - client-name: component-aml-els
            role-name: AML_ELS_ROLE_APP
      - name: 'rs-boa'
        description: |
          Resource server for backoffice
        authorization-services-enabled: true
        assign-roles:
          - client-name: world-idm-boa
            role-name: PID-BOA-BANK-CALL-CENTRUM
          - client-name: world-idm-boa
            role-name: PID-BOA-ADMIN
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: manage-authorization
      - name: 'service-idm-midpoint-client'
        description: |
          IDM Midpoint client
        authorization-services-enabled: true
        assign-roles:
          - client-name: realm-management
            role-name: manage-realm
          - client-name: realm-management
            role-name: view-clients
          - client-name: realm-management
            role-name: query-clients
          - client-name: realm-management
            role-name: manage-users
          - client-name: realm-management
            role-name: query-groups
          - client-name: account
            role-name: manage-account
  - name: 'services'
    client-scopes:
      - name: 'default'
        description: |
          Default scope needed by WSO2 APIM
        type: DEFAULT
    clients-openid:
      - name: 'wso2-apim-client-services'
        description: |
          Binding client for WSO2 APIM
        service-accounts-enabled: true
        direct-access-grants-enabled: false
        public-client: false
        assign-roles:
          - client-name: realm-management
            role-name: view-clients
        assign-client-scopes:
          - name: default
            type: DEFAULT
      - name: 'department-bank-employee-client'
        description: |
          The department client for Bank Employee role (created mainly for automation test support)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK_EMPLOYEE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
      - name: 'department-pfs-consultant-client'
        description: |
          The department client for PFS Consultant role (created mainly for automation test support)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PFS_CONSULTANT
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
      - name: 'department-fip-client'
        description: |
          The department client for FIP
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-pfs-integration
            role-name: LOAN-ACCESS
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PFS
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-READ
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PFS_PORTFOLIO
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_ACTIVATION
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
          - client-name: service-pid-identity-blocking
            role-name: READ-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: PFS-SEAL-PID-WORKFLOW
      - name: 'department-fip-sk-client'
        description: |
          The department client for FIP in SK
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_SFS
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-READ
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PFS_PORTFOLIO
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_ACTIVATION
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
          - client-name: service-pid-identity-blocking
            role-name: READ-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: SFS-SEAL-PID-WORKFLOW
      - name: 'department-fip-payment-client'
        description: |
          Service client for FIP to create payments
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-payment
            role-name: BANK_CLIENT_PAYMENTS_WRITE
          - client-name: service-bank-standing-order
            role-name: BANK_CLIENT_STANDING_ORDER_WRITE
      - name: 'department-orfeus-client'
        description: |
          The department client for ORFEUS
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-pfs-integration
            role-name: LOAN-ACCESS
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_ORFEUS
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-READ
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PFS_PORTFOLIO
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: PFS_ADVISOR_SELECTION
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_ACTIVATION
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
          - client-name: service-pid-identity-blocking
            role-name: READ-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: PFS-SEAL-PID-WORKFLOW
      - name: 'department-orfeus-sk-client'
        description: |
          The department client for ORFEUS in SK
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_ORFEUS_SK
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-READ
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PFS_PORTFOLIO
          - client-name: service-pid-gateway
            role-name: PFS_ADVISOR_SELECTION
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_ACTIVATION
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
          - client-name: service-pid-identity-blocking
            role-name: READ-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: SFS-SEAL-PID-WORKFLOW
      - name: 'department-anakin-client'
        description: |
          The department client for Anakin (former Akela)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PFS
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
          - client-name: service-pid-gateway
            role-name: BANK_PRODUCT_COMPARISON
      - name: 'department-anakin-sk-client'
        description: |
          The department client for Anakin (former Akela) in SK
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_SFS
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
      - name: 'department-ocp-client'
        description: |
          The department client for OCP (Shares trading)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_OCP
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-identity-blocking
            role-name: READ-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: OCP-SEAL-PID-WORKFLOW
      - name: 'department-pfs-client'
        description: |
          The department client for PFS (Partners Financial Services)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PFS
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-READ
          - client-name: service-bank-service-gateway
            role-name: DAILY-PFS-ACCOUNT-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PFS_PORTFOLIO
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_ACTIVATION
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
          - client-name: service-pid-identity-blocking
            role-name: READ-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: PFS-SEAL-PID-WORKFLOW
      - name: 'department-pid-client'
        description: |
          The department client for PID (Partners Identity)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID
          - client-name: service-pid-gateway
            role-name: ROLE_PFS_PORTFOLIO
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_ACTIVATION
          - client-name: service-bank-service-gateway
            role-name: DAILY-PID-NOTIFICATION-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: ANY-SEAL-PID-WORKFLOW
      - name: 'service-aml-plugin-service'
        description: |
          Service client for AML Plugin Service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
      - name: 'service-paf-case-investigation-core'
        description: |
          Service client for PAF
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-bank-transaction
            role-name: BANK_CLIENT_TRANSACTIONS_READ
          - client-name: service-bank-payment
            role-name: BANK_CLIENT_PAYMENTS_READ
      - name: 'department-pid-test-client'
        description: |
          The department client for PID (Partners Identity) for testing
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID_TEST
          - client-name: service-notification-gateway
            role-name: EXPONEA-NOTIFICATIONS-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_EXTERNAL_AML_PROVIDER
          - client-name: service-pid-gateway
            role-name: ROLE_PFS_PORTFOLIO
          - client-name: service-bank-service-gateway
            role-name: DAILY-BANK-SIGN-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_ACTIVATION
          - client-name: service-bank-service-gateway
            role-name: DAILY-PID-NOTIFICATION-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_OCR_SCAN
          - client-name: service-pid-gateway
            role-name: ROLE_AIS_DATA_READ
          - client-name: service-pid-identity-blocking
            role-name: ACCESS-BLOCK-PID-IDENTITY
          - client-name: service-pid-identity-blocking
            role-name: ACCESS-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: ANY-SEAL-PID-WORKFLOW
      - name: 'department-pis-client'
        description: |
          The department client for PIS
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PFS
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
      - name: 'department-rentea-client'
        description: |
          The department client for Rentea (Retirement Savings)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_RENTEA
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: RENTEA-SEAL-PID-WORKFLOW
      - name: 'department-simplea-client'
        description: |
          The department client for Simplea (Insurance)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_SIMPLEA
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_GET
          - client-name: service-pid-identity-blocking
            role-name: READ-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: SIMPLEA-SEAL-PID-WORKFLOW
      - name: 'department-vub-client'
        description: |
          The department client for VUB Bank
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_VUB
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-workflow
            role-name: ACCESS-WORKFLOW-PID-WORKFLOW
          - client-name: service-pid-workflow
            role-name: VUB-SEAL-PID-WORKFLOW
      - name: 'department-gepard-client'
        description: |
          The department client for Gepard Finance (mortgage loans)
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_GEPARD
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
      - name: 'department-odoo-client'
        description: |
          The client for odoo
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-treasury-service
            role-name: ODOO_DATA_WRITE
          - client-name: service-bank-treasury-service
            role-name: ODOO_DATA_READ

      - name: 'service-keycloak-user-manager'
        description: |
          Service client for keycloak-user-manager service
        service-accounts-enabled: true
        roles:
          - name: FIND-USER_PID
            description: Allows to find users in realm pid
          - name: FIND-USER_ONBOARD
            description: Allows to find users in realm onboard
          - name: FIND-USER_PFS
            description: Allows to find users in realm pfs
          - name: FIND-USER_GEPARD
            description: Allows to find users in realm gepard
          - name: CREATE-USER_PID
            description: Allows to create users in realm pid
          - name: CREATE-USER_ONBOARD
            description: Allows to create users in realm onboard
          - name: CREATE-USER_PFS
            description: Allows to create users in realm pfs
          - name: DELETE-USER_PFS
            description: Allows to delete users in realm pfs
          - name: CREATE-GROUP_PID
            description: Allows to create group in realm pid
          - name: MANAGE-GROUP-ROLE_PID
            description: Allows to manage role for group in realm PID
          - name: MANAGE-USER-GROUP_PID
            description: Allows to manage group for user in realm PID
          - name: MANAGE-USER-ROLE_PID_WORLD-PID
            description: Allows to assign role from client world-pid in realm pid
          - name: MANAGE-USER-ROLE_PID_WORLD-PFS-PORTFOLIO
            description: Allows to assign role from client world-pfs-portfolio in realm pid
          - name: MANAGE-USER-ROLE_PID_WORLD-DAILY
            description: Allows to assign role from client world-daily in realm pid
          - name: MANAGE-USER-ROLE_ONBOARD_WORLD-ONBOARD
            description: Allows to assign role from client world-onboard in realm onboard
          - name: MANAGE-USER-ROLE_PID_WORLD-BANK
            description: Allows to assign role from client world-bank in realm pid
          - name: MANAGE-USER-ROLE_PID_WORLD-BANK-APS
            description: Allows to assign role from client world-bank-aps in realm pid
          - name: MANAGE-USER-ROLE_PID_WORLD-BANK-BACKOFFICE
            description: Allows to assign role from client world-bank-backoffice in realm pid
          - name: MANAGE-USER-ROLE_PFS_WORLD-PFS
            description: Allows to assign role from client world-pfs in realm pfs
          - name: MANAGE-USER-ROLE_PFS_WORLD-BANK-APS
            description: Allows to assign role from client world-bank-aps in realm pfs
          - name: MANAGE-USER-ROLE_PID_WORLD-LOAN
            description: Allows to assign role from client world-loan in realm pid
          - name: GET-ACCESS-TOKEN_PID_FRONTEND-MOBILE-APP
            description: Allows to get access token for client frontend-mobile-app in realm pid
          - name: GET-ACCESS-TOKEN_ONBOARD_FRONTEND-MOBILE-APP
            description: Allows to get access token for client frontend-mobile-app in realm onboard
          - name: GET-ACCESS-TOKEN_PFS_FRONTEND-BOA-PFS
            description: Allows to get access token for client frontend-boa in realm pfs
          - name: GET-ACCESS-TOKEN_PID_FRONTEND-BOA
            description: Allows to get access token for client frontend-boa in realm pid
          - name: GET-ACCESS-TOKEN_PFS_FRONTEND-APS
            description: Allows to get access token for client frontend-aps in realm pfs
          - name: GET-ACCESS-TOKEN_PFS_FRONTEND-LOAN-APPLICATION-PFS
            description: Allows to get access token for client frontend-loan-application-pfs in realm pfs
          - name: GET-ACCESS-TOKEN_PFS_FRONTEND-BANK-ONBOARDING
            description: Allows to get access token for client frontend-bank-onboarding in realm pfs
      - name: 'service-pfs-auth-manager'
        description: |
          Service client for pfs-auth-manager service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-keycloak-user-manager
            role-name: CREATE-USER_PFS
          - client-name: service-keycloak-user-manager
            role-name: DELETE-USER_PFS
          - client-name: service-keycloak-user-manager
            role-name: MANAGE-USER-ROLE_PFS_WORLD-PFS
          - client-name: service-keycloak-user-manager
            role-name: MANAGE-USER-ROLE_PFS_WORLD-BANK-APS
          - client-name: service-keycloak-user-manager
            role-name: GET-ACCESS-TOKEN_PFS_FRONTEND-BOA-PFS
          - client-name: service-keycloak-user-manager
            role-name: GET-ACCESS-TOKEN_PFS_FRONTEND-APS
          - client-name: service-keycloak-user-manager
            role-name: GET-ACCESS-TOKEN_PFS_FRONTEND-BANK-ONBOARDING
          - client-name: service-keycloak-user-manager
            role-name: GET-ACCESS-TOKEN_PFS_FRONTEND-LOAN-APPLICATION-PFS
      - name: 'service-pfs-portfolio-auth-manager'
        description: |
          Service client for pfs-portfolio-auth-manager service
        service-accounts-enabled: true
        roles:
          - name: MANAGE-USER-ROLE_PFS-PORTFOLIO
            description: Allows to assign role from client world-pfs-portfolio in realm pid
        assign-roles:
          - client-name: service-keycloak-user-manager
            role-name: MANAGE-USER-ROLE_PID_WORLD-PFS-PORTFOLIO
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-pfs-portfolio'
        description: |
          Service client for pfs-portfolio service
        service-accounts-enabled: true
        roles:
          - name: ONBOARDING_ADVISOR_SELECTION
            description: Role for services which have access to advisors in pfs-portfolio
        assign-roles:
          - client-name: service-pfs-portfolio-auth-manager
            role-name: MANAGE-USER-ROLE_PFS-PORTFOLIO
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_NONBANK
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-pfs-insurance-comparator'
        description: |
          Service client for pfs-insurance-comparator service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_NONBANK
      - name: 'service-pfs-contract-sharing'
        description: |
          Service client for pfs-contract-sharing service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_GET
      - name: 'service-pfs-acceptance'
        description: |
          Service client for pfs-acceptance service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_NONBANK
          - client-name: service-pid-gateway
            role-name: CONTRACT_SIGNATURE_ANTIVIRUS_CHECK
      - name: 'service-nebanka-ocp'
        description: |
          Service client for nebanka-ocp service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-codelists
            role-name: ACCESS-BANK-CODELISTS
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
      - name: 'department-moje-partners-client'
        description: |
          Department client for Moje Partners
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PFS_CONTRACT_SHARING
      - name: 'service-pid-activation-backoffice-gateway'
        description: |
          Service client for pid-activation-backoffice-gateway service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID
      - name: 'service-pid-aml'
        description: |
          Service client for pid-aml service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-service-gateway
            role-name: DAILY-AML-RESULT-WRITE
      - name: 'service-pid-auth-manager'
        description: |
          Service client for pid-auth-manager service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-keycloak-user-manager
            role-name: FIND-USER_PID
          - client-name: service-keycloak-user-manager
            role-name: CREATE-USER_PID
          - client-name: service-keycloak-user-manager
            role-name: MANAGE-USER-ROLE_PID_WORLD-PID
          - client-name: service-keycloak-user-manager
            role-name: MANAGE-USER-ROLE_PID_WORLD-BANK-BACKOFFICE
          - client-name: service-keycloak-user-manager
            role-name: GET-ACCESS-TOKEN_PID_FRONTEND-MOBILE-APP
          - client-name: service-keycloak-user-manager
            role-name: GET-ACCESS-TOKEN_PID_FRONTEND-BOA
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
        roles:
          - name: CREATE-USER_PID
            description: Allows to create users in realm pid
          - name: MANAGE-USER-ROLE_PID
            description: Allows to assign role from client world-pid in realm pid
          - name: GET-ACCESS-TOKEN_PID
            description: Allows to get access token for supported frontends in realm pid
      - name: 'service-bank-daily-auth-manager'
        description: |
          Service client for bank-daily-auth-manager service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-keycloak-user-manager
            role-name: MANAGE-USER-ROLE_PID_WORLD-DAILY
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
        roles:
          - name: MANAGE-USER-ROLE_DAILY
            description: Allows to assign role from client world-daily in realm pid
      - name: 'service-bank-transaction-notification'
        description: |
          Service client for bank-transaction-notification
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-transaction'
        description: |
          Service client for bank-transaction
        service-accounts-enabled: true
        roles:
          - name: BANK_CLIENT_TRANSACTIONS_READ
            description: Allows to read transactions for service-paf-case-investigation-core
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: service-idp-apim-orchestrator
        description: |
          Service client for idp-apim-orchestrator service
        service-accounts-enabled: true
        roles:
          - name: GET-CLIENTS-OWN_PID
            description: Allows to retrieve clients created by the caller in realm pid
          - name: GET-CLIENTS-OWN_ONBOARD
            description: Allows to retrieve clients created by the caller in realm onboard
          - name: GET-CLIENTS-OWN_PFS
            description: Allows to retrieve clients created by the caller in realm pfs
          - name: GET-CLIENTS-OWN_SERVICES
            description: Allows to retrieve clients created by the caller in realm services
          - name: GET-CLIENTS-ALL_PID
            description: Allows to retrieve all clients regardless of who were they created by in realm pid
          - name: GET-CLIENTS-ALL_ONBOARD
            description: Allows to retrieve all clients regardless of who were they created by in realm onboard
          - name: GET-CLIENTS-ALL_PFS
            description: Allows to retrieve all clients regardless of who were they created by in realm pfs
          - name: GET-CLIENTS-ALL_SERVICES
            description: Allows to retrieve all clients regardless of who were they created by in realm services
          - name: MANAGE-CLIENTS-OWN_PID
            description: Allows to create/update/delete clients created by the caller in realm pid
          - name: MANAGE-CLIENTS-OWN_ONBOARD
            description: Allows to create/update/delete clients created by the caller in realm onboard
          - name: MANAGE-CLIENTS-OWN_PFS
            description: Allows to create/update/delete clients created by the caller in realm pfs
          - name: MANAGE-CLIENTS-OWN_SERVICES
            description: Allows to create/update/delete clients created by the caller in realm services
          - name: MANAGE-CLIENTS-ALL_PID
            description: Allows to create/update/delete all clients regardless of who were they created by in realm pid
          - name: MANAGE-CLIENTS-ALL_ONBOARD
            description: Allows to create/update/delete all clients regardless of who were they created by in realm onboard
          - name: MANAGE-CLIENTS-ALL_PFS
            description: Allows to create/update/delete all clients regardless of who were they created by in realm pfs
          - name: MANAGE-CLIENTS-ALL_SERVICES
            description: Allows to create/update/delete all clients regardless of who were they created by in realm services
          - name: MANAGE-SUBSCRIPTION-GROUP_BANK_ID
            description: Allows to add or remove clients from and to APIM subscription group 'BANK_ID'
          - name: MANAGE-SUBSCRIPTION-GROUP_PSD2
            description: Allows to add or remove clients from and to APIM subscription group 'PSD2'
          - name: SET-API-SUBSCRIPTIONS
            description: Allows to set API subscriptions. Primarily meant for WSO2 used for WSO2 API deployment
      - name: 'service-pid-onboard-auth-manager'
        description: |
          Service client for onboard-auth-manager service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-keycloak-user-manager
            role-name: FIND-USER_ONBOARD
          - client-name: service-keycloak-user-manager
            role-name: CREATE-USER_ONBOARD
          - client-name: service-keycloak-user-manager
            role-name: MANAGE-USER-ROLE_ONBOARD_WORLD-ONBOARD
          - client-name: service-keycloak-user-manager
            role-name: GET-ACCESS-TOKEN_ONBOARD_FRONTEND-MOBILE-APP
        roles:
          - name: CREATE-USER_ONBOARD
            description: Allows to create users in realm onboard
          - name: MANAGE-USER-ROLE_ONBOARD
            description: Allows to assign role from client world-onboard in realm onboard
          - name: GET-ACCESS-TOKEN_ONBOARD
            description: Allows to get access token for supported frontends in realm onboard
      - name: 'service-pid-bpm'
        description: |
          Service client for pid-bpm service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-auth-manager
            role-name: GET-ACCESS-TOKEN_PID
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-pid-bankid-oidc-connector'
        description: |
          Service client for pid-bankid-oidc-connector service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-idp-apim-orchestrator
            role-name: GET-CLIENTS-OWN_PID
          - client-name: service-idp-apim-orchestrator
            role-name: MANAGE-CLIENTS-OWN_PID
          - client-name: service-idp-apim-orchestrator
            role-name: MANAGE-SUBSCRIPTION-GROUP_BANK_ID
      - name: 'service-pid-bankid-data'
        description: |
          Service client for pid-bankid-data service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-service-gateway
            role-name: DAILY-PID-ACCOUNT-READ
          - client-name: service-pid-gateway
            role-name: ROLE_AIS_DATA_READ
      - name: 'service-pid-fed-authentication'
        description: |
          Service client for pid-fed-authentication service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-pid-onboarding'
        description: |
          Service client for pid-onboarding service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pfs-portfolio
            role-name: ONBOARDING_ADVISOR_SELECTION
          - client-name: service-pid-onboard-auth-manager
            role-name: CREATE-USER_ONBOARD
          - client-name: service-pid-onboard-auth-manager
            role-name: MANAGE-USER-ROLE_ONBOARD
          - client-name: service-pid-onboard-auth-manager
            role-name: GET-ACCESS-TOKEN_ONBOARD
          - client-name: service-pid-auth-manager
            role-name: GET-ACCESS-TOKEN_PID
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
          - client-name: service-pid-identity-blocking
            role-name: READ-BLACKLIST-PID-IDENTITY
      - name: 'service-pid-mobile-gateway'
        description: |
          Service client for pid-mobile-gateway service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-auth-manager
            role-name: GET-ACCESS-TOKEN_PID
          - client-name: service-pid-onboard-auth-manager
            role-name: GET-ACCESS-TOKEN_ONBOARD
          - client-name: service-bank-service-gateway
            role-name: DAILY-BANK-SIGN-WRITE
          - client-name: service-bank-service-gateway
            role-name: DAILY-BANK-SIGN-READ
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-pid-activation-service'
        description: |
          Service client for pid-activation-service service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-onboard-auth-manager
            role-name: CREATE-USER_ONBOARD
          - client-name: service-pid-onboard-auth-manager
            role-name: MANAGE-USER-ROLE_ONBOARD
          - client-name: service-pid-onboard-auth-manager
            role-name: GET-ACCESS-TOKEN_ONBOARD
      - name: 'service-pid-core'
        description: |
          Service client for pid-core service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-auth-manager
            role-name: CREATE-USER_PID
          - client-name: service-pid-auth-manager
            role-name: MANAGE-USER-ROLE_PID
          - client-name: service-notification-gateway
            role-name: PID-DAKTELA-CONTACT-WRITE
          - client-name: service-notification-gateway
            role-name: PID-EXPONEA-CUSTOMER-WRITE
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-pid-email'
        description: |
          Service client for pid-email service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-pid-notification-service'
        description: |
          Service client for pid-notification-service service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-service-gateway
            role-name: DAILY-PID-NOTIFICATION-WRITE
      - name: 'service-pid-identity-blocking'
        description: |
          Service client for pid-identity-blocking service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-BLOCK-PID-IDENTITY
            description: Allows to block all client's devices and his identity
          - name: ACCESS_PID_IDENTITY_BLOCKING
            description: Deprecated(Use ACCESS-BLOCK-PID-IDENTITY instead), Allows to block all client's devices and his identity
          - name: ACCESS-BLACKLIST-PID-IDENTITY
            description: Allows to read and add/remove client to/from blacklist
          - name: READ-BLACKLIST-PID-IDENTITY
            description: Allows to read blacklist
        assign-roles:
          - client-name: service-bank-jira-adapter
            role-name: JIRA-PROJECT-BLK
      - name: 'service-pid-workflow'
        description: |
          Service client for pid-workflow service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-WORKFLOW-PID-WORKFLOW
            description: Allows to read and add/remove workflows
          - name: READ-WORKFLOW-PID-WORKFLOW
            description: Allows to read workflows
          - name: BANK-SEAL-PID-WORKFLOW
            description: Allows to access Bank seal (signature)
          - name: PFS-SEAL-PID-WORKFLOW
            description: Allows to access PFS seal (signature)
          - name: SFS-SEAL-PID-WORKFLOW
            description: Allows to access SFS seal (signature)
          - name: SIMPLEA-SEAL-PID-WORKFLOW
            description: Allows to access Simplea seal (signature)
          - name: RENTEA-SEAL-PID-WORKFLOW
            description: Allows to access Rentea seal (signature)
          - name: OCP-SEAL-PID-WORKFLOW
            description: Allows to access OCP seal (signature)
          - name: VUB-SEAL-PID-WORKFLOW
            description: Allows to access VUB seal (signature)
          - name: ANY-SEAL-PID-WORKFLOW
            description: Allows to access any seal (signature)
        assign-roles:
          - client-name: service-pid-document-signer
            role-name: ROLE_PID_DOCUMENT_SIGNER_WRITE
          - client-name: service-pid-document-signer
            role-name: ROLE_PID_DOCUMENT_SIGNER_READ
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-pid-backoffice-events'
        description: |
          Service client for pid-backoffice-events service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID
      - name: 'service-pid-backoffice-gateway'
        description: |
          Service client for pid-backoffice-gateway service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-auth-manager
            role-name: GET-ACCESS-TOKEN_PID
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID
          - client-name: service-pid-identity-blocking
            role-name: ACCESS-BLACKLIST-PID-IDENTITY
          - client-name: service-pid-identity-blocking
            role-name: ACCESS-BLOCK-PID-IDENTITY
      - name: 'service-bank-pfs-integration'
        description: |
          Service client for bank-pfs-integration service
        service-accounts-enabled: true
        roles:
          - name: LOAN-ACCESS
            description: Allows access to loan data
        assign-roles:
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-bank-client-documents
            role-name: ACCESS-BANK-CLIENT-DOCUMENTS
      - name: 'service-bank-service-gateway'
        description: |
          Service client for bank-service-gateway
        service-accounts-enabled: true
        roles:
          - name: DAILY-PID-ACCOUNT-READ
            description: Allows read access to accounts for pid
          - name: DAILY-PFS-ACCOUNT-READ
            description: Allows read access to accounts for pfs
          - name: DAILY-PFS-ACCOUNT-WRITE
            description: Allows write access to accounts for pfs
          - name: DAILY-BANK-SIGN-READ
            description: Allows read access to bank sign
          - name: DAILY-BANK-SIGN-WRITE
            description: Allows write access to bank sign
          - name: DAILY-AML-RESULT-WRITE
            description: Allows write pid aml result
          - name: DAILY-PID-NOTIFICATION-WRITE
            description: Allows write access for pid event notifications
      - name: 'service-aps-modeling'
        description: |
          Service client for APS Modeling
        service-accounts-enabled: true
        roles:
          - name: APS-GET-MODELING
            description: Allows to get a modeling from APS Modeling
        assign-roles:
          - client-name: service-loan-de-bridge
            role-name: ACCESS-LOAN-DE-BRIDGE
          - client-name: service-loan-product-catalog-bridge
            role-name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
          - client-name: service-aps-parameters
            role-name: APS-GET-PARAMETERS
      - name: 'service-aps-bff'
        description: |
          Service client for APS BFF
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-aps-modeling
            role-name: APS-GET-MODELING
          - client-name: service-aps-processes
            role-name: APS-PROCESSES-BFF
          - client-name: service-loan-product-catalog-bridge
            role-name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
          - client-name: service-loan-codelists-bridge
            role-name: ACCESS-LOAN-CODELISTS-BRIDGE
          - client-name: service-loan-whisper-bridge
            role-name: ACCESS-LOAN-WHISPER-BRIDGE
          - client-name: service-loan-pfs-bridge
            role-name: ACCESS-LOAN-PFS-BRIDGE
          - client-name: service-aps-pepz
            role-name: APS-READ-INDIVIDUAL-AGREEMENTS
          - client-name: service-aps-pepz
            role-name: APS-EDIT-INDIVIDUAL-AGREEMENTS
      - name: 'service-loan-whisper-bridge'
        description: |
          Service client for loan-whisper-bridge service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-WHISPER-BRIDGE
            description: Allows access to loan whisper bridge
      - name: 'service-loan-de-bridge'
        description: |
          Service client for loan-de-bridge service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-DE-BRIDGE
            description: Allows access to loan de bridge
      - name: 'service-loan-pid-bridge'
        description: |
          Service client for loan-pid-bridge service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-PID-BRIDGE
            description: Allows access to loan pid bridge
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_LOAN
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_GET
      - name: 'service-loan-product-catalog-bridge'
        description: |
          Service client for loan-product-catalog-bridge service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
            description: Allows access to loan product catalog bridge
      - name: 'service-loan-core-bridge'
        description: |
          Service client for loan core bridge service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-SIMULATE-LOAN-PARAMETER
            description: Allows access to simulate loan parameter operation
          - name: ACCESS-GET-MAIN-ACCOUNT
            description: Allows access to get-main-account operation
        assign-roles:
          - client-name: service-loan-account-registry
            role-name: ACCESS-LOAN-ACCOUNT-REGISTRY
      - name: 'component-decision-engine'
        description: |
          Component service client for decision engine
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-loan-core-bridge
            role-name: ACCESS-SIMULATE-LOAN-PARAMETER
          - client-name: service-loan-documents-bridge
            role-name: ACCESS-LOAN-DOCUMENTS-BRIDGE
      - name: 'service-aps-application'
        description: |
          Service client for APS Modeling
        service-accounts-enabled: true
        roles:
          - name: APS-APPLICATION-READ
            description: Allows to read an application
          - name: APS-APPLICATION-WRITE
            description: Allows to update an application
          - name: APS-APPLICATION-DOCUMENTS-READ
            description: Allows to get application documents
          - name: APS-COLLATERAL-NOTIFICATIONS-WRITE
            description: Allows to send notifications about collateral
        assign-roles:
          - client-name: service-loan-de-bridge
            role-name: ACCESS-LOAN-DE-BRIDGE
          - client-name: service-loan-product-catalog-bridge
            role-name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
          - client-name: service-loan-pfs-bridge
            role-name: ACCESS-LOAN-PFS-BRIDGE
          - client-name: service-aps-parameters
            role-name: APS-GET-PARAMETERS
          - client-name: service-loan-whisper-bridge
            role-name: ACCESS-LOAN-WHISPER-BRIDGE
          - client-name: service-loan-codelists-bridge
            role-name: ACCESS-LOAN-CODELISTS-BRIDGE
          - client-name: service-loan-core-bridge
            role-name: ACCESS-GET-MAIN-ACCOUNT
          - client-name: service-loan-documents-bridge
            role-name: ACCESS-LOAN-DOCUMENTS-BRIDGE
          - client-name: service-aps-document
            role-name: APS-DOCUMENTS-WRITE
          - client-name: service-aps-document
            role-name: APS-DOCUMENTS-READ
          - client-name: service-aps-loan
            role-name: APS-READ-LOAN
          - client-name: service-aps-loan
            role-name: APS-EDIT-LOAN
      - name: 'service-aps-gate'
        description: Service APS Gate
        service-accounts-enabled: true
        roles:
          - name: APS-APPLICATION-DOCUMENTS-READ
            description: Get documents from APS Loan Application
          - name: APS-COLLATERAL-NOTIFICATIONS-WRITE
            description: Send notifications about collateral
        assign-roles:
          - client-name: service-aps-application
            role-name: APS-APPLICATION-DOCUMENTS-READ
          - client-name: service-aps-application
            role-name: APS-COLLATERAL-NOTIFICATIONS-WRITE
      - name: 'service-loan-pfs-bridge'
        description: |
          Service client for loan pfs bridge service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-PFS-BRIDGE
            description: Allows access to loan pfs bridge
          - name: ACCESS-GET-GEPARD-USER
            description: Allows access to get gepard user
        assign-roles:
          - client-name: service-pfs-advisor-definition
            role-name: ACCESS-PFS-ADVISOR-DEFINITION
          - client-name: service-keycloak-user-manager
            role-name: FIND-USER_PFS
          - client-name: service-keycloak-user-manager
            role-name: FIND-USER_GEPARD
      - name: 'service-pfs-advisor-definition'
        description: |
          Service client for pfs advisor definition service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-PFS-ADVISOR-DEFINITION
            description: Allows access to pfs advisor definition service
      - name: 'service-aps-loan'
        description: |
          Service client for APS loan
        service-accounts-enabled: true
        roles:
          - name: APS-READ-LOAN
            description: Allows to read loan from APS Loan
          - name: APS-EDIT-LOAN
            description: Allows to edit loan from APS Loan
      - name: 'service-aps-parameters'
        description: |
          Service client for APS Parameters
        service-accounts-enabled: true
        roles:
          - name: APS-GET-PARAMETERS
            description: Allows to get a parameters from APS Parameters
      - name: 'service-loan-codelists-bridge'
        description: |
          Service client for loan codelists bridge service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-CODELISTS-BRIDGE
            description: Allows access to loan codelists bridge
        assign-roles:
          - client-name: service-bank-codelists
            role-name: ACCESS-BANK-CODELISTS
      - name: 'service-bank-codelists'
        description: |
          Service client for bank codelists service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-BANK-CODELISTS
            description: Allows access to bank codelists
      - name: 'service-bank-payment'
        description: |
          Service client for bank-payment
        service-accounts-enabled: true
        roles:
          - name: BANK_CLIENT_PAYMENTS_WRITE
            description: Allows to create payments for fip
          - name: BANK_CLIENT_PAYMENTS_READ
            description: Allows to read payments for service-paf-case-investigation-core
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-accounts-registry'
        description: |
          Service client for bank accounts registry service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-daily-auth-manager
            role-name: MANAGE-USER-ROLE_DAILY
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-client'
        description: |
          Service client for bank client service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-daily-auth-manager
            role-name: MANAGE-USER-ROLE_DAILY
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
      - name: 'component-knbox'
        description: |
          Component service client for knbox
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-codelists
            role-name: ACCESS-BANK-CODELISTS
      - name: 'service-aps-origination'
        description: Service APS Origination
        service-accounts-enabled: true
        roles:
          - name: APS-ORIGINATION-WRITE
            description: Allows to create an origination process instance
        assign-roles:
          - client-name: service-aps-application
            role-name: APS-APPLICATION-READ
          - client-name: service-aps-application
            role-name: APS-APPLICATION-WRITE
          - client-name: service-aps-application
            role-name: APS-APPLICATION-DOCUMENTS-READ
          - client-name: service-aps-application
            role-name: APS-COLLATERAL-NOTIFICATIONS-WRITE
          - client-name: service-aps-processes
            role-name: APS-PROCESSES-BPM
      - name: 'service-aps-processes'
        description: Service APS Processes
        service-accounts-enabled: true
        roles:
          - name: APS-PROCESSES-BPM
            description: Roles for access bpm endpoints
          - name: APS-PROCESSES-BFF
            description: Roles for access to bff endpoints
        assign-roles:
          - client-name: service-aps-origination
            role-name: APS-ORIGINATION-WRITE
      - name: 'service-bank-client-documents'
        description: |
          Service client for bank client documents
        service-accounts-enabled: true
        roles:
          - name: ACCESS-BANK-CLIENT-DOCUMENTS
            description: Allow access to bank client documents
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
      - name: 'service-loan-documents-bridge'
        description: |
          Service client for loan documents bridge
        service-accounts-enabled: true
        roles:
          - name: "ACCESS-LOAN-DOCUMENTS-BRIDGE"
            description: Allow access to loan documents bridge
        assign-roles:
          - client-name: service-bank-client-documents
            role-name: ACCESS-BANK-CLIENT-DOCUMENTS
      - name: 'service-loan-knbox-bridge'
        description: |
          Service client for loan knbox bridge
        service-accounts-enabled: true
        roles:
          - name: "ACCESS-LOAN-KNBOX-BRIDGE"
            description: Allow access to loan knbox bridge
      - name: 'service-loan-notification-bridge'
        description: |
          Service client for loan notification bridge
        service-accounts-enabled: true
        roles:
          - name: "ACCESS-LOAN-NOTIFICATION-BRIDGE"
            description: Allow access to loan notification bridge
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-loan-customer-gateway'
        description: |
          Service client for loan customer gateway
        service-accounts-enabled: true
      - name: 'service-loan-sign-bridge'
        description: |
          Service client for loan sign bridge
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-SIGN-BRIDGE
            description: Allow access to loan sign bridge
      - name: 'service-aps-pepz'
        description: |
          Service client for APS PEPZ
        service-accounts-enabled: true
        roles:
          - name: APS-READ-INDIVIDUAL-AGREEMENTS
            description: Allows to read individual agreements from APS PEPZ
          - name: APS-EDIT-INDIVIDUAL-AGREEMENTS
            description: Allows to edit individual agreements from APS PEPZ
      - name: 'service-aps-document'
        description: Service APS Document
        service-accounts-enabled: true
        roles:
          - name: APS-DOCUMENTS-WRITE
            description: Allows to create an document
          - name: APS-DOCUMENTS-READ
            description: Allows to read an document
      - name: 'service-aps-valuers'
        description: Service APS Valuers
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-aps-parameters
            role-name: APS-GET-PARAMETERS
          - client-name: service-aps-document
            role-name: APS-DOCUMENTS-WRITE
          - client-name: service-aps-document
            role-name: APS-DOCUMENTS-READ
      - name: 'service-aps-exception'
        description: Service APS Exception
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-aps-parameters
            role-name: APS-GET-PARAMETERS
          - client-name: service-aps-document
            role-name: APS-DOCUMENTS-WRITE
          - client-name: service-aps-document
            role-name: APS-DOCUMENTS-READ
      - name: 'service-bank-chargeback'
        description: |
          Service client for bank-chargeback service
        service-accounts-enabled: true
      - name: 'service-loan-application-unsecured'
        description: Service client for loan-application-unsecured service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-APPLICATION-UNSECURED
            description: Allows access to Loan Application Unsecured
        assign-roles:
          - client-name: service-loan-product-catalog-bridge
            role-name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
          - client-name: service-loan-documents-bridge
            role-name: ACCESS-LOAN-DOCUMENTS-BRIDGE
          - client-name: service-aps-modeling
            role-name: APS-GET-MODELING
          - client-name: service-loan-core-bridge
            role-name: ACCESS-GET-MAIN-ACCOUNT
          - client-name: service-loan-pfs-bridge
            role-name: ACCESS-LOAN-PFS-BRIDGE
          - client-name: service-loan-de-bridge
            role-name: ACCESS-LOAN-DE-BRIDGE
          - client-name: service-loan-codelists-bridge
            role-name: ACCESS-LOAN-CODELISTS-BRIDGE
          - client-name: service-loan-notification-bridge
            role-name: ACCESS-LOAN-NOTIFICATION-BRIDGE
          - client-name: service-loan-sign-bridge
            role-name: ACCESS-LOAN-SIGN-BRIDGE
          - client-name: service-loan-whisper-bridge
            role-name: ACCESS-LOAN-WHISPER-BRIDGE
          - client-name: service-loan-account-registry
            role-name: ACCESS-LOAN-ACCOUNT-REGISTRY
          - client-name: service-loan-business-codegen
            role-name: ACCESS-LOAN-BUSINESS-CODEGEN
      - name: 'service-loan-application-secured'
        description: Service client for loan-application-secured service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-APPLICATION-SECURED
            description: Allows access to Loan Application Secured
        assign-roles:
          - client-name: service-loan-modeling-secured
            role-name: ACCESS-LOAN-MODELING-SECURED
          - client-name: service-loan-product-catalog-bridge
            role-name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
          - client-name: service-loan-documents-bridge
            role-name: ACCESS-LOAN-DOCUMENTS-BRIDGE
          - client-name: service-aps-modeling
            role-name: APS-GET-MODELING
          - client-name: service-loan-core-bridge
            role-name: ACCESS-GET-MAIN-ACCOUNT
          - client-name: service-loan-pfs-bridge
            role-name: ACCESS-LOAN-PFS-BRIDGE
          - client-name: service-loan-de-bridge
            role-name: ACCESS-LOAN-DE-BRIDGE
          - client-name: service-loan-codelists-bridge
            role-name: ACCESS-LOAN-CODELISTS-BRIDGE
          - client-name: service-loan-notification-bridge
            role-name: ACCESS-LOAN-NOTIFICATION-BRIDGE
          - client-name: service-loan-sign-bridge
            role-name: ACCESS-LOAN-SIGN-BRIDGE
          - client-name: service-loan-whisper-bridge
            role-name: ACCESS-LOAN-WHISPER-BRIDGE
          - client-name: service-loan-account-registry
            role-name: ACCESS-LOAN-ACCOUNT-REGISTRY
          - client-name: service-loan-business-codegen
            role-name: ACCESS-LOAN-BUSINESS-CODEGEN
          - client-name: service-loan-knbox-bridge
            role-name: ACCESS-LOAN-KNBOX-BRIDGE
      - name: 'service-loan-modeling-secured'
        description: Service client for loan-modeling-secured service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-MODELING-SECURED
            description: Allows access to Loan Application Secured
        assign-roles:
          - client-name: service-loan-product-catalog-bridge
            role-name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
          - client-name: service-loan-pfs-bridge
            role-name: ACCESS-LOAN-PFS-BRIDGE
          - client-name: service-loan-de-bridge
            role-name: ACCESS-LOAN-DE-BRIDGE
          - client-name: service-loan-codelists-bridge
            role-name: ACCESS-LOAN-CODELISTS-BRIDGE
          - client-name: service-loan-application-secured
            role-name: ACCESS-LOAN-APPLICATION-SECURED
      - name: 'service-loan-contracting'
        description: Service client for loan-contracting service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-CONTRACTING
            description: Allows access to Loan Contracting
        assign-roles:
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
          - client-name: service-loan-pfs-bridge
            role-name: ACCESS-LOAN-PFS-BRIDGE
          - client-name: service-loan-product-catalog-bridge
            role-name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
          - client-name: service-loan-codelists-bridge
            role-name: ACCESS-LOAN-CODELISTS-BRIDGE
          - client-name: service-loan-account-registry
            role-name: ACCESS-LOAN-ACCOUNT-REGISTRY
          - client-name: service-loan-notification-bridge
            role-name: ACCESS-LOAN-NOTIFICATION-BRIDGE
          - client-name: service-loan-documents-bridge
            role-name: ACCESS-LOAN-DOCUMENTS-BRIDGE
          - client-name: service-loan-core-bridge
            role-name: ACCESS-GET-MAIN-ACCOUNT
      - name: 'service-loan-account-registry'
        description: Service client for loan-account-registry service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-ACCOUNT-REGISTRY
            description: Allows access to Loan Account Registry
        assign-roles:
          - client-name: service-loan-application-unsecured
            role-name: ACCESS-LOAN-APPLICATION-UNSECURED
          - client-name: service-loan-application-secured
            role-name: ACCESS-LOAN-APPLICATION-SECURED
          - client-name: service-loan-contracting
            role-name: ACCESS-LOAN-CONTRACTING
          - client-name: service-loan-pfs-bridge
            role-name: ACCESS-LOAN-PFS-BRIDGE
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
          - client-name: service-loan-de-bridge
            role-name: ACCESS-LOAN-DE-BRIDGE
          - client-name: service-loan-product-catalog-bridge
            role-name: ACCESS-LOAN-PRODUCT-CATALOG-BRIDGE
          - client-name: service-loan-notification-bridge
            role-name: ACCESS-LOAN-NOTIFICATION-BRIDGE
          - client-name: service-loan-documents-bridge
            role-name: ACCESS-LOAN-DOCUMENTS-BRIDGE
          - client-name: service-pfs-advisor-definition
            role-name: ACCESS-PFS-ADVISOR-DEFINITION
          - client-name: service-keycloak-user-manager
            role-name: MANAGE-USER-ROLE_PID_WORLD-LOAN
          - client-name: service-loan-codelists-bridge
            role-name: ACCESS-LOAN-CODELISTS-BRIDGE
          - client-name: service-bank-jira-adapter
            role-name: JIRA-PROJECT-UIS
      - name: 'service-loan-task-queue'
        description: Service client for loan-task-queue service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-TASK-QUEUE
            description: Allows access to Loan Task Queue
        assign-roles:
          - client-name: service-loan-account-registry
            role-name: ACCESS-LOAN-ACCOUNT-REGISTRY
          - client-name: service-loan-pid-bridge
            role-name: ACCESS-LOAN-PID-BRIDGE
      - name: 'service-loan-business-codegen'
        description: Service client for loan-business-codegen service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-LOAN-BUSINESS-CODEGEN
            description: Allows access to Loan Business Code Generator
      - name: 'service-nebanka-backoffice-be'
        description: Service client for nebanka-backoffice-be service
        service-accounts-enabled: true
      - name: 'service-pid-gateway'
        description: Service client for pid-gateway service
        service-accounts-enabled: true
        roles:
          - name: ROLE_EXTERNAL_AML_PROVIDER
            description: Allows to send AML check results
          - name: ROLE_PFS_CONTRACT_SHARING
            description: Allows to read and modify PFS contract sharing relationships
          - name: CONTRACT_SIGNATURE_ANTIVIRUS_CHECK
            description: Allows to upload / download pdf documents to pid-upload for antivirus check
          - name: ROLE_PFS_PORTFOLIO
            description: Allows to send and cancel portfolio and document update notifications
          - name: PFS_ADVISOR_SELECTION
            description: Allows to reset advisor selected for given PID
          - name: ROLE_PID_DATA_READ
            description: Allows to read data from PID
          - name: ROLE_PID_DATA_WRITE
            description: Allows to write data to PID
          - name: ROLE_PID_DEVICE_ACTIVATION
            description: Allows to activate a device
          - name: ROLE_PID_DEVICE_GET
            description: Allows to read device status
          - name: ROLE_PID_OCR_SCAN
            description: Allows to operate with OCR scanning
          - name: ROLE_AIS_DATA_READ
            description: Allows to read data from AIS (Agendovy Informacni System)
          - name: BANK_PRODUCT_COMPARISON
            description: Allows to compare bank products
          - name: SOURCE_CLIENT_BANK
            description: Represents a client from Bank
          - name: SOURCE_CLIENT_BANK_EMPLOYEE
            description: Represents a client from Bank Employee
          - name: SOURCE_CLIENT_GEPARD
            description: Represents a client from Gepard Finance
          - name: SOURCE_CLIENT_LOAN
            description: Represents a client from Loans
          - name: SOURCE_CLIENT_BANK_TEST
            description: Represents a client from Bank (test purpose)
          - name: SOURCE_CLIENT_NONBANK
            description: Represents a client from "Not a Bank"
          - name: SOURCE_CLIENT_OCP
            description: Represents a client from Shares trading (Obchod s Cennymi Papiry)
          - name: SOURCE_CLIENT_PFS
            description: Represents a client from PFS
          - name: SOURCE_CLIENT_PFS_CONSULTANT
            description: Represents a client from PFS Consultant
          - name: SOURCE_CLIENT_PID
            description: Represents a client from PID
          - name: SOURCE_CLIENT_PID_TEST
            description: Represents a client from PID (test purpose)
          - name: SOURCE_CLIENT_RENTEA
            description: Represents a client from Rentea (Retirement Savings)
          - name: SOURCE_CLIENT_SFS
            description: Represents a client from SFS (Simplea Financial Services - SK)
          - name: SOURCE_CLIENT_SIMPLEA
            description: Represents a client from Simplea (Insurance)
          - name: SOURCE_CLIENT_SIMPLEA_SK
            description: Represents a client from Simplea (Insurance - SK)
          - name: SOURCE_CLIENT_VUB
            description: Represents a client from VUB Bank
          - name: SOURCE_CLIENT_ORFEUS
            description: Represents a client from ORFEUS
          - name: SOURCE_CLIENT_ORFEUS_SK
            description: Represents a client from ORFEUS (SK)
      - name: 'service-bank-standing-order'
        description: |
          Service client for bank-standing-order
        service-accounts-enabled: true
        roles:
          - name: BANK_CLIENT_STANDING_ORDER_WRITE
            description: Allows to standing orders for fip
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-treasury-service'
        description: |
          Service client for bank-treasury-service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
        roles:
          - name: ODOO_DATA_READ
            description: A role representing Odoo read
          - name: ODOO_DATA_WRITE
            description: A role representing Odoo write
      - name: 'service-notification-gateway'
        description: Service client for notification-gateway service.
        service-accounts-enabled: true
        roles:
          - name: DAKTELA-ACTIVITY-WRITE
            description: A role representing Daktela (external system) user allowed to notify about activity.
          - name: DAKTELA-CALL-WRITE
            description: A role representing Daktela (external system) user allowed to verify calls.
          - name: DAKTELA-CHAT-WRITE
            description: A role representing Daktela (external system) user allowed to notify about chat message.
          - name: PID-DAKTELA-CONTACT-WRITE
            description: A role representing Daktela (external system) user allowed to notify about new recording of calls.
          - name: DAKTELA-RECORDING-WRITE
            description: A role representing PID service allowed to change customer data in Exponea.
          - name: EXPONEA-NOTIFICATIONS-WRITE
            description: A role representing Exponea (external system) user allowed to send push notifications.
          - name: EXPONEA-CALL-CAMPAIGNS-WRITE
            description: A role representing Exponea (external system) user allowed to manage call campaigns.
          - name: EXPONEA-SMS-WRITE
            description: A role representing Exponea (external system) user allowed to send SMS.
          - name: PID-EXPONEA-CUSTOMER-WRITE
            description: A role representing PID service allowed to change customer data in Exponea.
      - name: 'component-daktela'
        description: Client for Daktela (external system – call centre).
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-gateway
            role-name: DAKTELA-ACTIVITY-WRITE
          - client-name: service-notification-gateway
            role-name: DAKTELA-CALL-WRITE
          - client-name: service-notification-gateway
            role-name: DAKTELA-CHAT-WRITE
          - client-name: service-notification-gateway
            role-name: DAKTELA-RECORDING-WRITE
      - name: 'component-exponea'
        description: Client for Exponea (external system – CRM).
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-gateway
            role-name: EXPONEA-NOTIFICATIONS-WRITE
          - client-name: service-notification-gateway
            role-name: EXPONEA-CALL-CAMPAIGNS-WRITE
          - client-name: service-notification-gateway
            role-name: EXPONEA-SMS-WRITE
          - client-name: service-pid-identity-blocking
            role-name: ACCESS-BLOCK-PID-IDENTITY
          - client-name: service-pid-identity-blocking
            role-name: ACCESS_PID_IDENTITY_BLOCKING
          - client-name: service-loan-pfs-bridge
            role-name: ACCESS-GET-GEPARD-USER
      - name: 'service-notification-daktela-bridge'
        description: |
          Service client for notification-daktela-bridge service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-notification-exponea-bridge'
        description: |
          Service client for notification-exponea-bridge service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID
      - name: 'service-notification-push-service'
        description: |
          Service client for notification-push-service service
        service-accounts-enabled: true
        roles:
          - name: NOTIFICATIONS-WRITE
            description: Allows to create notifications
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_ACTIVATION
      - name: 'service-notification-smsgate-tmobile'
        description: |
          Service client for notification-smsgate-tmobile service
        service-accounts-enabled: true
        roles:
          - name: SMS-TMOBILE-SEND
            description: Allows to send SMS via T-Mobile gateway
      - name: 'service-bank-mobility'
        description: |
          Service client for bank-mobility service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-fip-onboarding-gateway'
        description: |
          Service client for bank-fip-onboarding-gateway service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
      - name: 'service-bank-challenges'
        description: |
          Service client for bank-challenges service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-fip-onboarding'
        description: |
          Service client for bank-fip-onboarding service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DEVICE_GET
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-cooperation-service'
        description: |
          Service client for bank-cooperation-service service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
      - name: 'service-bank-card-management'
        description: |
          Service client for bank-card-management service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
      - name: 'service-bank-card-savings'
        description: |
          Service client for bank-card-savings service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-click-to-pay'
        description: |
          Service client for bank-click-to-pay service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'component-aml-services'
        description: |
          Service client for AML Services
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_EXTERNAL_AML_PROVIDER
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID
      - name: 'service-bank-campaign'
        description: |
          Service client for bank-campaign service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-jira-adapter'
        description: |
          Service client for bank-jira-adapter service
        service-accounts-enabled: true
        roles:
          - name: JIRA-PROJECT-UIS
            description: Allows to alter UIS project
          - name: JIRA-PROJECT-BLK
            description: Allows to alter BLK project
      - name: 'service-apigw-operator-synchronous-api'
        description: |
          Service client for apigw-operator-synchronous-api
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-idp-apim-orchestrator
            role-name: SET-API-SUBSCRIPTIONS
      - name: 'service-bank-backoffice-gateway'
        description: |
          Service client for bank-backoffice-gateway service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_PID
      - name: 'service-bank-dashboard'
        description: |
          Service client for bank-dashboard service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
      - name: 'service-wso2'
        description: |
          Service client for wso2 apim
        service-accounts-enabled: true
        roles:
          - name: ROLE_WSO2
            description: Allows to access APIs behind WSO2 GW
        assign-roles:
          - client-name: service-wso2
            role-name: ROLE_WSO2
      - name: 'service-bank-account-blocking'
        description: |
          Service client for bank-account-blocking service
        service-accounts-enabled: true
        roles:
          - name: ACCESS_BANK_ACCOUNT_BLOCKING
            description: Allows to block client account
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
          - client-name: service-bank-jira-adapter
            role-name: JIRA-PROJECT-BLK
      - name: 'department-wpb-client'
        description: |
          The department client for Web Partners Bank
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-account-blocking
            role-name: ACCESS_BANK_ACCOUNT_BLOCKING
      - name: 'service-bank-account-termination'
        description: |
          Service client for bank-account-termination service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-daily-auth-manager
            role-name: MANAGE-USER-ROLE_DAILY
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_WRITE
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
          - client-name: service-notification-push-service
            role-name: NOTIFICATIONS-WRITE
      - name: 'service-bank-foreign-payment'
        description: |
          Service client for bank-foreign-payment service
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-pid-gateway
            role-name: ROLE_PID_DATA_READ
          - client-name: service-pid-gateway
            role-name: SOURCE_CLIENT_BANK
      - name: 'service-bank-template-renderer'
        description: |
          Service client for bank-template-renderer service
        service-accounts-enabled: true
        roles:
          - name: ACCESS-BANK-TEMPLATE-RENDERER
            description: Allow access to bank template renderer
      - name: 'service-bank-templating'
        description: |
          Service client for bank templating
        service-accounts-enabled: true
        assign-roles:
          - client-name: service-bank-template-renderer
            role-name: ACCESS-BANK-TEMPLATE-RENDERER
      - name: 'service-pid-document-signer'
        description: |
          Service client for PID document signer
        service-accounts-enabled: true
        roles:
          - name: ROLE_PID_DOCUMENT_SIGNER_WRITE
            description: Role for services which have access to perform write operations (signing, filling documents, etc.)
          - name: ROLE_PID_DOCUMENT_SIGNER_READ
            description: Role for services which have access to perform read operations (checking signature fields on document, etc.)
