FROM harbor-aws.internal.pbk-lab.tech/pid-baseimage/pid-baseimage:0.0.40

ARG version_arg=dev
WORKDIR /home/<USER>

COPY service/build/docker/main/layers/libs /home/<USER>/libs
COPY service/build/docker/main/layers/resources /home/<USER>/resources
COPY service/build/docker/main/layers/application.jar /home/<USER>/application.jar
COPY keycloak/configuration.yaml /home/<USER>/keycloak/configuration.yaml
COPY keycloak/authentication-flows.yaml /home/<USER>/keycloak/authentication-flows.yaml

ENV version=$version_arg
EXPOSE 8080

ENTRYPOINT ["/entrypoint.sh"]
CMD ["application"]
