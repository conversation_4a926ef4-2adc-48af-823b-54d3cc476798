openapi: 3.0.2
info:
  title: Keycloak Admin REST API
  description: This is a REST API reference for the Keycloak Admin
  version: '19.5'
  contact:
    name: <PERSON>
    url: https://partners.cz
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: https://todo
security:
  - access_token: [ ]
servers:
  - url: /admin/realms
paths:
  /:
    post:
      tags:
        - Realms Admin
      summary: Import a realm   Imports a realm from a full representation of that
        realm.
      requestBody:
        description: JSON representation of the realm
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RealmRepresentation'
        required: true
      responses:
        2XX:
          description: success

  /{realm}:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      tags:
        - Realms Admin
      summary: Get the top-level representation of the realm   It will not include
        nested information like User and Client representations.
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RealmRepresentation'
    put:
      tags:
        - Realms Admin
      summary: Update the top-level information of the realm   Any user, roles or
        client information in the representation  will be ignored.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RealmRepresentation'
        required: true
      responses:
        2XX:
          description: success

    delete:
      tags:
        - Realms Admin
      summary: Deletes the realm
      responses:
        2XX:
          description: success

  /{realm}/default-default-client-scopes/{clientScopeId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientScopeId"
    put:
      tags:
        - Realms Admin
      responses:
        2XX:
          description: success
  /{realm}/default-optional-client-scopes/{clientScopeId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientScopeId"
    put:
      tags:
        - Realms Admin
      responses:
        2XX:
          description: success
  /{realm}/users/profile:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      tags:
        - Users
      summary: Get the configuration for the user profile
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UPConfig'
    put:
      tags:
        - Users
      summary: Set the configuration for the user profile
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UPConfig'
        required: true
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UPConfig'

  /{realm}/client-scopes:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      tags:
        - Client Scopes
      summary: Get client scopes belonging to the realm   Returns a list of client
        scopes belonging to the realm
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ClientScopeRepresentation"
    post:
      tags:
        - Client Scopes
      summary: Create a new client scope   Client Scope’s name must be unique!
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientScopeRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/client-scopes/{clientScopeId}/protocol-mappers/models:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientScopeId"
    post:
      tags:
        - Protocol Mappers
      summary: Create a mapper
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolMapperRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/clients:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      tags:
        - Clients
      summary: Get clients belonging to the realm.
      parameters:
        - in: query
          name: clientId
          description: filter by clientId
          schema:
            type: string
          style: form
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ClientRepresentation"
    post:
      tags:
        - Clients
      summary: Create a new client   Client’s client_id must be unique!
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/clients/{clientId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientId"
    put:
      tags:
        - Clients
      summary: Update the client
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientRepresentation'
        required: true
      responses:
        2XX:
          description: success
    delete:
      tags:
        - Clients
      summary: Deletes the specified client
      responses:
        2XX:
          description: success
  /{realm}/clients/{clientId}/service-account-user:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientId"
    get:
      tags:
        - Clients
      summary: Get a user dedicated to the service account
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRepresentation'
  /{realm}/clients/{clientId}/default-client-scopes:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientId"
    get:
      tags:
        - Clients
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientScopeMappingRepresentation'
  /{realm}/clients/{clientId}/default-client-scopes/{clientScopeId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientId"
      - $ref: "#/components/parameters/ClientScopeId"
    put:
      tags:
        - Clients
      responses:
        2XX:
          description: success
    delete:
      tags:
        - Clients
      responses:
        2XX:
          description: success
  /{realm}/clients/{clientId}/optional-client-scopes:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientId"
    get:
      tags:
        - Clients
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientScopeMappingRepresentation'
  /{realm}/clients/{clientId}/optional-client-scopes/{clientScopeId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientId"
      - $ref: "#/components/parameters/ClientScopeId"
    put:
      tags:
        - Clients
      responses:
        2XX:
          description: success
    delete:
      tags:
        - Clients
      responses:
        2XX:
          description: success
  /{realm}/users/{userId}/role-mappings/realm:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/UserId"
    get:
      tags:
        - Role Mapper
      summary: Get client-level role mappings for the user, and the app
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RoleRepresentation"
    post:
      tags:
        - Role Mapper
      summary: Add client-level roles to the user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
        required: true
      responses:
        2XX:
          description: success
    delete:
      tags:
        - Role Mapper
      summary: Delete client-level roles from user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/users/{userId}/role-mappings/clients/{clientId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/UserId"
      - $ref: "#/components/parameters/ClientId"
    get:
      tags:
        - Client Role Mappings
      summary: Get client-level role mappings for the user, and the app
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/RoleRepresentation"
    post:
      tags:
        - Client Role Mappings
      summary: Add client-level roles to the user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
        required: true
      responses:
        2XX:
          description: success
    delete:
      tags:
        - Client Role Mappings
      summary: Delete client-level roles from user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/roles:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      tags:
        - Roles
      summary: Get all roles for the realm
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
  /{realm}/clients/{clientId}/roles:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientId"
    get:
      tags:
        - Roles
      summary: Get all roles for the realm or client
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
        - Roles
      summary: Create a new role for the realm or client
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/clients/{clientId}/roles/{roleName}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ClientId"
      - $ref: "#/components/parameters/RoleName"
    delete:
      tags:
        - Roles
      summary: Delete a role by name
      responses:
        2XX:
          description: success
  /{realm}/users/{userId}/role-mappings:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/UserId"
    get:
      tags:
        - Role Mapper
      summary: Get role mappings
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MappingsRepresentation'
  /{realm}/users:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      tags:
        - Users
      summary: Get users   Returns a stream of users, filtered according to query
        parameters
      parameters:
        - in: query
          name: q
          description: A String contained in user custom attributes
          schema:
            type: string
          style: form
        - in: query
          name: exact
          description: Boolean which defines whether the params "last", "first", "email"
            and "username" must match exactly
          schema:
            type: boolean
          style: form
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRepresentation'
    post:
      tags:
        - Users
      summary: Create a new user   Username must be unique.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/groups:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      tags:
        - Groups
      summary: Get group hierarchy.
      parameters:
        - in: query
          name: search
          schema:
            type: string
          style: form
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/GroupRepresentation"
    post:
      tags:
        - Groups
      summary: create or add a top level realm groupSet or create child.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/groups/{groupId}/role-mappings/clients/{clientId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/GroupId"
      - $ref: "#/components/parameters/ClientId"
    post:
      tags:
        - Client Role Mappings
      summary: Add client-level roles to the user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
        required: true
      responses:
        2XX:
          description: success
    delete:
      tags:
        - Client Role Mappings
      summary: Delete client-level roles from user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/users/{userId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/UserId"
    delete:
      tags:
        - Users
      responses:
        2XX:
          description: success
    put:
      tags:
        - Users
      summary: Update the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/users/{userId}/groups/{groupId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/UserId"
      - $ref: "#/components/parameters/GroupId"
    put:
      tags:
        - Users
      responses:
        2XX:
          description: success
    delete:
      tags:
        - Users
      responses:
        2XX:
          description: success
  /{realm}/users/{userId}/reset-password:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/UserId"
    put:
      tags:
        - Users
      summary: Set up a new password for the user.
      requestBody:
        description: The representation must contain a rawPassword with the plain-text
          password
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CredentialRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/roles-by-id/{roleId}/composites:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/RoleId"
    post:
      tags:
        - Roles
      summary: Make the role a composite role by associating some child roles
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/authentication/authenticator-providers:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      operationId: getAuthenticatorProviders
      tags:
        - Providers
      summary: Returns a list of all available authenticator providers
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AuthenticationProviderRepresentation"
  /{realm}/authentication/flows:
    parameters:
      - $ref: "#/components/parameters/Realm"
    get:
      operationId: getAuthenticationFlows
      tags:
        - Authentication flow
      summary: Returns a list of all authentication flows within the realm
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AuthenticationFlowRepresentation"
    post:
      operationId: createAuthenticationFlow
      tags:
        - Authentication flow
      summary: Creates a new authentication flow
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticationFlowRepresentation'
        required: true
      responses:
        2XX:
          description: success
        4XX:
          description: 409 An authentication flow with the specified alias already exists
  /{realm}/authentication/flows/{flowId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/FlowId"
    get:
      operationId: getAuthenticationFlow
      tags:
        - Authentication flow
      summary: Returns a detail of a single authentication flow
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuthenticationFlowRepresentation"
  /{realm}/authentication/flows/{flowAlias}/executions:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/FlowAlias"
    get:
      operationId: getExecutionsForAuthenticationFlow
      tags:
        - Authentication flow
      summary: Returns a list of executions for the specified authentication flow
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/AuthenticationExecutionInfoRepresentation"
    put:
      operationId: editAuthenticationFlowExecution
      tags:
        - Authentication flow
      summary: Edits an authentication flow execution specified by ID in the payload
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AuthenticationExecutionInfoRepresentation"
      responses:
        2XX:
          description: success

  /{realm}/authentication/flows/{flowAlias}/executions/flow:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/FlowAlias"
    post:
      operationId: createAuthenticationFlowSubflow
      tags:
        - Authentication flow
      summary: Creates a new authentication flow subflow
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAuthenticationFlowSubflow'
        required: true
      responses:
        2XX:
          description: success
        4XX:
          description: 409 An authentication flow execution with the specified alias already exists
  /{realm}/authentication/executions:
    parameters:
      - $ref: "#/components/parameters/Realm"
    post:
      operationId: createAuthenticationFlowExecution
      tags:
        - Authentication flow
      summary: Creates a new authentication flow execution
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAuthenticationFlowExecution'
        required: true
      responses:
        2XX:
          description: success
        4XX:
          description: 409 An authentication flow execution with the specified alias already exists
  /{realm}/authentication/executions/{executionId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ExecutionId"
    delete:
      operationId: deleteAuthenticationFlowExecution
      tags:
        - Authentication flow
      summary: Deletes the authentication flow execution with the specified ID
      responses:
        2XX:
          description: success
  /{realm}/authentication/executions/{executionId}/config:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ExecutionId"
    post:
      operationId: upsertAuthenticationFlowExecutionConfig
      tags:
        - Authentication flow
      summary: Creates or updates a config for the specified authentication flow execution
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAuthenticationFlowExecutionConfig'
        required: true
      responses:
        2XX:
          description: success
  /{realm}/authentication/config/{configId}:
    parameters:
      - $ref: "#/components/parameters/Realm"
      - $ref: "#/components/parameters/ConfigId"
    delete:
      operationId: deleteAuthenticationFlowExecutionConfig
      tags:
        - Authentication flow
      summary: Deletes the authentication flow execution config with the specified ID
      responses:
        2XX:
          description: success
components:
  securitySchemes:
    access_token:
      type: http
      scheme: bearer
      bearerFormat: 'JWT'
  parameters:
    Realm:
      in: path
      name: realm
      description: realm name (not id!)
      required: true
      schema:
        type: string
      style: simple
    ClientScopeId:
      in: path
      name: clientScopeId
      description: id of client scope (not name)
      required: true
      schema:
        type: string
      style: simple
    ClientId:
      in: path
      name: clientId
      description: id of client (not client-id)
      required: true
      schema:
        type: string
      style: simple
    GroupId:
      in: path
      name: groupId
      description: id of group (not client-id)
      required: true
      schema:
        type: string
      style: simple
    UserId:
      in: path
      name: userId
      description: User id
      required: true
      schema:
        type: string
      style: simple
    RoleId:
      in: path
      name: roleId
      description: Role id
      required: true
      schema:
        type: string
      style: simple
    RoleName:
      in: path
      name: roleName
      description: Role name (not ID)
      required: true
      schema:
        type: string
      style: simple
    FlowId:
      in: path
      name: flowId
      description: Authentication flow ID
      required: true
      schema:
        type: string
        format: uuid
      style: simple
    FlowAlias:
      in: path
      name: flowAlias
      description: Authentication flow alias
      required: true
      schema:
        type: string
      style: simple
    ExecutionId:
      in: path
      name: executionId
      description: Authentication flow execution ID
      required: true
      schema:
        type: string
        format: uuid
      style: simple
    ConfigId:
      in: path
      name: configId
      description: Authentication flow execution config ID
      required: true
      schema:
        type: string
        format: uuid
      style: simple
  schemas:
    CredentialRepresentation:
      type: object
      properties:
        temporary:
          type: boolean
        type:
          type: string
          enum:
            - password
        value:
          type: string
    MappingsRepresentation:
      type: object
      properties:
        clientMappings:
          type: object
          additionalProperties: true
        realmMappings:
          type: array
          items:
            $ref: '#/components/schemas/RoleRepresentation'
    ClientMappingsRepresentation:
      type: object
      required:
        - id
        - client
        - mappings
      properties:
        client:
          type: string
        id:
          type: string
        mappings:
          type: array
          items:
            $ref: '#/components/schemas/RoleRepresentation'
    GroupRepresentation:
      type: object
      required:
        - name
      properties:
        id:
          type: string
        name:
          type: string
    RoleRepresentation:
      type: object
      required:
        - name
      properties:
        description:
          type: string
        id:
          type: string
        name:
          type: string
    UserRepresentation:
      type: object
      required:
        - username
      properties:
        id:
          type: string
        username:
          type: string
        enabled:
          type: boolean
        attributes:
          type: object
          additionalProperties: true
    ClientRepresentation:
      type: object
      required:
        - publicClient
        - clientId
        - directAccessGrantsEnabled
        - enabled
        - serviceAccountsEnabled
        - standardFlowEnabled
        - implicitFlowEnabled
      properties:
        id:
          type: string
        clientId:
          type: string
        name:
          type: string
        description:
          type: string
        publicClient:
          type: boolean
        directAccessGrantsEnabled:
          type: boolean
        enabled:
          type: boolean
        secret:
          type: string
        authorizationServicesEnabled:
          type: boolean
        serviceAccountsEnabled:
          type: boolean
        standardFlowEnabled:
          type: boolean
        implicitFlowEnabled:
          type: boolean
        protocol:
          $ref: "#/components/schemas/Protocol"
        clientAuthenticatorType:
          type: string
        rootUrl:
          type: string
        baseUrl:
          type: string
        redirectUris:
          type: array
          items:
            type: string
        webOrigins:
          type: array
          items:
            type: string
        adminUrl:
          type: string
        frontchannelLogout:
          type: boolean
        authenticationFlowBindingOverrides:
          $ref: "#/components/schemas/AuthenticationFlowOverride"
        defaultClientScopes:
          type: array
          items:
            type: string
        optionalClientScopes:
          type: array
          items:
            type: string
        attributes:
          type: object
          additionalProperties:
            type: string
    RealmRepresentation:
      type: object
      required:
        - realm
        - enabled
      properties:
        realm:
          type: string
        enabled:
          type: boolean
        accessTokenLifespan:
          description: Max time in seconds before an access token is expired.
          type: integer
          format: int32
        ssoSessionIdleTimeout:
          description: Time in seconds a session is allowed to be idle before it expires.
          type: integer
          format: int32
        ssoSessionMaxLifespan:
          description: Max time in seconds before a session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session.
          type: integer
          format: int32
        clientSessionIdleTimeout:
          description: Time in seconds a client session is allowed to be idle before it expires.
          type: integer
          format: int32
        clientSessionMaxLifespan:
          description: Max time in seconds before a client session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session.
          type: integer
          format: int32
        offlineSessionIdleTimeout:
          description: Time in seconds an offline session is allowed to be idle before it expires. Expiration of a single offline token.
          type: integer
          format: int32
        offlineSessionMaxLifespanEnabled:
          description: Enable offline session max.
          type: boolean
        offlineSessionMaxLifespan:
          description: Max time in seconds before an offline session is expired regardless of activity. Newly generated offline tokens cannot exceed overall lifespan of the session. Only relevant if offlineSessionMaxLifespanEnabled is true.
          type: integer
          format: int32
        attributes:
          type: object
          properties:
            frontendUrl:
              type: string
    ClientScopeRepresentation:
      type: object
      required:
        - attributes
        - name
        - protocol
      properties:
        attributes:
          $ref: "#/components/schemas/ClientScopeRepresentationAttributes"
        id:
          type: string
        name:
          type: string
        description:
          type: string
        protocol:
          type: string
    ClientScopeMappingRepresentation:
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: string
        name:
          type: string
    ClientScopeRepresentationAttributes:
      type: object
      properties:
        consent.screen.text:
          type: string
        display.on.consent.screen:
          type: boolean
        include.in.token.scope:
          type: boolean
        gui.order:
          type: string
    ProtocolMapperRepresentation:
      type: object
      required:
        - name
        - protocol
        - protocolMapper
        - config
      properties:
        config:
          $ref: "#/components/schemas/ProtocolMapperConfig"
        id:
          type: string
        name:
          type: string
        protocol:
          $ref: "#/components/schemas/Protocol"
        protocolMapper:
          type: string
          enum:
            - oidc-usermodel-attribute-mapper
            - oidc-usersessionmodel-note-mapper
            - nonce-id-token-mapper
            - oidc-nonce-backwards-compatible-mapper
            - saml-user-session-note-mapper
            - custom-subject-saml-mapper
    ProtocolMapperConfig:
      type: object
      description: Configuration specific for every type of protocol mapper
      oneOf:
        - $ref: '#/components/schemas/UserAttributeMapperConfig'
        - $ref: '#/components/schemas/UserSessionNoteMapperConfig'
        - $ref: '#/components/schemas/NonceIdTokenMapperConfig'
        - $ref: '#/components/schemas/NonceBackwardsCompatibleMapperConfig'
      required:
        - discriminatorType
      properties:
        discriminatorType:
          $ref: "#/components/schemas/ProtocolMapperConfigDiscriminatorType"
      discriminator:
        # technical property allowing polymorphic API
        # its serialization is OK as long as it is unique enough and does not cause collisions with Keycloak attribute names
        propertyName: discriminatorType
        mapping:
          USER_ATTRIBUTE_MAPPER: '#/components/schemas/UserAttributeMapperConfig'
          USER_SESSION_NOTE_MAPPER: '#/components/schemas/UserSessionNoteMapperConfig'
          NONCE_ID_MAPPER: '#/components/schemas/NonceIdTokenMapperConfig'
          NONCE_BACKWARDS_COMPATIBLE_MAPPER: '#/components/schemas/NonceBackwardsCompatibleMapperConfig'
          SAML_USER_SESSION_NOTE_MAPPER: '#/components/schemas/SamlUserSessionNoteMapperConfig'
          CUSTOM_SUBJECT_SAML_MAPPER: '#/components/schemas/CustomSubjectSamlMapperConfig'
    ProtocolMapperConfigDiscriminatorType:
      type: string
      description: Technical type for discriminator/polymorphism purposes purposes
      enum:
        - USER_ATTRIBUTE_MAPPER
        - USER_SESSION_NOTE_MAPPER
        - NONCE_ID_MAPPER
        - NONCE_BACKWARDS_COMPATIBLE_MAPPER
        - SAML_USER_SESSION_NOTE_MAPPER
        - CUSTOM_SUBJECT_SAML_MAPPER
    UserAttributeMapperConfig:
      allOf:
        - $ref: "#/components/schemas/ProtocolMapperConfig"
        - type: object
          required:
            - access.token.claim
            - aggregate.attrs
            - claim.name
            - id.token.claim
            - jsonType.label
            - multivalued
            - user.attribute
            - userinfo.token.claim
          properties:
            access.token.claim:
              type: boolean
            aggregate.attrs:
              type: boolean
            claim.name:
              type: string
            id.token.claim:
              type: boolean
            jsonType.label:
              type: string
              enum:
                - ''
                - JSON
            multivalued:
              type: boolean
            user.attribute:
              type: string
            userinfo.token.claim:
              type: boolean
    UserSessionNoteMapperConfig:
      allOf:
        - $ref: "#/components/schemas/ProtocolMapperConfig"
        - type: object
          required:
            - user.session.note
            - claim.name
            - jsonType.label
            - access.token.claim
            - id.token.claim
            - lightweight.claim
            - introspection.token.claim
            - userinfo.token.claim
            - access.tokenResponse.claim
          properties:
            user.session.note:
              type: string
            claim.name:
              type: string
            jsonType.label:
              type: string
              enum:
                - ''
                - JSON
            access.token.claim:
              type: boolean
            id.token.claim:
              type: boolean
            lightweight.claim:
              type: boolean
            introspection.token.claim:
              type: boolean
            userinfo.token.claim:
              type: boolean
            access.tokenResponse.claim:
              type: boolean
    NonceIdTokenMapperConfig:
      allOf:
        - $ref: "#/components/schemas/ProtocolMapperConfig"
        - type: object
    NonceBackwardsCompatibleMapperConfig:
      allOf:
        - $ref: "#/components/schemas/ProtocolMapperConfig"
        - type: object
    SamlUserSessionNoteMapperConfig:
      allOf:
        - $ref: "#/components/schemas/ProtocolMapperConfig"
        - type: object
          required:
            - attribute.nameformat
          properties:
            note:
              description: User Session Note Attribute
              type: string
            attribute.nameformat:
              $ref: "#/components/schemas/SamlAttributeNameFormat"
            attribute.name:
              description: SAML Attribute Name
              type: string
            friendly.name:
              description: Friendly Name
              type: string
    SamlAttributeNameFormat:
      type: string
      description: SAML Attribute NameFormat. Can be basic, URI reference, or unspecified.
      enum:
        - URI Reference
        - Basic
        - Unspecified
    CustomSubjectSamlMapperConfig:
      allOf:
        - $ref: "#/components/schemas/ProtocolMapperConfig"
        - type: object
    Protocol:
      type: string
      enum:
        - saml
        - openid-connect
    SamlSignatureKeyName:
      type: string
      enum:
        - NONE
        - KEY_ID
        - CERT_SUBJECT
    AuthenticationFlowExecutionRequirement:
      type: string
      enum:
        - REQUIRED
        - ALTERNATIVE
        - CONDITIONAL
        - DISABLED
    AuthenticationExecutionExportRepresentation:
      type: object
      required:
        - authenticatorFlow
        - requirement
        - priority
      properties:
        id:
          type: string
          format: uuid
        authenticator:
          type: string
        authenticatorFlow:
          type: boolean
        authenticatorConfig:
          type: string
        authenticationConfig:
          type: string
          format: uuid
        requirement:
          $ref: "#/components/schemas/AuthenticationFlowExecutionRequirement"
        priority:
          type: integer
          format: int32
    AuthenticationExecutionInfoRepresentation:
      type: object
      required:
        - id
        - requirement
      properties:
        id:
          type: string
          format: uuid
        displayName:
          type: string
        description:
          type: string
        authenticationFlow:
          type: boolean
        providerId:
          type: string
        requirementChoices:
          type: array
          items:
            $ref: "#/components/schemas/AuthenticationFlowExecutionRequirement"
        requirement:
          $ref: "#/components/schemas/AuthenticationFlowExecutionRequirement"
        priority:
          type: integer
        level:
          type: integer
          format: int32
        flowId:
          type: string
          format: uuid
    CreateAuthenticationFlowExecution:
      type: object
      allOf:
        - $ref: "#/components/schemas/AuthenticationExecutionExportRepresentation"
      required:
        - parentFlow
      properties:
        parentFlow:
          type: string
          format: uuid
    CreateAuthenticationFlowSubflow:
      type: object
      required:
        - alias
        - type
      properties:
        alias:
          type: string
        description:
          type: string
        type:
          type: string
        priority:
          type: integer
    AuthenticationProviderRepresentation:
      type: object
      required:
        - id
        - displayName
      properties:
        id:
          type: string
        displayName:
          type: string
        description:
          type: string
    AuthenticationFlowRepresentation:
      type: object
      required:
        - alias
        - providerId
      properties:
        id:
          type: string
          format: uuid
        alias:
          type: string
        description:
          type: string
        providerId:
          type: string
        topLevel:
          type: boolean
        builtIn:
          type: boolean
        authenticationExecutions:
          type: array
          items:
            $ref: "#/components/schemas/AuthenticationExecutionExportRepresentation"
    AuthenticationFlowOverride:
      type: object
      properties:
        direct_grant:
          type: string
          format: uuid
        browser:
          type: string
          format: uuid
    UpdateAuthenticationFlowExecutionConfig:
      type: object
      required:
        - alias
        - config
      properties:
        alias:
          type: string
        config:
          type: object
          additionalProperties: true
    UPConfig:
      type: object
      properties:
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/UPAttribute'
        groups:
          type: array
          items:
            $ref: '#/components/schemas/UPGroup'
        unmanagedAttributePolicy:
          $ref: '#/components/schemas/UnmanagedAttributePolicy'
    UPAttribute:
      type: object
      properties:
        name:
          type: string
        displayName:
          type: string
        validations:
          type: object
          additionalProperties:
            type: object
            additionalProperties: {}
        annotations:
          type: object
          additionalProperties: {}
        required:
          $ref: '#/components/schemas/UPAttributeRequired'
        permissions:
          $ref: '#/components/schemas/UPAttributePermissions'
        selector:
          $ref: '#/components/schemas/UPAttributeSelector'
        group:
          type: string
        multivalued:
          type: boolean
    UPAttributePermissions:
      type: object
      properties:
        view:
          uniqueItems: true
          type: array
          items:
            type: string
        edit:
          uniqueItems: true
          type: array
          items:
            type: string
    UPAttributeRequired:
      type: object
      properties:
        roles:
          uniqueItems: true
          type: array
          items:
            type: string
        scopes:
          uniqueItems: true
          type: array
          items:
            type: string
    UPAttributeSelector:
      type: object
      properties:
        scopes:
          uniqueItems: true
          type: array
          items:
            type: string
    UPGroup:
      type: object
      properties:
        name:
          type: string
        displayHeader:
          type: string
        displayDescription:
          type: string
        annotations:
          type: object
          additionalProperties: {}
    UnmanagedAttributePolicy:
      enum:
      - ENABLED
      - ADMIN_VIEW
      - ADMIN_EDIT
      type: string
