openapi: 3.0.2
info:
  title: Keycloak OpenID Connect API
  description: This is a custom specification, because non standardized exists
  version: '19.0'
paths:
  /realms/{realm}/protocol/openid-connect/token:
    post:
      tags:
        - openid connect
      operationId: getToken
      parameters:
        - name: realm
          in: path
          schema:
            type: string
          required: true
      requestBody:
        description: JSON representation of the realm
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/GetTokenRequest'
        required: true
      responses:
        2XX:
          description: success
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetTokenResponse"

components:
  schemas:
    GetTokenRequest:
      type: object
      properties:
        grant_type:
          type: string
          enum:
            - password
            - client_credentials
        client_id:
          type: string
        client_secret:
          type: string
        username:
          type: string
        password:
          type: string
        scope:
          type: string
      required:
        - grant_type
        - client_id
    GetTokenResponse:
      type: object
      properties:
        access_token:
          type: string
        expires_in:
          type: integer
        refresh_token:
          type: string
        refresh_expires_in:
          type: integer
        token_type:
          type: string
        id_token:
          type: string
        scope:
          type: string
        not-before-policy:
          type: integer
        session_state:
          type: string
      required:
        - access_token
        - expires_in
        - refresh_token
        - refresh_expires_in