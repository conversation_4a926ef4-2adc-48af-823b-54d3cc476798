import de.undercouch.gradle.tasks.download.Download
import java.nio.file.Files
import java.nio.file.Paths

val usePlatformCommon = (project.findProperty("platformCommonVersion") != null)
val platformCommonVersion = if (usePlatformCommon) project.findProperty("platformCommonVersion").toString() else null

val tokenResponse = when {
    project.findProperty("library")?.equals("micronaut-pbk-reactive") == true -> "TokenResponse=cz.pbktechnology.common.TokenResponse"
    usePlatformCommon -> "TokenResponse=cz.pbktechnology.platform.common.model.TokenResponse"
    else -> "TokenResponse=cz.partners.common.model.TokenResponse"
}
val userContext = when(usePlatformCommon){
    true -> "UserContext=cz.pbktechnology.platform.common.security.model.UserContext"
    else -> "UserContext=cz.partners.common.security.model.UserContext"
}
val defaultImportMappings = listOf(
    "AuditInfo=cz.partners.aps.common.model.AuditInfo",
    "ThreatMark=cz.partners.afs.common.model.ThreatMark",
    userContext,
    tokenResponse,
)
val artifactoryUrl = "https://artifactory.pbk-lab.tech"
val artifactoryUser = System.getenv("artifactory_user") ?: project.findProperty("artifactory_user").toString()
val artifactoryPassword = System.getenv("artifactory_password") ?: project.findProperty("artifactory_password").toString()
val legacySwagger = project.findProperty("legacySwagger")?.toString()
val inspectUserToken = project.findProperty("inspectUserToken")?.toString() ?: "true"
val generateSecurity = project.findProperty("generateSecurity")?.toString() ?: "true"
val useMicronautServicesToDefineClients = project.findProperty("useMicronautServicesToDefineClients")?.toString() ?: "false"
val separateClientAndServerDtos = project.findProperty("separateClientAndServerDtos")?.toString() ?: "false"
val typeMappings = project.findProperty("typeMappings")?.toString() ?: ""
val modelNameSuffix = project.findProperty("modelNameSuffix")?.toString() ?: ""
val importMappings = project.findProperty("importMappings")?.toString() ?: defaultImportMappings.joinToString(separator = ",")
val library = project.findProperty("library")?.toString() ?: "micronaut"
val useJakarta = project.findProperty("useJakarta")?.toString() ?: "false"
val generateOperationIdAnnotation = project.findProperty("generateOperationIdAnnotation")?.toString() ?: "false"
val operationIdHidden = project.findProperty("operationIdHidden")?.toString() ?: "true"
val arrow = project.findProperty("arrow")?.toString() ?: "true"
val arrowInClient = project.findProperty("arrowInClient")?.toString() ?: arrow
val responseHelperClass = project.findProperty("responseHelperClass")?.toString()
    ?: if (usePlatformCommon) "cz.pbktechnology.platform.common.helper.BaseResponseHelper" else "cz.partners.common.helper.BaseResponseHelper"
val enhancedErrorMapping = if ( platformCommonVersion != null && isLaterOrEqualSemver(platformCommonVersion, "2.0.0") ) "true"  else "false"
val implicitCorrelationId = if (platformCommonVersion != null && isLaterOrEqualSemver(platformCommonVersion, "2.0.0")) "true" else "false"
val validationsVersion = project.findProperty("validationsVersion")?.toString() ?: "1"
val clientEnableResponseGeneric = project.findProperty("clientEnableResponseGeneric")?.toString() ?: "false"
val clientAdditionalProperties = project.findProperty("clientAdditionalProperties")?.toString()
    ?: "serializationLibrary=jackson,enumPropertyNaming=UPPERCASE,sourceFolder=src,removeEnumValuePrefix=false,useMicronautServicesToDefineClients=$useMicronautServicesToDefineClients,separateClientAndServerDtos=$separateClientAndServerDtos,useJakarta=$useJakarta,arrow=$arrowInClient,usePlatformCommon=$usePlatformCommon,enhancedErrorMapping=$enhancedErrorMapping,validationsVersion=$validationsVersion,enableResponseGeneric=$clientEnableResponseGeneric"
val serverAdditionalProperties = project.findProperty("serverAdditionalProperties")?.toString()
    ?: "serializationLibrary=jackson,enumPropertyNaming=UPPERCASE,sourceFolder=src,removeEnumValuePrefix=false,inspectUserToken=$inspectUserToken,generateSecurity=$generateSecurity,separateClientAndServerDtos=$separateClientAndServerDtos,useJakarta=$useJakarta,generateOperationIdAnnotation=$generateOperationIdAnnotation,operationIdHidden=$operationIdHidden,responseHelperClass=$responseHelperClass,arrow=$arrow,usePlatformCommon=$usePlatformCommon,enhancedErrorMapping=$enhancedErrorMapping,implicitCorrelationId=$implicitCorrelationId,validationsVersion=$validationsVersion"
val modelAdditionalProperties = project.findProperty("modelAdditionalProperties")?.toString()
    ?: "serializationLibrary=jackson,enumPropertyNaming=UPPERCASE,sourceFolder=src,removeEnumValuePrefix=false,useJakarta=$useJakarta,usePlatformCommon=$usePlatformCommon"
val ignoreFile = "$rootDir" + (project.findProperty("ignoreFile")?.toString() ?: "/openapi/.openapi-generator-ignore")
val skipFormModel = project.findProperty("skipFormModel")?.toString() ?: "true"
val globalProperty = "skipFormModel=$skipFormModel"
val openapiCliVersion = project.findProperty("generatorVersion")?.toString() ?: "6.0.1"
val openapiCliPath = "../openapiCli"
var localCodegen = project.findProperty("localCodegen")?.toString() ?: "false"
val pbkCodegenVersion = "0.20.1"
var pbkCodegenJarPath =
    if (localCodegen == "true")
        "$rootDir/codegen/build/libs/pbk-codegen${if (version == "unspecified") "" else "-$version"}.jar"
    else "$buildDir/pbk-codegen.jar"
val pathSeparator = File.pathSeparator
val clientGenerator = project.findProperty("clientGenerator")?.toString() ?: "pbk-kotlin-client"
val serverGenerator = project.findProperty("serverGenerator")?.toString() ?: "pbk-kotlin-server"
val modelGenerator = project.findProperty("modelGenerator")?.toString() ?: "pbk-kotlin-client"

buildscript {
    repositories {
        maven {
            url = uri("https://plugins.gradle.org/m2/")
        }
    }
    dependencies {
        classpath("de.undercouch:gradle-download-task:5.1.0")
    }
}

tasks.register("copySwaggerUi") {
    group = "openapi"
    outputs.dir("$buildDir/openapi-generated/resources")
    inputs.dir("$rootDir/openapi")
    doLast {
        var swaggersConfig = mutableListOf<String>()
        val openapiPath = Paths.get("$rootDir/openapi")
        forEachSpecInFile("servers.conf", "openapi") {
            swaggersConfig.add(("""{url: contextPath + '/swagger/${openapiPath.relativize(it)}', name: "${it.fileName}"}""").replace('\\', '/'))
        }

        legacySwagger?.let {
            swaggersConfig.add("""{url: contextPath + '$it', name: "legacy"}""")
        }

        copy {
            from("$rootDir/openapi/")
            include("index.html.template")
            into("$buildDir/openapi-generated/resources/openapi")
            filter { line -> line.replace("{{specs}}", swaggersConfig.joinToString(",")) }
            rename { file -> "index.html" }
        }
    }
}

tasks.register("regenerateClasses") {
    group = "openapi"
    dependsOn("downloadPbkCodegen")
    dependsOn("downloadOpenapiGenerator")
    dependsOn("copyOpenApiSpec")
    dependsOn("copySwaggerUi")
    shouldRunAfter("clean")
    val mainOutputDir = "$buildDir/openapi-generated"
    val testOutputDir = "$buildDir/openapi-generated-test"

    outputs.dir("$mainOutputDir/src")
    outputs.dir("$testOutputDir/src")

    inputs.dir("$rootDir/openapi")

    doLast {
        println("Type Mappings: $typeMappings")
        println("Import Mappings: $importMappings")
        println("Active ignore file: $ignoreFile")

        val missingTestClientPaths = mutableListOf<java.nio.file.Path>()
        forEachSpecInFile("servers.conf", "openapi") {
            missingTestClientPaths.add(it)
            regenerateClassesForApi(
                inputPath = it,
                generator = serverGenerator,
                additionalProperties = serverAdditionalProperties,
                outputDir = mainOutputDir,
            )
        }
        forEachSpecInFile("clients.conf", "openapi") {
            missingTestClientPaths.remove(it)
            regenerateClassesForApi(
                inputPath = it,
                generator = clientGenerator,
                additionalProperties = clientAdditionalProperties,
                outputDir = mainOutputDir,
            )
        }

        missingTestClientPaths.forEach {
            regenerateClassesForApi(
                inputPath = it,
                generator = clientGenerator,
                additionalProperties = clientAdditionalProperties,
                outputDir = testOutputDir,
            )
        }

        forEachSpecInFile("../models/models.conf", "models") {
            regenerateClassesForApi(
                inputPath = it,
                generator = modelGenerator,
                additionalProperties = modelAdditionalProperties,
                outputDir = mainOutputDir,
            )
        }
    }
}

fun forEachSpecInFile(file: String, dir: String, runnable: (path: java.nio.file.Path) -> Unit) {
    val configFile = Paths.get("$rootDir/$dir/").resolve(file)
    if (Files.exists(configFile)) {
        Files.readAllLines(configFile).forEach {
            val specPath = Paths.get("$rootDir/$dir/").resolve(it)
            runnable.invoke(specPath)
        }
    }
}


fun isLaterOrEqualSemver(what: String, laterThan: String): Boolean {
    val whatParsed = what.split(".").map { Regex("\\d+").find(it)!!.value.toInt() }
    val laterThanParsed = laterThan.split(".").map { Regex("\\d+").find(it)!!.value.toInt() }

    return whatParsed[0] > laterThanParsed[0] ||
            (whatParsed[0] == laterThanParsed[0] && whatParsed[1] > laterThanParsed[1]) ||
            (whatParsed[0] == laterThanParsed[0] && whatParsed[1] == laterThanParsed[1] && whatParsed[2] > laterThanParsed[2]) ||
            (whatParsed[0] == laterThanParsed[0] && whatParsed[1] == laterThanParsed[1] && whatParsed[2] == laterThanParsed[2])
}

fun Openapi_gradle.regenerateClassesForApi(
    inputPath: java.nio.file.Path,
    generator: String,
    additionalProperties: String,
    outputDir: String,
) = exec {
    val logLevel = when(gradle.startParameter.logLevel) {
        LogLevel.DEBUG -> "debug"
        LogLevel.INFO -> "info"
        LogLevel.LIFECYCLE -> "warn"
        LogLevel.WARN -> "warn"
        LogLevel.QUIET -> "error"
        LogLevel.ERROR -> "error"
        else -> "warn"
    }
    commandLine(
        "java",
        "--illegal-access=deny",
        "-Dlog.level=$logLevel",
        "-cp", "$pbkCodegenJarPath$pathSeparator$buildDir/$openapiCliPath/openapi-generator-cli-$openapiCliVersion.jar",
        "org.openapitools.codegen.OpenAPIGenerator", "generate",
        "-g", generator,
        "-o", outputDir,
        "-i", inputPath.toString(),
        "--additional-properties", additionalProperties,
        "--package-name", inputPath.fileName.toString().substringBefore(".yml").substringBefore(".yaml").substringBefore('-'),
        "--type-mappings", typeMappings,
        "--import-mappings", importMappings,
        "--ignore-file-override", ignoreFile,
        "--library", library,
        "--global-property", globalProperty,
        "--model-name-suffix", modelNameSuffix
    )
}

tasks.register<Copy>("copyOpenApiSpec") {
    group = "openapi"
    outputs.dir("$buildDir/openapi-generated/resources")
    inputs.dir("$rootDir/openapi")
    from("$rootDir/openapi")
    include("**/*.*")
    exclude("**/*.template")
    exclude("**/*.kts")
    into("$buildDir/openapi-generated/resources/openapi")
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

tasks.register<Download>("downloadPbkCodegen") {
    onlyIf { !Files.exists(Paths.get(pbkCodegenJarPath)) }
    group = "openapi"
    src("$artifactoryUrl/artifactory/pbk/cz/partners/common/pbk-codegen/$pbkCodegenVersion/pbk-codegen-$pbkCodegenVersion.jar")
    dest(File(buildDir, "pbk-codegen.jar"))
    overwrite(false)
    username(artifactoryUser)
    password(artifactoryPassword)
}

tasks.register<Download>("downloadOpenapiGenerator") {
    onlyIf { !Files.exists(Paths.get("$buildDir/$openapiCliPath/openapi-generator-cli-$openapiCliVersion.jar")) }
    group = "openapi"
    src("$artifactoryUrl/artifactory/mavenCentral/org/openapitools/openapi-generator-cli/$openapiCliVersion/openapi-generator-cli-$openapiCliVersion.jar")
    dest(File("$buildDir/$openapiCliPath", "openapi-generator-cli-$openapiCliVersion.jar"))
    overwrite(false)
    username(artifactoryUser)
    password(artifactoryPassword)
}

// Without this doesn't work with kotlin 1.9.0
// It's because of https://kotlinlang.org/docs/whatsnew19.html#kapt-doesn-t-cause-eager-task-creation-in-gradle
afterEvaluate {
    tasks {
        findByName("kaptGenerateStubsKotlin")?.dependsOn(
            "regenerateClasses",
        )
        findByName("processResources")?.dependsOn(
            "copyOpenApiSpec",
            "copySwaggerUi",
        )
        findByName("inspectRuntimeClasspath")?.dependsOn(
            "regenerateClasses",
            "copyOpenApiSpec",
            "copySwaggerUi",
        )
    }
}
