# Keycloak migration

Never initialize keycloak manually again!

We define the whole keycloak configuration in this tool. This is the only one place to define any changes in keycloak, except
* user management (see project [keycloak-user-manager](https://gitlab.pbk-lab.tech/branka/be/security/keycloak-user-manager)) 
* and resource permission management (see project [keycloak-uma-operator](https://gitlab.pbk-lab.tech/branka/be/security/keycloak-uma-operator)).

It is a job container (as opposed to a long-running service).
Every time when `keycloak-user-manager` is run, it starts Micronaut application, run migrations in keycloak and exits.

## Restrictions
- description is always required
- use client roles only, do not use general realm roles
- client roles are never removed

## Configuration
All keycloak configuration is defined in file `keycloak/configuration.yaml` (custom file).
Just look into file and define your client with all properties. 

The configuration is then processed by business logic which setups a Keycloak configuration.

### Environment specific
Usually you just define a string, but in some cases, like an URLs, are environment specific.
> All environment specific properties ends with `-required` postfix

Enumeration of environment specific properties:
- root-url-required
- base-url-required
- redirect-uris-required
- web-origins-required
- admin-url-required
- master-saml-processing-url-required
- authentication-flow-required

Example:

Property `master-saml-processing-url` is environment specific so we define it in `configuration.yaml` like this:
```yaml
realms:
  - name: 'pid'
    clients-saml:
      - name: "azure"
        master-saml-processing-url-required: true
```
Then you have to define environment specific value in `application.yaml` like this:
```properties
client-saml:
  azure:
    master-saml-processing-url: ${CLIENT_SAML_AZURE_MASTER_SAML_PROCESSING_URL:`https://nexus.microsoftonline-p.com/federationmetadata/saml20/federationmetadata.xml`}
```

### Naming conventions

- Frontend clients starts with prefix `frontend-` e.g. `frontend-pbk-backoffice`
- Service clients starts with prefix `service-` and then name of the service in gitlab e.g. `service-pid-core`
- Roles holders starts with prefix `world-` e.g. `world-bank`

#### Naming of Authorization Manager Roles

Role names defined in `*-auth-manager` clients are based on roles listed in `service-keycloak-user-manager` client.
The actions covered by role access are typically - Create/Update user, Manage user roles and Get access token

The pattern is `<PERMISSION-FOR-ACTION>_<AUTH-MANAGER-PREFIX>` 
(e.g. `CREATE-USER_PFS` in `pfs-auth-manager`, `MANAGE-USER-ROLE_PFS-PORTFOLIO` in `pfs-portfolio-auth-manager`, ...)

Note: The role name must be unique because `pid-common`'s `security` library combines them into single list of roles.

### Authentication flows

Regarding authentication flows, this tool lets you define the following:

- Authentication flows
- Flow executions (steps)
- Flow execution configurations
- Mapping authentication flows to clients

#### Authentication flow and execution definition

Authentication flows are defined using `configuration.yaml`. Example:

```yaml
# within realm definition
authentication-flows:
  - PIDFederatedAuthentication
  - PIDFederatedAuthenticationBankId
  - PIDNiaAuthentication
```

Authentication flow names must correspond to an authentication flow definition in `authentication-flows.yaml`.

```yaml
authentication-flows:
  - name: PIDFederatedAuthenticationBankId
    description: Authentication flow for PID federated authentication - specific configuration for Bank iD
    executions:
      - alias: PFABIFedAuth
        description: Federated authentication
        type: FED_AUTHENTICATOR
        config: fed-authenticator-config-bankid
        requirement: REQUIRED

      - alias: PFABIDetermineLoa
        description: Determine LoA
        type: GENERIC_SUBFLOW
        requirement: REQUIRED
        executions:
          - alias: PFABIAcrLoa2
            description: ACR loa2
            type: GENERIC_SUBFLOW
            requirement: CONDITIONAL
```

- name - (required) Name of the authentication flow
- description - Flow description shown in KC GUI
- type - (default `basic-flow`) Type of authentication flow (`basic-flow`/`client-flow`)
- executions - List of auth flow executions
  - alias - (required, unique) Alias/identifier of the referenced execution
  - description - Description of the execution
  - type - (required) implementation and other characteristics of the execution
  - config - ID of a referenced configuration (see below)
  - requirement - (default `REQUIRED`) Execution requirement level (`REQUIRED`, `ALTERNATIVE`, `CONDITIONAL`, `DISABLED`)

#### Authentication flow execution config definitions

Individual flow executions can be parameterized and `keycloak-migration` can be used to set values of defined parameters.
Configurations are defined within `application.yml` and values can reference environment variables to accomodate for env-specific values.

Name of the configuration is important for mapping to flow executions. One configuration can be mapped to multiple executions

```yaml
authentication-flows:
  config:
    fed-authenticator-config:
      properties:
        - key: FedAuthBackendPublicUrl
          value: ${CLIENTS_PID_FED_AUTHENTICATION:`http://localhost:9999/`}
        - key: FedAuthBackendApiUrl
          value: ${CLIENTS_PID_FED_AUTHENTICATION:`http://localhost:9999/`}
        - key: FedAuthBackendLoginPath
          value: ${CONFIG_FED_AUTH_LOGIN_PATH:`public/v1/login/init`}
        - key: FedAuthBackendVerifyPath
          value: ${CONFIG_FED_AUTH_VERIFY_PATH:`keycloak/v1/verify`}
```

#### Mapping authentication flows to clients

Mapping is defined in `application.yml` in the `client-openid` section.
The value should match the `name` attribute of an existing authentication flow.

```yaml
client-openid:
  tpp-fedauth-test:
    redirect-uris: ${REDIRECT_URIS_TPP_FEDAUTH_TEST:`https://oauth.pstmn.io/v1/callback`}
    authentication-flow: ${AUTH_FLOW_TPP_FEDAUTH_TEST:`PIDFederatedAuthentication`}
```

## Features
- create realms
  - define open-id clients
    - set name
    - set service accounts enabled
    - set standard flow enabled
    - set implicit flow enabled
    - set direct access grants enabled
    - set public client
    - set authorization services enabled
    - set root url
    - set base url
    - set redirect uris
    - set web origins
    - set admin url
    - define roles
      - set name
      - set description
    - assign client scopes
    - assign roles from another client
      - client name
      - role name
  - define saml clients
    - set name
    - set master saml processing url
    - set saml signature key name
    - define roles
      - set name
      - set description
    - assign roles from another client
      - client name
      - role name
  - define client-scopes
    - name
    - type
    - define mappers
      - set name
      - set type
      - set token claim name
      - set token claim type
  - define authentication flows
    - name
    - description
    - define executions (auth flow steps)
      - authenticator reference
      - requirement
      - configuration
