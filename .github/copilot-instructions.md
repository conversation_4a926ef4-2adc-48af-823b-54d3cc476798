# Copilot Instructions - Keycloak Migration Tool

## Project Overview
This is a **declarative Keycloak configuration management tool** - a job container (not a long-running service) built with **Micronaut + Kotlin** that applies Keycloak configuration migrations idempotently. The tool is the single source of truth for Keycloak configuration across multiple realms and environments.

## Key Architecture

### Core Components
- **Main Application**: `service/` - Micronaut application with startup migration trigger
- **Configuration Sources**: 
  - `keycloak/configuration.yaml` - Main Keycloak config (realms, clients, roles)
  - `keycloak/authentication-flows.yaml` - Authentication flow definitions
  - `service/src/main/resources/application.yml` - Environment-specific values

### Service Layer Pattern
The migration follows a layered service architecture in `service/src/main/kotlin/cz/partners/component/keycloak/migration/service/keycloak/`:
- `MigrationService` - Orchestrates the complete migration flow
- `RealmService` - Manages realm creation/updates
- `ClientService` - Handles OpenID/SAML client configuration
- `RoleService` - Creates and manages client roles
- `AssignRoleService` - Manages role assignments between clients
- `AuthenticationFlowService` - Configures custom authentication flows
- `ClientScopeService` - Manages OAuth2 scopes

## Critical Configuration Patterns

### Environment-Specific Properties
Properties ending with `-required` in `configuration.yaml` must have corresponding values in `application.yml`:
```yaml
# In configuration.yaml
redirect-uris-required: true
# In application.yml  
client-openid:
  my-client:
    redirect-uris: ${REDIRECT_URIS_MY_CLIENT:`http://localhost:8080/*`}
```

### Client Naming Conventions
- **Frontend clients**: `frontend-*` (e.g., `frontend-pbk-backoffice`)
- **Service clients**: `service-*` (e.g., `service-pid-core`) 
- **Role holders**: `world-*` (e.g., `world-bank`)
- **Auth manager roles**: `<PERMISSION-FOR-ACTION>_<AUTH-MANAGER-PREFIX>`

### Critical Business Rules
- **Description is always required** for all entities
- **Use client roles only** - never realm roles
- **Client roles are never removed** - only added
- **Idempotent operations** - migrations can be run multiple times safely

## Development Workflows

### Build and Test
```bash
./gradlew build                    # Full build including OpenAPI generation
./gradlew test                     # Run test suite
./gradlew regenerateClasses        # Regenerate OpenAPI clients from specs
```

### Local Development
- Configuration files are copied into Docker container at `/home/<USER>/keycloak/`
- Token renewal happens automatically via `RenewAccessTokenService`
- Use `ContainerTest` base class for integration tests with Testcontainers

### Key Integration Points
- **Keycloak Admin API**: Generated clients in `build/openapi-generated/src/org/keycloak/admin/v19/`
- **Token Management**: `KeycloakAdminApiFilter` automatically injects bearer tokens
- **Error Handling**: Arrow Either pattern with `PbkException` for functional error handling
- **Retry Logic**: Resilience4j for handling transient failures

## Testing Strategy
- Integration tests extend `ContainerTest` which spins up real Keycloak instances
- Tests use `@MicronautTest(startApplication = false)` to avoid startup migration
- Token renewal required before realm operations: `renewAccessTokenService.renewAccessToken()`

## Configuration Validation
When modifying configurations:
1. Ensure all `-required` properties have environment mappings
2. Validate naming conventions are followed
3. Test with `FullMigrationTest` for end-to-end validation
4. Check client role assignments follow the restriction patterns

## Deployment Context
- Job runs on startup via `@EventListener` on `StartupEvent`
- Deployed as Helm chart to `keycloak` namespace via ArgoCD
- GitLab CI handles chart generation and OCI registry pushes
- Environment-specific values injected via Kubernetes secrets/configmaps

## Common Gotchas
- Authentication flows must be defined in separate `authentication-flows.yaml`
- Client scopes can only be changed by removing and re-adding
- Service account users are automatically created for service clients
- Platform Common library provides BOM and shared utilities (`cz.pbktechnology.platform.common.*`)