# Configuration Advice
## Project

|            |                                                                   |
|------------|-------------------------------------------------------------------|
| Name:      | keycloak-migration                                                |
| Namespace: | OCI: keycloak, AWS:keycloak-migration                             |
| Type:      | job                                                               |
| Repository | https://gitlab.pbk-lab.tech/branka/be/security/keycloak-migration |


## Destination environment
| DEV  | STAGE | TEST | PROD |
|------|-------|------|------|
| true | true  | true | true |

## Ingress
|          | DEV   | STAGE | TEST  | PROD  |
|----------|-------|-------|-------|-------|
| Enabled: | false | false | false | false |

## Infrastructure requirement
- Zipkin
- Loki
- Keycloak 20

## External integration

---
## Values and secrets

### Application
| Key                         | Description                                                                    | Example                     |
|-----------------------------|--------------------------------------------------------------------------------|-----------------------------|
| SERVER_PORT                 | Port aplikace - vzdy 8083                                                      | 8083                        |
| KEYCLOAK_CONFIGURATION_FILE | Cesta ke konfiguraci KC. Vzdy stejna, protoze konfigurace je zabalena v image. | keycloak/configuration.yaml |

### Zipkin
| Key        | Description | Example                                           |
|------------|-------------|---------------------------------------------------|
| ZIPKIN_URL | Host url    | http://jaeger-dev-oci-collector.surveillance:9411 |

### Loki
| Key           | Description                                                                           | Example                                                   |
|---------------|---------------------------------------------------------------------------------------|-----------------------------------------------------------|
| LOKI_ENABLED  | Enabled                                                                               | true                                                      |
| LOKI_HOST     | Loki host url                                                                         | http://loki-oci-dev-loki-distributed-gateway.surveillance |
| LOKI_TENANT   | By namespace                                                                          | bank                                                      |
| LOKI_USER     | User - default `brankar` defined in `deployment.yaml`. Na produkci hodnotu `sa_loki`. | sa_loki                                                   |
| LOKI_PASSWORD | Password                                                                              | #secret                                                   |

### Keycloak admin
| Key                                   | Description                                           | Example                                                                        |
|---------------------------------------|-------------------------------------------------------|--------------------------------------------------------------------------------|
| KEYCLOAK_ADMIN_ENDPOINT               | Url na realm master na keycloak 20                    | https://login-adm.dev.pbk-lab.tech/realms/master/protocol/openid-connect/token |
| KEYCLOAK_ADMIN_CLIENT_ID              | Client name                                           | admin-cli                                                                      |
| KEYCLOAK_ADMIN_USERNAME               | Admin username                                        | admin                                                                          |
| KEYCLOAK_ADMIN_PASSWORD               | Admin password                                        | #secret                                                                        |
| KEYCLOAK_ADMIN_TOKEN_RENEWAL_INTERVAL | Access token renewal interval < access token lifespan | 55s                                                                            |

### Authentication flow configuration

| Key                                                 | Description                                                                                          | Type                            | Example                                        |
|-----------------------------------------------------|------------------------------------------------------------------------------------------------------|---------------------------------|------------------------------------------------|
| CLIENTS_APIM                                        | WSO2 URL. Used for 3xx redirects to frontend of federated authentication                             | String (URI)                    | https://api.test.pbk-lab.tech                  |
| CLIENTS_PID_FED_AUTHENTICATION                      | `pid-fed-authentication` service URL accessible from within cluster                                  | String (URI)                    | http://pid-fed-authentication.pid.svc:8083     |
| CONFIG_FED_AUTH_LOGIN_PATH                          | Application context where the endpoint for initiation of federated authentication session is present | String                          | pid-fed-authentication-publicapi/v1/login/init |
| CONFIG_FED_AUTH_VERIFY_PATH                         | Application context where the endpoint for auth session state verification is present                | String                          | keycloak/v1/verify                             |
| CONFIG_FED_AUTH_BANKID_REQUEST_VALIDATION_ENABLED   | Toggle for Bank iD specific validations because the same auth provider is shared with e.g. PSD2      | Boolean                         | true                                           |
| CONFIG_FED_AUTH_BANKID_PARAM_VALIDATION_PARAMS      | List of parameters to validate during Bank iD specific validations                                   | String of `##` separated values | state##nonce##scope                            |
| CONFIG_FED_AUTH_BANKID_AUTH_METHODS_NOTE_NAME       | Client session note name containing a list of methods used for authentication                        | String                          | authentication_methods                         |
| CONFIG_CONDITION_LOA_LOA1_LEVEL                     | LoA value used for ACR loa2 condition auth flow execution                                            | Int                             | 1                                              |
| CONFIG_CONDITION_LOA_LOA1_MAX_AGE                   | LoA condition max age                                                                                | Int                             | 0                                              |
| CONFIG_CONDITION_LOA_LOA2_LEVEL                     | LoA value used for ACR loa3 condition auth flow execution                                            | Int                             | 2                                              |
| CONFIG_CONDITION_LOA_LOA2_MAX_AGE                   | LoA condition max age                                                                                | Int                             | 0                                              |
| CONFIG_VERIFY_AUTH_METHODS_LOA2_METHODS_REQUIRED    | List of authentication methods which all have to be present in order to obtain ACR loa2              | String of `##` separated values | mfa##swk                                       |
| CONFIG_VERIFY_AUTH_METHODS_LOA2_NOTE_NAME           | Client session note name where authentication methods are looked up for ACR loa2 verification        | String                          | authentication_methods                         |
| CONFIG_VERIFY_AUTH_METHODS_LOA3_METHODS_REQUIRED    | List of authentication methods which all have to be present in order to obtain ACR loa3              | String of `##` separated values | mfa##swk                                       |
| CONFIG_VERIFY_AUTH_METHODS_LOA3_METHODS_ALTERNATIVE | List of authentication methods which at least one has to be present in order to obtain ACR loa3      | String of `##` separated values | pin##fpt                                       |
| CONFIG_VERIFY_AUTH_METHODS_LOA3_NOTE_NAME           | Client session note name where authentication methods are looked up for ACR loa3 verification        | String                          | authentication_methods                         |

### Clients
| Key                               | Description                            | Example                            |
|-----------------------------------|----------------------------------------|------------------------------------|
| CLIENTS_ORG_KEYCLOAK_CLIENT_URL   | Base url na KC                         | https://login-adm.dev.pbk-lab.tech |
| CLIENTS_ORG_KEYCLOAK_READ_TIMEOUT | Read timeout for calls to Keycloak API | 30 s                               |
| CLIENTS_ORG_KEYCLOAK_CLIENT_PATH  | Path pro admina                        | /admin/realms                      |

### Clients settings

| Key                                                        | Description                                                                                                                                                                                                                                | Example                                                                                          |
|------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------|
| WEB_ORIGINS_FRONTEND_APS                                   | Nastaveni web-origins pro client `frontend-aps`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                                 | https://aps-frontend.oci.dev.pbk-lab.tech &#124; https://aps-frontend.oci.dev.pbk-lab.tech/*     |
| WEB_ORIGINS_FRONTEND_LOAN_APPLICATION_PFS                  | Nastaveni web-origins pro client `frontend-loan-application-pfs`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                | https://loan-application-fe-pfs.internal.dev.pbk-lab.tech                                        |
| WEB_ORIGINS_FRONTEND_LOAN_APPLICATION                      | Nastaveni web-origins pro client `frontend-loan-application`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                    | https://loan-application-fe.internal.dev.pbk-lab.tech                                            |
| REDIRECT_URIS_FRONTEND_LOAN_APPLICATION                    | Nastaveni redirect-uri pro client `frontend-loan-application`.  Prosim dodrzet pattern podle example.                                                                                                                                      | https://loan-application-fe.internal.dev.pbk-lab.tech/auth/pid-callback                          |                                                                                           |
| WEB_ORIGINS_FRONTEND_LOAN_PROCESSES                        | Nastaveni web-origins pro client `frontend-loan-processes`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                      | https://loan-processes-fe.internal.dev.pbk-lab.tech                                              |
| REDIRECT_URIS_FRONTEND_LOAN_PROCESSES                      | Nastaveni redirect-uri pro client `frontend-loan-processes`.  Prosim dodrzet pattern podle example.                                                                                                                                        | https://loan-processes-fe.internal.dev.pbk-lab.tech/auth/pid-callback                            |                                                                                           |
| WEB_ORIGINS_FRONTEND_LOAN_TEMPLATING                       | Nastaveni web-origins pro client `frontend-loan-templating`. Je to url aplikace. Prosím dodržet pattern podle example.                                                                                                                     | https://loan-templating-fe.internal.dev.pbk-lab.tech                                             |
| REDIRECT_URIS_FRONTEND_LOAN_TEMPLATING                     | Nastaveni redirect-uri pro client `frontend-loan-templating`.  Prosím dodržet pattern podle example.                                                                                                                                       | https://loan-templating-fe.internal.dev.pbk-lab.tech/auth/pid-callback                           |                                                                                           |
| WEB_ORIGINS_FRONTEND_BANK_ONBOARDING                       | Nastaveni web-origins pro client `bank-onboarding`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                              | https://onboarding.oci.dev.pbk-lab.tech &#124; https://onboarding.oci.dev.pbk-lab.tech/*         |
| WEB_ORIGINS_FRONTEND_BOA                                   | Nastavení web-origins pro client `frontend-boa`. Je to URL aplikace. Prosím dodržet pattern podle example.                                                                                                                                 | https://olymp.internal.dev.pbk-lab.tech &#124; https://olymp.internal.dev.pbk-lab.tech/*         |
| WEB_ORIGINS_FRONTEND_BOA_PFS                               | Nastavení web-origins pro client `frontend-boa-pfs`. Je to URL aplikace. Prosím dodržet pattern podle example.                                                                                                                             | https://olymp-pfs.internal.dev.pbk-lab.tech &#124; https://olymp-pfs.internal.dev.pbk-lab.tech/* |
| REALM_PID_FRONTEND_URL                                     | Subdomena per realm. Default je empty string.                                                                                                                                                                                              |                                                                                                  |
| REALM_ONBOARD_FRONTEND_URL                                 | Subdomena per realm. Default je empty string.                                                                                                                                                                                              |                                                                                                  |
| REALM_PFS_FRONTEND_URL                                     | Subdomena per realm. Default je empty string.                                                                                                                                                                                              |                                                                                                  |
| REALM_SERVICES_FRONTEND_URL                                | Subdomena per realm. Default je empty string.                                                                                                                                                                                              |                                                                                                  |
| REALM_GEPARD_FRONTEND_URL                                  | Subdomena per realm. Default je empty string.                                                                                                                                                                                              |                                                                                                  |
| REDIRECT_URIS_FRONTEND_KNBOX                               | Nastaveni web-origins pro client `frontend-knbox`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                               | http://knbox.internal.dev.pbk-lab.tech/* &#124; https://knbox.internal.dev.pbk-lab.tech/*        |                                                                                           |
| ROOT_URL_FRONTEND_KNBOX                                    | Nastaveni root-url pro client `frontend-knbox`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                                  | https://knbox.internal.dev.pbk-lab.tech                                                          |
| REDIRECT_URIS_FRONTEND_PAF                                 | Nastaveni web-origins pro client `frontend-paf`.                                                                                                                                                                                           | https://paf-case-investigation-web.internal.dev.pbk-lab.tech                                     |                                                                                           |
| ROOT_URL_FRONTEND_PAF                                      | Nastaveni root-url pro client `frontend-paf`.                                                                                                                                                                                              | https://paf-case-investigation-web.internal.dev.pbk-lab.tech                                     |
| REDIRECT_URIS_FRONTEND_AML_PLUGIN                                   | Nastaveni redirect-uri pro client `frontend-aml-plugin`                                                                                                                                                                                    | https://aml-services.internal.test.pbk-lab.tech                                     |
| WEB_ORIGINS_FRONTEND_AML_PLUGIN                                   | Nastaveni  pro client `frontend-aml-plugin`                                                                                                                                                                                    | https://aml-services.internal.test.pbk-lab.tech                                     |
| REALM_PID_TOKEN_LIFESPAN_ACCESS_TOKEN                      | Max time in seconds before an access token is expired. (PID realm)                                                                                                                                                                         | 5m                                                                                               |
| REALM_PID_TOKEN_LIFESPAN_SSO_SESSION                       | Time in seconds a session is allowed to be idle before it expires. (PID realm)                                                                                                                                                             | 30m                                                                                              |
| REALM_PID_TOKEN_LIFESPAN_SSO_SESSION_MAX                   | Max time in seconds before a session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session. (PID realm)                                                                | 10h                                                                                              |
| REALM_PID_TOKEN_LIFESPAN_CLIENT_SESSION                    | Time in seconds a client session is allowed to be idle before it expires. (PID realm)                                                                                                                                                      | 0m                                                                                               |
| REALM_PID_TOKEN_LIFESPAN_CLIENT_SESSION_MAX                | Max time in seconds before a client session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session. (PID realm)                                                         | 0m                                                                                               |
| REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION                   | Time in seconds an offline session is allowed to be idle before it expires. Expiration of a single offline token. (PID realm)                                                                                                              | 30d                                                                                              |
| REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED       | Enable offline session max. (PID realm)                                                                                                                                                                                                    | false                                                                                            |
| REALM_PID_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX               | Max time in seconds before an offline session is expired regardless of activity. Newly generated offline tokens cannot exceed overall lifespan of the session. Only relevant if offlineSessionMaxLifespanEnabled is true. (PID realm)      | 60d                                                                                              |
| REALM_PFS_TOKEN_LIFESPAN_ACCESS_TOKEN                      | Max time in seconds before an access token is expired. (PFS realm)                                                                                                                                                                         | 5m                                                                                               |
| REALM_PFS_TOKEN_LIFESPAN_SSO_SESSION                       | Time in seconds a session is allowed to be idle before it expires. (PFS realm)                                                                                                                                                             | 30m                                                                                              |
| REALM_PFS_TOKEN_LIFESPAN_SSO_SESSION_MAX                   | Max time in seconds before a session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session. (PFS realm)                                                                | 10h                                                                                              |
| REALM_PFS_TOKEN_LIFESPAN_CLIENT_SESSION                    | Time in seconds a client session is allowed to be idle before it expires. (PFS realm)                                                                                                                                                      | 0m                                                                                               |
| REALM_PFS_TOKEN_LIFESPAN_CLIENT_SESSION_MAX                | Max time in seconds before a client session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session. (PFS realm)                                                         | 0m                                                                                               |
| REALM_PFS_TOKEN_LIFESPAN_OFFLINE_SESSION                   | Time in seconds an offline session is allowed to be idle before it expires. Expiration of a single offline token. (PFS realm)                                                                                                              | 30d                                                                                              |
| REALM_PFS_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED       | Enable offline session max. (PFS realm)                                                                                                                                                                                                    | false                                                                                            |
| REALM_PFS_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX               | Max time in seconds before an offline session is expired regardless of activity. Newly generated offline tokens cannot exceed overall lifespan of the session. Only relevant if offlineSessionMaxLifespanEnabled is true. (PFS realm)      | 60d                                                                                              |
| REALM_ONBOARD_TOKEN_LIFESPAN_ACCESS_TOKEN                  | Max time in seconds before an access token is expired. (ONBOARD realm)                                                                                                                                                                     | 5m                                                                                               |
| REALM_ONBOARD_TOKEN_LIFESPAN_SSO_SESSION                   | Time in seconds a session is allowed to be idle before it expires. (ONBOARD realm)                                                                                                                                                         | 30m                                                                                              |
| REALM_ONBOARD_TOKEN_LIFESPAN_SSO_SESSION_MAX               | Max time in seconds before a session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session. (ONBOARD realm)                                                            | 10h                                                                                              |
| REALM_ONBOARD_TOKEN_LIFESPAN_CLIENT_SESSION                | Time in seconds a client session is allowed to be idle before it expires. (ONBOARD realm)                                                                                                                                                  | 0m                                                                                               |
| REALM_ONBOARD_TOKEN_LIFESPAN_CLIENT_SESSION_MAX            | Max time in seconds before a client session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session. (ONBOARD realm)                                                     | 0m                                                                                               |
| REALM_ONBOARD_TOKEN_LIFESPAN_OFFLINE_SESSION               | Time in seconds an offline session is allowed to be idle before it expires. Expiration of a single offline token. (ONBOARD realm)                                                                                                          | 30d                                                                                              |
| REALM_ONBOARD_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED   | Enable offline session max. (ONBOARD realm)                                                                                                                                                                                                | false                                                                                            |
| REALM_ONBOARD_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX           | Max time in seconds before an offline session is expired regardless of activity. Newly generated offline tokens cannot exceed overall lifespan of the session. Only relevant if offlineSessionMaxLifespanEnabled is true. (ONBOARD realm)  | 60d                                                                                              |
| REALM_SERVICES_TOKEN_LIFESPAN_ACCESS_TOKEN                 | Max time in seconds before an access token is expired. (SERVICES realm)                                                                                                                                                                    | 5m                                                                                               |
| REALM_SERVICES_TOKEN_LIFESPAN_SSO_SESSION                  | Time in seconds a session is allowed to be idle before it expires. (SERVICES realm)                                                                                                                                                        | 30m                                                                                              |
| REALM_SERVICES_TOKEN_LIFESPAN_SSO_SESSION_MAX              | Max time in seconds before a session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session. (SERVICES realm)                                                           | 10h                                                                                              |
| REALM_SERVICES_TOKEN_LIFESPAN_CLIENT_SESSION               | Time in seconds a client session is allowed to be idle before it expires. (SERVICES realm)                                                                                                                                                 | 0m                                                                                               |
| REALM_SERVICES_TOKEN_LIFESPAN_CLIENT_SESSION_MAX           | Max time in seconds before a client session is expired. Expiration of a single session - newly generated refresh tokens cannot exceed overall lifespan of the session. (SERVICES realm)                                                    | 0m                                                                                               |
| REALM_SERVICES_TOKEN_LIFESPAN_OFFLINE_SESSION              | Time in seconds an offline session is allowed to be idle before it expires. Expiration of a single offline token. (SERVICES realm)                                                                                                         | 30d                                                                                              |
| REALM_SERVICES_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX_ENABLED  | Enable offline session max. (SERVICES realm)                                                                                                                                                                                               | false                                                                                            |
| REALM_SERVICES_TOKEN_LIFESPAN_OFFLINE_SESSION_MAX          | Max time in seconds before an offline session is expired regardless of activity. Newly generated offline tokens cannot exceed overall lifespan of the session. Only relevant if offlineSessionMaxLifespanEnabled is true. (SERVICES realm) | 60d                                                                                              |
| WEB_ORIGINS_FRONTEND_LOAN_TILES                            | Nastaveni web-origins pro client `frontend-loan-tiles`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                          | https://loan-tiles-fe.internal.dev.pbk-lab.tech                                                  |
| REDIRECT_URIS_FRONTEND_LOAN_TILES                          | Nastaveni redirect-uri pro client `frontend-loan-tiles`.  Prosim dodrzet pattern podle example.                                                                                                                                            | https://loan-tiles-fe.internal.dev.pbk-lab.tech/auth/callback                                    |                                                                                           |
| WEB_ORIGINS_FRONTEND_LOAN_TILES_GEPARD                     | Nastaveni web-origins pro client `frontend-loan-tiles-gepard`. Je to url aplikace. Prosim dodrzet pattern podle example.                                                                                                                   | https://loan-tiles-fe-gepard.internal.dev.pbk-lab.tech                                           |
| REDIRECT_URIS_FRONTEND_LOAN_TILES_GEPARD                   | Nastaveni redirect-uri pro client `frontend-loan-tiles-gepard`.  Prosim dodrzet pattern podle example.                                                                                                                                     | https://loan-tiles-fe-gepard.internal.dev.pbk-lab.tech/auth/callback                             |                                                                                           |


## Secrets
| Key                                                      | Secret name                             | Key                                                      |
|----------------------------------------------------------|-----------------------------------------|----------------------------------------------------------|
| CLIENT_SECRETS_COMPONENT_DECISION_ENGINE                 | loan-keycloak-client-secrets            | CLIENT_SECRETS_COMPONENT_DECISION_ENGINE                 |
| CLIENT_SECRETS_COMPONENT_DECISION_ENGINE_AML             | tarandm-keycloak-client-secrets         | CLIENT_SECRETS_COMPONENT_DECISION_ENGINE_AML             |
| CLIENT_SECRETS_COMPONENT_KNBOX                           | knbox-keycloak-client-secrets           | CLIENT_SECRETS_COMPONENT_KNBOX                           |
| CLIENT_SECRETS_DEPARTMENT_MOJE_PARTNERS_CLIENT           | external-keycloak-client-secrets        | CLIENT_SECRETS_DEPARTMENT_MOJE_PARTNERS_CLIENT           |
| CLIENT_SECRETS_DEPARTMENT_PIS_CLIENT                     | external-keycloak-client-secrets        | CLIENT_SECRETS_DEPARTMENT_PIS_CLIENT                     |
| CLIENT_SECRETS_FRONTEND_KNBOX                            | knbox-keycloak-client-secrets           | CLIENT_SECRETS_FRONTEND_KNBOX                            |
| CLIENT_SECRETS_SERVICE_PAF                               | paf-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PAF                               |
| CLIENT_SECRETS_SERVICE_PID_PAF                           | paf-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_PAF                           |
| CLIENT_SECRETS_COMPONENT_AML_PLUGIN_SERVICE              | aml-keycloak-client-secrets             | CLIENT_SECRETS_COMPONENT_AML_PLUGIN_SERVICE              |   
| CLIENT_SECRETS_FRONTEND_LOAN_APPLICATION                 | loan-keycloak-client-secrets            | CLIENT_SECRETS_FRONTEND_LOAN_APPLICATION                 |
| CLIENT_SECRETS_FRONTEND_LOAN_PROCESSES                   | loan-keycloak-client-secrets            | CLIENT_SECRETS_FRONTEND_LOAN_PROCESSES                   |
| CLIENT_SECRETS_FRONTEND_TREASURY                         | treasury-keycloak-client-secrets        | CLIENT_SECRETS_FRONTEND_TREASURY                         |
| CLIENT_SECRETS_MASTER_KEYCLOAK_USER_MANAGER              | keycloak-keycloak-client-secrets        | CLIENT_SECRETS_MASTER_KEYCLOAK_USER_MANAGER              |
| CLIENT_SECRETS_PSD2_TPP_DUMMY                            | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_DUMMY                            |
| CLIENT_SECRETS_PSD2_TPP_FINBRICKS                        | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_FINBRICKS                        |
| CLIENT_SECRETS_PSD2_TPP_PARTNERS_BANKA                   | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_PARTNERS_BANKA                   |
| CLIENT_SECRETS_PSD2_TPP_GOPAY                            | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_GOPAY                            |
| CLIENT_SECRETS_PSD2_TPP_PAYU                             | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_PAYU                             |
| CLIENT_SECRETS_PSD2_TPP_TRINITY_BANK                     | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_TRINITY_BANK                     |
| CLIENT_SECRETS_PSD2_TPP_INBANK                           | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_INBANK                           |
| CLIENT_SECRETS_PSD2_TPP_BANK_TRANSFER_WORLDLINE          | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_BANK_TRANSFER_WORLDLINE          |
| CLIENT_SECRETS_PSD2_TPP_EVERIFIN                         | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_EVERIFIN                         |
| CLIENT_SECRETS_PSD2_TPP_CONNECT_PAY_UAB                  | psd2-tpp-secrets                        | CLIENT_SECRETS_PSD2_TPP_CONNECT_PAY_UAB                  |
| CLIENT_SECRETS_RS_APS                                    | rs-pfs-secret                           | CLIENT_SECRETS_RS_APS                                    |
| CLIENT_SECRETS_RS_BOA                                    | rs-pid-secret                           | CLIENT_SECRETS_RS_BOA                                    |
| CLIENT_SECRETS_RS_BOA_PFS                                | rs-pfs-secret                           | CLIENT_SECRETS_RS_BOA                                    |
| CLIENT_SECRETS_RS_LOAN_APPLICATION                       | rs-pid-secret                           | CLIENT_SECRETS_RS_LOAN_APPLICATION                       |
| CLIENT_SECRETS_RS_LOAN_APPLICATION_PFS                   | rs-pfs-secret                           | CLIENT_SECRETS_RS_LOAN_APPLICATION                       |
| CLIENT_SECRETS_RS_LOAN_APPLICATION_GEPARD                | rs-gepard-secret                        | CLIENT_SECRETS_RS_LOAN_APPLICATION                       |
| CLIENT_SECRETS_RS_LOAN_PROCESSES                         | rs-pid-secret                           | CLIENT_SECRETS_RS_LOAN_PROCESSES                         |
| CLIENT_SECRETS_SERVICE_APIGW_OPERATOR_SYNCHRONOUS_API    | api-gw-operator-keycloak-client-secrets | CLIENT_SECRETS_SERVICE_APIGW_OPERATOR_SYNCHRONOUS_API    |
| CLIENT_SECRETS_SERVICE_APS_APPLICATION                   | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_APPLICATION                   |
| CLIENT_SECRETS_SERVICE_APS_BFF                           | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_BFF                           |
| CLIENT_SECRETS_SERVICE_APS_DOCUMENT                      | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_DOCUMENT                      |
| CLIENT_SECRETS_SERVICE_APS_EXCEPTION                     | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_EXCEPTION                     |
| CLIENT_SECRETS_SERVICE_APS_GATE                          | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_GATE                          |
| CLIENT_SECRETS_SERVICE_APS_LOAN                          | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_LOAN                          |
| CLIENT_SECRETS_SERVICE_APS_MODELING                      | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_MODELING                      |
| CLIENT_SECRETS_SERVICE_APS_ORIGINATION                   | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_ORIGINATION                   |
| CLIENT_SECRETS_SERVICE_APS_PARAMETERS                    | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_PARAMETERS                    |
| CLIENT_SECRETS_SERVICE_APS_PEPZ                          | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_PEPZ                          |
| CLIENT_SECRETS_SERVICE_APS_PROCESSES                     | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_PROCESSES                     |
| CLIENT_SECRETS_SERVICE_APS_VALUERS                       | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_APS_VALUERS                       |
| CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_BLOCKING             | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_ACCOUNTS_REGISTRY            |
| CLIENT_SECRETS_SERVICE_BANK_ACCOUNTS_REGISTRY            | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_ACCOUNTS_REGISTRY            |
| CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_TERMINATION          | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_TERMINATION          |
| CLIENT_SECRETS_SERVICE_BANK_TEMPLATE_RENDERER            | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_TEMPLATE_RENDERER            |
| CLIENT_SECRETS_SERVICE_BANK_TEMPLATING                   | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_TEMPLATING                   |
| CLIENT_SECRETS_SERVICE_BANK_FOREIGN_PAYMENT              | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_BANK_FOREIGN_PAYMENT         |
| CLIENT_SECRETS_SERVICE_BANK_BACKOFFICE_GATEWAY           | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_BACKOFFICE_GATEWAY           |
| CLIENT_SECRETS_SERVICE_BANK_CAMPAIGN                     | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CAMPAIGN                     |
| CLIENT_SECRETS_SERVICE_BANK_CARD_MANAGEMENT              | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CARD_MANAGEMENT              |
| CLIENT_SECRETS_SERVICE_BANK_CARD_SAVINGS                 | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CARD_SAVINGS                 |
| CLIENT_SECRETS_SERVICE_BANK_CLICK_TO_PAY                 | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CLICK_TO_PAY                 |
| CLIENT_SECRETS_SERVICE_BANK_CLIENT                       | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CLIENT                       |
| CLIENT_SECRETS_SERVICE_BANK_CLIENT_DOCUMENTS             | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CLIENT_DOCUMENTS             |
| CLIENT_SECRETS_SERVICE_BANK_CODELISTS                    | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CODELISTS                    |
| CLIENT_SECRETS_SERVICE_BANK_COOPERATION_SERVICE          | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_COOPERATION_SERVICE          |
| CLIENT_SECRETS_SERVICE_BANK_DASHBOARD                    | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_DASHBOARD                    |
| CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING               | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING               |
| CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING_GATEWAY       | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING_GATEWAY       |
| CLIENT_SECRETS_SERVICE_BANK_CHALLENGES                   | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CHALLENGES                   |
| CLIENT_SECRETS_SERVICE_BANK_CHARGEBACK                   | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_CHARGEBACK                   |
| CLIENT_SECRETS_SERVICE_BANK_JIRA_ADAPTER                 | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_JIRA_ADAPTER                 |
| CLIENT_SECRETS_SERVICE_BANK_MOBILITY                     | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_MOBILITY                     |
| CLIENT_SECRETS_SERVICE_BANK_PAYMENT                      | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_PAYMENT                      |
| CLIENT_SECRETS_SERVICE_BANK_PFS_INTEGRATION              | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_PFS_INTEGRATION              |
| CLIENT_SECRETS_SERVICE_BANK_SERVICE_GATEWAY              | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_SERVICE_GATEWAY              |
| CLIENT_SECRETS_SERVICE_BANK_STANDING_ORDER               | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_NOTIFICATION_GATEWAY              |
| CLIENT_SECRETS_SERVICE_BANK_TRANSACTION                  | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_TRANSACTION                  |
| CLIENT_SECRETS_SERVICE_BANK_TRANSACTION_NOTIFICATION     | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_TRANSACTION_NOTIFICATION     |
| CLIENT_SECRETS_SERVICE_BANK_TREASURY_SERVICE             | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_BANK_TREASURY_SERVICE             |
| CLIENT_SECRETS_SERVICE_DAILY_AUTH_MANAGER                | bank-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_DAILY_AUTH_MANAGER                |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_CLIENT          | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_CLIENT          |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_SK_CLIENT       | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_SK_CLIENT       |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT             | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT             |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT             | pfs-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT             |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_PAYMENT_CLIENT     | pfs-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_PAYMENT_CLIENT     |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_SK_CLIENT          | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_SK_CLIENT          |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_OCP_CLIENT             | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_OCP_CLIENT             |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CLIENT             | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CLIENT             |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_CLIENT             | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_CLIENT             |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_TEST_CLIENT        | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_TEST_CLIENT        |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_RENTEA_CLIENT          | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_RENTEA_CLIENT          |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_SIMPLEA_CLIENT         | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_SIMPLEA_CLIENT         |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_GEPARD_CLIENT          | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_GEPARD_CLIENT          |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_VUB_CLIENT             | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_VUB_CLIENT             |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_CLIENT          | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_CLIENT          |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_SK_CLIENT       | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_ORFEUS_SK_CLIENT       |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_WPB_CLIENT             | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_WPB_CLIENT             |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_BANK_EMPLOYEE_CLIENT   | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_BANK_EMPLOYEE_CLIENT   |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CONSULTANT_CLIENT  | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CONSULTANT_CLIENT  |
| CLIENT_SECRETS_SERVICE_DEPARTMENT_ODOO_CLIENT            | external-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_DEPARTMENT_ODOO_CLIENT            |
| CLIENT_SECRETS_SERVICE_KEYCLOAK_USER_MANAGER             | keycloak-keycloak-client-secrets        | CLIENT_SECRETS_SERVICE_KEYCLOAK_USER_MANAGER             |
| CLIENT_SECRETS_SERVICE_LOAN_ACCOUNT_REGISTRY             | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_ACCOUNT_REGISTRY             |
| CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_SECURED          | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_SECURED          |
| CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_UNSECURED        | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_UNSECURED        |
| CLIENT_SECRETS_SERVICE_LOAN_BUSINESS_CODEGEN             | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_BUSINESS_CODEGEN             |
| CLIENT_SECRETS_SERVICE_LOAN_CODELISTS_BRIDGE             | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_CODELISTS_BRIDGE             |
| CLIENT_SECRETS_SERVICE_LOAN_CONTRACTING                  | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_CONTRACTING                  |
| CLIENT_SECRETS_SERVICE_LOAN_CORE_BRIDGE                  | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_CORE_BRIDGE                  |
| CLIENT_SECRETS_SERVICE_LOAN_CUSTOMER_GATEWAY             | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_CUSTOMER_GATEWAY             |
| CLIENT_SECRETS_SERVICE_LOAN_DE_BRIDGE                    | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_DE_BRIDGE                    |
| CLIENT_SECRETS_SERVICE_LOAN_DOCUMENTS_BRIDGE             | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_DOCUMENTS_BRIDGE             |
| CLIENT_SECRETS_SERVICE_LOAN_MODELING_SECURED             | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_MODELING_SECURED             |
| CLIENT_SECRETS_SERVICE_LOAN_NOTIFICATION_BRIDGE          | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_NOTIFICATION_BRIDGE          |
| CLIENT_SECRETS_SERVICE_LOAN_PFS_BRIDGE                   | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_PFS_BRIDGE                   |
| CLIENT_SECRETS_SERVICE_LOAN_PID_BRIDGE                   | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_PID_BRIDGE                   |
| CLIENT_SECRETS_SERVICE_LOAN_PRODUCT_CATALOG_BRIDGE       | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_PRODUCT_CATALOG_BRIDGE       |
| CLIENT_SECRETS_SERVICE_LOAN_SIGN_BRIDGE                  | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_SIGN_BRIDGE                  |
| CLIENT_SECRETS_SERVICE_LOAN_TASK_QUEUE                   | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_TASK_QUEUE                   |
| CLIENT_SECRETS_SERVICE_LOAN_WHISPER_BRIDGE               | loan-keycloak-client-secrets            | CLIENT_SECRETS_SERVICE_LOAN_WHISPER_BRIDGE               |
| CLIENT_SECRETS_SERVICE_NOTIFICATION_EXPONEA_BRIDGE       | notification-keycloak-client-secrets    | CLIENT_SECRETS_SERVICE_NOTIFICATION_EXPONEA_BRIDGE       |
| CLIENT_SECRETS_SERVICE_NOTIFICATION_GATEWAY              | notification-keycloak-client-secrets    | CLIENT_SECRETS_SERVICE_NOTIFICATION_GATEWAY              |
| CLIENT_SECRETS_SERVICE_NOTIFICATION_PUSH_SERVICE         | notification-keycloak-client-secrets    | CLIENT_SECRETS_SERVICE_NOTIFICATION_PUSH_SERVICE         |
| CLIENT_SECRETS_SERVICE_NOTIFICATION_SMSGATE_TMOBILE      | notification-keycloak-client-secrets    | CLIENT_SECRETS_SERVICE_NOTIFICATION_SMSGATE_TMOBILE      |
| CLIENT_SECRETS_SERVICE_PFS_ACCEPTANCE                    | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PFS_ACCEPTANCE                    |
| CLIENT_SECRETS_SERVICE_PFS_ADVISOR_DEFINITION            | pfs-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PFS_ADVISOR_DEFINITION            |
| CLIENT_SECRETS_SERVICE_PFS_AUTH_MANAGER                  | pfs-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PFS_AUTH_MANAGER                  |
| CLIENT_SECRETS_SERVICE_PFS_CONTRACT_SHARING              | pfs-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PFS_CONTRACT_SHARING              |
| CLIENT_SECRETS_SERVICE_PFS_INSURANCE_COMPARATOR          | pfs-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PFS_INSURANCE_COMPARATOR          |
| CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO                     | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO                     |
| CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO_AUTH_MANAGER        | pfs-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO_AUTH_MANAGER        |
| CLIENT_SECRETS_SERVICE_NEBANKA_OCP                       | nebanka-keycloak-client-secrets         | CLIENT_SECRETS_SERVICE_NEBANKA_OCP                       |
| CLIENT_SECRETS_SERVICE_PID_ACTIVATION_BACKOFFICE_GATEWAY | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_ACTIVATION_BACKOFFICE_GATEWAY |
| CLIENT_SECRETS_SERVICE_PID_ACTIVATION_SERVICE            | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_ACTIVATION_SERVICE            |
| CLIENT_SECRETS_SERVICE_PID_AML                           | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_AML                           |
| CLIENT_SECRETS_SERVICE_PID_AUTH_MANAGER                  | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_AUTH_MANAGER                  |
| CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_EVENTS             | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_EVENTS             |
| CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_GATEWAY            | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_GATEWAY            |
| CLIENT_SECRETS_SERVICE_PID_BANKID_DATA                   | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_BANKID_DATA                   |
| CLIENT_SECRETS_SERVICE_PID_BANKID_OIDC_CONNECTOR         | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_BANKID_OIDC_CONNECTOR         |
| CLIENT_SECRETS_SERVICE_PID_BPM                           | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_BPM                           |
| CLIENT_SECRETS_SERVICE_PID_CORE                          | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_CORE                          |
| CLIENT_SECRETS_SERVICE_PID_EMAIL                         | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_EMAIL                         |
| CLIENT_SECRETS_SERVICE_PID_FED_AUTHENTICATION            | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_FED_AUTHENTICATION            |
| CLIENT_SECRETS_SERVICE_PID_GATEWAY                       | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_GATEWAY                       |
| CLIENT_SECRETS_SERVICE_PID_MOBILE_GATEWAY                | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_MOBILE_GATEWAY                |
| CLIENT_SECRETS_SERVICE_PID_NOTIFICATION_SERVICE          | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_NOTIFICATION_SERVICE          |
| CLIENT_SECRETS_SERVICE_PID_ONBOARD_AUTH_MANAGER          | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_ONBOARD_AUTH_MANAGER          |
| CLIENT_SECRETS_SERVICE_PID_ONBOARDING                    | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_ONBOARDING                    |
| CLIENT_SECRETS_SERVICE_PID_IDENTITY_BLOCKING             | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_IDENTITY_BLOCKING             |
| CLIENT_SECRETS_SERVICE_PID_DOCUMENT_SIGNER               | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_DOCUMENT_SIGNER               |
| CLIENT_SECRETS_SERVICE_PID_WORKFLOW                      | pid-keycloak-client-secrets             | CLIENT_SECRETS_SERVICE_PID_WORKFLOW                      |
| CLIENT_SECRETS_SERVICE_WSO2                              | api-gw-operator-keycloak-client-secrets | CLIENT_SECRETS_SERVICE_WSO2                              |
| CLIENT_SECRETS_WSO2_APIM_ONB                             | wso2am-onb-keycloak-client-secrets      | CLIENT_SECRETS_WSO2_APIM_ONB                             |
| CLIENT_SECRETS_WSO2_APIM_PFS                             | wso2am-pfs-keycloak-client-secrets      | CLIENT_SECRETS_WSO2_APIM_PFS                             |
| CLIENT_SECRETS_WSO2_APIM_PID                             | wso2am-pid-keycloak-client-secrets      | CLIENT_SECRETS_WSO2_APIM_PID                             |
| CLIENT_SECRETS_WSO2_APIM_SVC                             | wso2am-svc-keycloak-client-secrets      | CLIENT_SECRETS_WSO2_APIM_SVC                             |
| CLIENT_SECRETS_WSO2_APIM_GEPARD                          | wso2am-gep-keycloak-client-secrets      | CLIENT_SECRETS_WSO2_APIM_GEPARD                          |
| KEYCLOAK_ADMIN_PASSWORD                                  | keycloak-admin-secret                   | KEYCLOAK_ADMIN_PASSWORD                                  |
| LOKI_PASSWORD                                            | loki-user-secret                        | password                                                 |

### Example - Secret files

1. Kazdy secret file je potrebne vytvorit v namespace, kde bezi keycloak-migration.
   * Slouzi to jako zdroj pro client-secret v pri initializaci KC.
2. Pak je potreba kazdy secret file zkopirovat i do namespace podle prvniho slova v nazve secretu.
   * To je proto aby aplikace bezici v danem namespace si ziskala svuj client-secret pro spravne fungovani.

Default client-secret pro dev prostredi je `Replace me` v base64 `UmVwbGFjZSBtZQ==`. Pokud chceme v budoucnu zmenit/zrotovat client-secret je potreba,
aby se zmena udelela v obou kopiich a nasledne otocit pod, aby si nacetl spravny obsah secretu pri svem startu.

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: loan-keycloak-client-secrets
  namespace: keycloak
  uid: aef8a514-0dbe-4dca-91b9-7c141222c2f1
  resourceVersion: '377204667'
  creationTimestamp: '2023-01-10T14:55:32Z'
  managedFields:
    - manager: node-fetch
      operation: Update
      apiVersion: v1
      time: '2023-03-20T13:54:48Z'
      fieldsType: FieldsV1
      fieldsV1:
        f:data:
          .: {}
          f:CLIENT_SECRETS_COMPONENT_DECISION_ENGINE: {}
          f:CLIENT_SECRETS_FRONTEND_LOAN_APPLICATION: {}
          f:CLIENT_SECRETS_FRONTEND_LOAN_PROCESSES: {}
          f:CLIENT_SECRETS_SERVICE_APS_APPLICATION: {}
          f:CLIENT_SECRETS_SERVICE_APS_BFF: {}
          f:CLIENT_SECRETS_SERVICE_APS_DOCUMENT: {}
          f:CLIENT_SECRETS_SERVICE_APS_EXCEPTION: {}
          f:CLIENT_SECRETS_SERVICE_APS_GATE: {}
          f:CLIENT_SECRETS_SERVICE_APS_MODELING: {}
          f:CLIENT_SECRETS_SERVICE_APS_ORIGINATION: {}
          f:CLIENT_SECRETS_SERVICE_APS_LOAN: {}
          f:CLIENT_SECRETS_SERVICE_APS_PARAMETERS: {}
          f:CLIENT_SECRETS_SERVICE_APS_PEPZ: {}
          f:CLIENT_SECRETS_SERVICE_APS_PROCESSES: {}
          f:CLIENT_SECRETS_SERVICE_APS_VALUERS: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_CODELISTS_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_CORE_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_DE_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_DOCUMENTS_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_NOTIFICATION_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_CUSTOMER_GATEWAY: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_PFS_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_PID_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_PRODUCT_CATALOG_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_ACCOUNT_REGISTRY: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_BUSINESS_CODEGEN: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_SIGN_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_WHISPER_BRIDGE: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_UNSECURED: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_SECURED: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_MODELING_SECURED: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_CONTRACTING: {}
          f:CLIENT_SECRETS_SERVICE_LOAN_TASK_QUEUE: {}
        f:type: {}
  selfLink: /api/v1/namespaces/keycloak/secrets/loan-keycloak-client-secrets
type: Opaque
data:
  CLIENT_SECRETS_COMPONENT_DECISION_ENGINE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_FRONTEND_LOAN_APPLICATION: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_FRONTEND_LOAN_PROCESSES: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_APPLICATION: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_BFF: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_DOCUMENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_EXCEPTION: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_GATE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_MODELING: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_ORIGINATION: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_LOAN: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_PARAMETERS: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_PEPZ: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_PROCESSES: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_APS_VALUERS: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_CODELISTS_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_CORE_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_DE_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_DOCUMENTS_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_NOTIFICATION_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_CUSTOMER_GATEWAY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_PFS_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_PID_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_ACCOUNT_REGISTRY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_BUSINESS_CODEGEN: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_PRODUCT_CATALOG_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_SIGN_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_WHISPER_BRIDGE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_UNSECURED: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_APPLICATION_SECURED: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_MODELING_SECURED: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_CONTRACTING: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_LOAN_TASK_QUEUE: UmVwbGFjZSBtZQ==
```

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: keycloak-keycloak-client-secrets
  namespace: keycloak
  uid: 18fa3739-97e0-484a-bdc7-813ed33e89d4
  resourceVersion: '*********'
  creationTimestamp: '2022-10-24T13:52:52Z'
  managedFields:
    - manager: node-fetch
      operation: Update
      apiVersion: v1
      time: '2022-10-24T13:52:52Z'
      fieldsType: FieldsV1
      fieldsV1:
        f:data:
          .: {}
          f:CLIENT_SECRETS_MASTER_KEYCLOAK_USER_MANAGER: {}
          f:CLIENT_SECRETS_SERVICE_KEYCLOAK_USER_MANAGER: {}
        f:type: {}
  selfLink: /api/v1/namespaces/keycloak/secrets/keycloak-keycloak-client-secrets
type: Opaque
data:
  CLIENT_SECRETS_MASTER_KEYCLOAK_USER_MANAGER: ********************************
  CLIENT_SECRETS_SERVICE_KEYCLOAK_USER_MANAGER: ********************************
```

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: bank-keycloak-client-secrets
  namespace: keycloak
  uid: 2c4e0aed-e4dd-4f69-907c-8a209703eb1f
  resourceVersion: '*********'
  creationTimestamp: '2022-12-28T10:30:30Z'
  managedFields:
    - manager: node-fetch
      operation: Update
      apiVersion: v1
      time: '2023-03-15T15:21:13Z'
      fieldsType: FieldsV1
      fieldsV1:
        f:data:
          .: {}
          f:CLIENT_SECRETS_SERVICE_BANK_CHARGEBACK: {}
          f:CLIENT_SECRETS_SERVICE_BANK_CLIENT_DOCUMENTS: {}
          f:CLIENT_SECRETS_SERVICE_BANK_CLIENT: {}
          f:CLIENT_SECRETS_SERVICE_BANK_CODELISTS: {}
          f:CLIENT_SECRETS_SERVICE_BANK_PAYMENT: {}
          f:CLIENT_SECRETS_SERVICE_BANK_ACCOUNTS_REGISTRY: {}
          f:CLIENT_SECRETS_SERVICE_BANK_PFS_INTEGRATION: {}
          f:CLIENT_SECRETS_SERVICE_BANK_SERVICE_GATEWAY: {}
          f:CLIENT_SECRETS_SERVICE_BANK_DAILY_AUTH_MANAGER: {}
          f:CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING_GATEWAY: {}
          f:CLIENT_SECRETS_SERVICE_BANK_CHALLENGES: {}
          f:CLIENT_SECRETS_SERVICE_BANK_COOPERATION_SERVICE: {}
          f:CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING: {}
          f:CLIENT_SECRETS_SERVICE_BANK_CAMPAIGN: {}
          f:CLIENT_SECRETS_SERVICE_BANK_JIRA_ADAPTER: {}
          f:CLIENT_SECRETS_SERVICE_BANK_DASHBOARD: {}
          f:CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_BLOCKING: {}
          f:CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_TERMINATION: {}
          f:CLIENT_SECRETS_SERVICE_BANK_TEMPLATE_RENDERER: {}
          f:CLIENT_SECRETS_SERVICE_BANK_TEMPLATING: {}
        f:type: {}
  selfLink: /api/v1/namespaces/keycloak/secrets/bank-keycloak-client-secrets
type: Opaque
data:
  CLIENT_SECRETS_SERVICE_BANK_CHARGEBACK: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_CLIENT_DOCUMENTS: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_CODELISTS: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_PAYMENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_ACCOUNTS_REGISTRY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_PFS_INTEGRATION: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_SERVICE_GATEWAY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_DAILY_AUTH_MANAGER: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING_GATEWAY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_CHALLENGES: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_COOPERATION_SERVICE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_FIP_ONBOARDING: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_CAMPAIGN: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_JIRA_ADAPTER: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_DASHBOARD: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_BLOCKING: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_ACCOUNT_TERMINATION: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_TEMPLATE_RENDERER: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_BANK_TEMPLATING: UmVwbGFjZSBtZQ==
```

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: pfs-keycloak-client-secrets
  namespace: keycloak
  uid: fe928968-02de-4d9d-a6a7-38496b47e779
  resourceVersion: '*********'
  creationTimestamp: '2023-01-10T14:58:56Z'
  managedFields:
    - manager: node-fetch
      operation: Update
      apiVersion: v1
      time: '2023-02-21T12:28:22Z'
      fieldsType: FieldsV1
      fieldsV1:
        f:data:
          .: {}
          f:CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT: {}
          f:CLIENT_SECRETS_SERVICE_PFS_ADVISOR_DEFINITION: {}
          f:CLIENT_SECRETS_SERVICE_PFS_AUTH_MANAGER: {}
          f:CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO_AUTH_MANAGER: {}
          f:CLIENT_SECRETS_SERVICE_PFS_INSURANCE_COMPARATOR: {}
          f:CLIENT_SECRETS_SERVICE_PFS_CONTRACT_SHARING: {}
          f:CLIENT_SECRETS_SERVICE_NEBANKA_OCP: {}
        f:type: {}
  selfLink: /api/v1/namespaces/keycloak/secrets/pfs-keycloak-client-secrets
type: Opaque
data:
  CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PFS_ADVISOR_DEFINITION: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PFS_AUTH_MANAGER: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO_AUTH_MANAGER:  UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PFS_INSURANCE_COMPARATOR:  UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PFS_CONTRACT_SHARING: UmVwbGFjZSBtZQ== 
  CLIENT_SECRETS_SERVICE_NEBANKA_OCP:  UmVwbGFjZSBtZQ==
```

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: pid-keycloak-client-secrets
  namespace: keycloak
  uid: 12bb7617-d48a-4717-bc1e-4cc100670eaa
  resourceVersion: '*********'
  creationTimestamp: '2023-01-10T14:54:32Z'
  managedFields:
    - manager: node-fetch
      operation: Update
      apiVersion: v1
      time: '2023-01-10T14:54:32Z'
      fieldsType: FieldsV1
      fieldsV1:
        f:data:
          .: {}
          f:CLIENT_SECRETS_SERVICE_PID_AML: {}
          f:CLIENT_SECRETS_SERVICE_PID_AUTH_MANAGER: {}
          f:CLIENT_SECRETS_SERVICE_PID_ONBOARD_AUTH_MANAGER: {}
          f:CLIENT_SECRETS_SERVICE_PID_ONBOARDING: {}
          f:CLIENT_SECRETS_SERVICE_PID_BPM: {}
          f:CLIENT_SECRETS_SERVICE_PID_BANKID_DATA: {}
          f:CLIENT_SECRETS_SERVICE_PID_BANKID_OIDC_CONNECTOR: {}
          f:CLIENT_SECRETS_SERVICE_PID_FED_AUTHENTICATION: {}
          f:CLIENT_SECRETS_SERVICE_PID_MOBILE_GATEWAY: {}
          f:CLIENT_SECRETS_SERVICE_PID_ACTIVATION_SERVICE: {}
          f:CLIENT_SECRETS_SERVICE_PID_CORE: {}
          f:CLIENT_SECRETS_SERVICE_PID_EMAIL: {}
          f:CLIENT_SECRETS_SERVICE_PID_NOTIFICATION_SERVICE: {}
          f:CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_GATEWAY: {}
          f:CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO: {}
          f:CLIENT_SECRETS_SERVICE_PFS_ACCEPTANCE: {}
          f:CLIENT_SECRETS_SERVICE_PID_GATEWAY: {}
          f:CLIENT_SECRETS_SERVICE_PID_ACTIVATION_BACKOFFICE_GATEWAY: {}
          f:CLIENT_SECRETS_SERVICE_PID_IDENTITY_BLOCKING: {}
          f:CLIENT_SECRETS_SERVICE_STAMPING_SERVICE: {}
          f:CLIENT_SECRETS_SERVICE_PID_DOCUMENT_SIGNER: {}
          f:CLIENT_SECRETS_SERVICE_PID_WORKFLOW: {}
        f:type: {}
  selfLink: /api/v1/namespaces/keycloak/secrets/pid-keycloak-client-secrets
type: Opaque
data:
  CLIENT_SECRETS_SERVICE_PID_AML: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_AUTH_MANAGER: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_ONBOARD_AUTH_MANAGER: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_ONBOARDING: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_BPM: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_BANKID_DATA: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_BANKID_OIDC_CONNECTOR: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_FED_AUTHENTICATION: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_MOBILE_GATEWAY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_ACTIVATION_SERVICE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_CORE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_EMAIL: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_NOTIFICATION_SERVICE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_BACKOFFICE_GATEWAY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PFS_PORTFOLIO: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PFS_ACCEPTANCE: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_GATEWAY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_ACTIVATION_BACKOFFICE_GATEWAY: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_IDENTITY_BLOCKING: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_DOCUMENT_SIGNER: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_PID_WORKFLOW: UmVwbGFjZSBtZQ==
```

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: knbox-keycloak-client-secrets
  namespace: keycloak
  uid: 2816ec11-4bf9-4920-be59-14fe17fff3e1
  resourceVersion: '149790373'
  creationTimestamp: '2023-06-09T14:01:08Z'
  labels:
    k8slens-edit-resource-version: v1
  managedFields:
    - manager: node-fetch
      operation: Update
      apiVersion: v1
      time: '2023-06-09T14:02:47Z'
      fieldsType: FieldsV1
      fieldsV1:
        f:data:
          .: {}
          f:CLIENT_SECRETS_COMPONENT_KNBOX: {}
          f:CLIENT_SECRETS_FRONTEND_KNBOX: {}
        f:metadata:
          f:labels:
            .: {}
            f:k8slens-edit-resource-version: {}
        f:type: {}
  selfLink: /api/v1/namespaces/keycloak/secrets/knbox-keycloak-client-secrets
data:
  CLIENT_SECRETS_COMPONENT_KNBOX: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_FRONTEND_KNBOX: UmVwbGFjZSBtZQ==
type: Opaque
```


```yaml
apiVersion: v1
kind: Secret
metadata:
   name: service-aml-plugin-service
   namespace: keycloak
   uid: 101bccca-e7d6-4f42-9bec-2f11008539be9
   resourceVersion: '149790373'
   creationTimestamp: '2025-02-13T16:00:00Z'
   labels:
      k8slens-edit-resource-version: v1
   managedFields:
      - manager: node-fetch
        operation: Update
        apiVersion: v1
        time: '2025-02-13T16:00:00Z'
        fieldsType: FieldsV1
        fieldsV1:
           f:data:
              .: {}
              f:CLIENT_SECRETS_COMPONENT_AML_PLUGIN_SERVICE: {}
           f:metadata:
              f:labels:
                 .: {}
                 f:k8slens-edit-resource-version: {}
           f:type: {}
   selfLink: /api/v1/namespaces/keycloak/secrets/paf-keycloak-client-secrets
data:
   CLIENT_SECRETS_COMPONENT_AML_PLUGIN_SERVICE: UmVwbGFjZSBtZQ==
type: Opaque
```

```yaml
apiVersion: v1
kind: Secret
metadata:
   name: paf-keycloak-client-secrets
   namespace: keycloak
   uid: 909bccca-e7d6-4f42-9bec-2f11008539b3
   resourceVersion: '149790373'
   creationTimestamp: '2025-02-13T16:00:00Z'
   labels:
      k8slens-edit-resource-version: v1
   managedFields:
      - manager: node-fetch
        operation: Update
        apiVersion: v1
        time: '2025-02-13T16:00:00Z'
        fieldsType: FieldsV1
        fieldsV1:
           f:data:
              .: {}
              f:CLIENT_SECRETS_SERVICE_PAF: {}
              f:CLIENT_SECRETS_SERVICE_PID_PAF: {}
           f:metadata:
              f:labels:
                 .: {}
                 f:k8slens-edit-resource-version: {}
           f:type: {}
   selfLink: /api/v1/namespaces/keycloak/secrets/paf-keycloak-client-secrets
data:
   CLIENT_SECRETS_SERVICE_PAF: UmVwbGFjZSBtZQ==
   CLIENT_SECRETS_SERVICE_PID_PAF: UmVwbGFjZSBtZQ==
type: Opaque
```




```yaml
apiVersion: v1
kind: Secret
metadata:
  name: treasury-keycloak-client-secrets
  namespace: keycloak
data:
  CLIENT_SECRETS_FRONTEND_TREASURY: UmVwbGFjZSBtZQ==
type: Opaque
```

Secret for External clients in `services` realm
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: external-keycloak-client-secrets
  namespace: keycloak
type: Opaque
data:
  CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_ANAKIN_SK_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_FIP_SK_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_OCP_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_PID_TEST_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_RENTEA_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_SIMPLEA_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_GEPARD_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_VUB_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_WPB_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_BANK_EMPLOYEE_CLIENT: UmVwbGFjZSBtZQ==
  CLIENT_SECRETS_SERVICE_DEPARTMENT_PFS_CONSULTANT_CLIENT: UmVwbGFjZSBtZQ==
```

Secret for WSO2 key manager of `pid` realm
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: wso2am-pid-keycloak-client-secrets
  namespace: keycloak
data:
  CLIENT_SECRETS_WSO2_APIM_PID: UmVwbGFjZSBtZQ==
type: Opaque

```

Secret for WSO2 key manager of `services` realm
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: wso2am-svc-keycloak-client-secrets
  namespace: keycloak
data:
  CLIENT_SECRETS_WSO2_APIM_SVC: UmVwbGFjZSBtZQ==
type: Opaque
```

Secret for WSO2 key manager of `onboard` realm
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: wso2am-onb-keycloak-client-secrets
  namespace: keycloak
data:
  CLIENT_SECRETS_WSO2_APIM_ONB: UmVwbGFjZSBtZQ==
type: Opaque
```

Secret for WSO2 key manager of `pfs` realm
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: wso2am-pfs-keycloak-client-secrets
  namespace: keycloak
data:
  CLIENT_SECRETS_WSO2_APIM_PFS: UmVwbGFjZSBtZQ==
type: Opaque
```

Secret for UMA of `pid` realm
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: rs-pid-secret
  namespace: keycloak
data:
   CLIENT_SECRETS_RS_LOAN_APPLICATION: UmVwbGFjZSBtZQ==
   CLIENT_SECRETS_RS_LOAN_PROCESSES: UmVwbGFjZSBtZQ==
```

Secret for UMA of `pfs` realm
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: rs-pfs-secret
  namespace: keycloak
data:
   CLIENT_SECRETS_RS_LOAN_APPLICATION: UmVwbGFjZSBtZQ==
   CLIENT_SECRETS_RS_APS: UmVwbGFjZSBtZQ==
```

Secret for TARAN DM of `pid` realm
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: tarandm-keycloak-client-secrets
  namespace: keycloak
data:
   CLIENT_SECRETS_COMPONENT_DECISION_ENGINE_AML: UmVwbGFjZSBtZQ==
```
